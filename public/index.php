<?php

use Faker\Provider\Uuid;
use G7\Tracing\TraceContext;
use G7\Tracing\TracerBuilder;

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| First we need to get an application instance. This creates an instance
| of the application / container and bootstraps the application so it
| is ready to receive HTTP / Console requests from the environment.
|
*/
header("Access-Control-Allow-Origin: *");
header(
    "Access-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT"
);

$app = require __DIR__ . '/../bootstrap/app.php';

// 创建Tracer
TracerBuilder::create()
             ->setServiceName('foss-oil-adapter-api')
             ->setAgentHostPort(config('logging.jaeger_agent.schema'))
             ->build();

// 获取Tracer
$tracer = TraceContext::instance()->getTracer();
$tracer->setTag('host.name', gethostname());
$tracer->setTag('cluster.id', env('APP_CLUSTER_ID', ''));
$tracer->setTag('cluster.name', env('APP_CLUSTER_NAME', ''));
$tracer->setTag('span.kind', 'server');
// 创建Span
global $rootSpan;
$rootSpan = $tracer->startSpan(explode('?', $_SERVER['REQUEST_URI'])[0], microtime(true));
//生成链路ID
global $routeId;
$routeId = $rootSpan->getTraceId() ?? Uuid::uuid();

register_shutdown_function(function () use ($tracer, $rootSpan) {
    $rootSpan->finish();
    $tracer->flush();
});

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request
| through the kernel, and send the associated response back to
| the client's browser allowing them to enjoy the creative
| and wonderful application we have prepared for them.
|
*/

$app->run();
