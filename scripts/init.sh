#!/bin/sh

# 设置宿主机IPcom
AGENT__IP=$(curl -s http://rancher-metadata/2015-12-19/self/host/agent_ip)
sed -i "s/'agent_ip'  => ''/'agent_ip'  => '${AGENT__IP}'/g" /data/web/config/machine.php

DOCKER__IP=$(ifconfig | grep 'inet addr:' | grep -v '127.0.0.1' | cut -d: -f2 | awk '{ print $1}')
sed -i "s/'docker_ip' => ''/'docker_ip' => '${DOCKER__IP}'/g" /data/web/config/machine.php

# 安装composer 依赖
#cd /data/web || exit
#composer install --no-scripts --no-autoloader
#composer dump-autoload --optimize
