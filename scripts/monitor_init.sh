#!/bin/bash

#monitor 发布脚本

if [ "$APP__ENV" = "prod" ]; then
    fileName=monitor.linux.pro.last.tar.gz
else
    fileName=monitor.linux.test.last.tar.gz
fi

#log存放目录
LOGPATH=/data/logs/monitor
#用户组要与php用户一致
USER=nginx
GROUP=nginx
#sock路径（固定不可修改）
SOCK=/var/tmp/monitor.sock

#卸载
#supervisorctl stop monitor
rm -rf /data/logs/monitor

cd /data/web/monitor || exit
echo "解压文件"
cd /data/web/monitor && tar zxvf $fileName
if [ ! -d $LOGPATH ]; then
    mkdir -p $LOGPATH
fi
chown -R $USER:$GROUP $LOGPATH
if [ ! -f $SOCK ]; then
    touch $SOCK
fi

chown -R $USER:$GROUP $SOCK
echo "完成构建"

supervisorctl reload
