<?php

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Evit\PhpGmCrypto\Encryption\EvitSM4Encryption;
use Faker\Provider\Uuid;
use G7\Tracing\TraceContext;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Redis\RedisManager;
use Request\HBKJ as HBKJRequest;
use Symfony\Component\HttpFoundation\Response;
use Tool\Alarm\FeiShu;
use Tool\RsaToJava;

/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-03
 * Time: 18:33
 */

/**
 * @param $data
 * @param $secret
 * @param int $jsonRule
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019/9/6 11:40 上午
 */
function createSign($data, $secret, int $jsonRule = 512)
{
    $sign = $secret;
    ksort($data);

    foreach ($data as $key => $val) {
        if ($key != '' && !is_null($val)) {
            if (!is_string($val)) {
                $val = json_encode($val, $jsonRule);
            }

            $sign .= $key . $val;
        }
    }

    $sign .= $secret;
    return strtoupper(md5($sign));
}

function createSignForHLJH($data, $secret, $publicKey, $jsonRule = 512)
{
    $sign = $secret;
    ksort($data);

    foreach ($data as $key => $val) {
        if ($key != '' && !is_null($val)) {
            if (!is_string($val)) {
                $val = json_encode($val, $jsonRule);
            }

            $sign .= $key . $val;
        }
    }

    $sign .= $secret;
    return (new RsaToJava($publicKey))->publicEncrypt($sign);
}

/**
 * @param array $data
 * @param int $timestamp
 * @param string $method
 * @param string $appId
 * @param string $appKey
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019/9/6 2:20 下午
 */
function createSignForZy(array $data, int $timestamp, string $method, string $appId, string $appKey)
{
    $newData = [];
    ksort($data);

    foreach ($data as $k => $v) {
        $newData[] = "$k=$v";
    }

    $signStr = "$appKey$appId" . implode(',', $newData) . "$method$timestamp$appKey";
    return strtoupper(md5($signStr));
}

/**
 * 生成九鼎签名
 * @param $data
 * @param $secret
 * @param int $jsonRule
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-07-15 19:52
 */
function createSignForJd($data, $secret, int $jsonRule = 512)
{
    foreach ($data as &$val) {
        if (!is_string($val)) {
            $val = json_encode($val, $jsonRule);
        }
    }

    $sign = "{$secret}truck_app_key{$data['truck_app_key']}data{$data['data']}timestamp{$data['timestamp']}$secret";
    return strtoupper(md5($sign));
}

/**
 * 生成九鼎签名
 * @param $data
 * @param $secret
 * @param int $jsonRule
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-07-15 19:52
 */
function createSignForDD($data, $secret, int $jsonRule = 512)
{
    foreach ($data as &$val) {
        if (!is_string($val)) {
            $val = json_encode($val, $jsonRule);
        }
    }

    $sign = "{$data['app_key']}|$secret|{$data['timestamp']}|{$data['data']}";
    return strtolower(md5($sign));
}

/**
 * 生成车主帮的签名
 * @param $data
 * @param $secret
 * @param int $jsonRule
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-07-15 19:52
 */
function createSignForCzb($data, $secret, int $jsonRule = 512)
{
    $sign = $secret;
    ksort($data);

    foreach ($data as $key => $val) {
        if ($key != '' && !is_null($val)) {
            if (!is_string($val)) {
                $val = json_encode($val, $jsonRule);
            }

            $sign .= $key . $val;
        }
    }

    $sign .= $secret;
    return md5($sign);
}

/**
 * curl函数
 * ---------------------------------------------
 * @param string $url
 * @param mixed $data
 * @param int $timeout
 * @param array $header
 * @param string $requestMethod
 * @param bool $jsonRequest
 * @param int $jsonRule
 * @return mixed
 * ---------------------------------------------
 * <AUTHOR> yuzhihao <<EMAIL>>
 */
function curl(
    string $url,
    $data,
    int $timeout = 10,
    array $header = [],
    string $requestMethod = 'post',
    bool $jsonRequest = false,
    int $jsonRule = 512
) {
    if ($jsonRequest and $requestMethod == 'post') {
        $data = json_encode($data, $jsonRule);
    } else {
        $data = http_build_query($data);
    }

    $ch = curl_init();

    if ($requestMethod == 'get') {
        $url = $url . "?" . $data;
    } else {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }


    if (false !== strpos($url, 'https://')) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);  // 跳过证书检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);  // 从证书中检查SSL加密算法是否存在
    }

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

    if ($header) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    }

    $output = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($responseData = json_decode($output, true)) {
        $responseData['responseHttpCode'] = $httpCode;
        return $responseData;
    }

    return ['responseRawData' => $output, 'responseHttpCode' => $httpCode];
}

/**
 * 通过指定的字段名转换二维数组为指定字段对源数据的映射字典
 * @param array $arr
 * @param null $key
 * @return array
 */
function convertListToDict(array $arr = [], $key = null): array
{
    if (empty($key) || empty($arr)):

        return [];
    endif;

    $newArr = [];

    foreach ($arr as $v):

        if ($v instanceof stdClass):

            $v = get_object_vars($v);
        endif;

        if (is_array($key)):

            $realKey = '';

            foreach ($key as $rk) :

                $realKey .= $v[$rk];
            endforeach;
        else:

            $realKey = $v[$key];
        endif;

        $newArr[$realKey] = $v;
    endforeach;

    return $newArr;
}

/**
 * @param array $data
 * @param array $keys
 * @return array
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-01-14 14:16
 */
function array_columns(array $data, array $keys): array
{
    $returnData = [];

    foreach ($data as $v) {
        extract($v);

        if (array_has($v, $keys)) {
            $returnData[] = compact($keys);
        }
    }

    return $returnData;
}

/**
 * 返回数据格式化
 * @param int $code 返回代码
 * @param mixed $data 返回数据
 * @param bool $send 是否中断代码
 * @param string $customMsg 自定义消息内容
 * @return JsonResponse
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-01-07 11:42
 */
function responseFormat(int $code = 0, $data = null, bool $send = false, string $customMsg = ''): JsonResponse
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @throws Exception
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-01-21 15:35
 */
function createOilConfig()
{
    $realRunMode = strtoupper(getLaravelAndMachineEnv("RUN__ENVIRONMENT", ""));

    if (is_file(base_path("config/oil.php"))) {
        return;
    }

    @chmod(base_path("config/"), 0777);

    switch ($realRunMode) {
        case "DEV":
        case "DEVELOP":
        case "LOCAL":
            link(base_path("config/oil-dev.php"), base_path("config/oil.php"));
            link(base_path("public/bdt-dev.cfg"), base_path("public/bdt.cfg"));
            break;
        case "TEST":
            link(base_path("config/oil-test.php"), base_path("config/oil.php"));
            link(base_path("public/bdt-test.cfg"), base_path("public/bdt.cfg"));
            break;
        case "PROD":
        case "PRODUCT":
        case "":
        case "ONLINE":
            link(base_path("config/oil-prod.php"), base_path("config/oil.php"));
            link(base_path("public/bdt-prod.cfg"), base_path("public/bdt.cfg"));
            break;
        case "DEMO":
            link(base_path("config/oil-demo.php"), base_path("config/oil.php"));
            link(base_path("public/bdt-demo.cfg"), base_path("public/bdt.cfg"));
            break;
        default:
            throw new Exception("RUN__ENVIRONMENT parse failed.Can't set oil config");
    }
}

/**
 * 多维数组获取指定keys的键值
 * @param array $data 原始数据
 * @param array $keys 指定keys数组
 * @return array 指定keys的键值
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-02-20 16:32
 */
function array_columns_depth(array $data, array $keys)
{
    $iterArr = checkIsAssocArray($data) ? [$data] : $data;
    $values = [];

    foreach ($iterArr as $iterV) {
        if (checkIsAssocArray($iterV)) {
            if (array_has($iterV, $keys)) {
                $tempValues = array_columns([$iterV], $keys);
            } else {
                $tempValues = [];
            }
        } else {
            $tempValues = array_columns_depth($iterV, $keys);
        }

        $values = array_merge($tempValues, $values);
    }

    return $values;
}

/**
 * 判断是否是关联索引数组
 * @param mixed $data
 * @return bool
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-02-25 15:54
 */
function checkIsAssocArray($data): bool
{
    $flag = false;

    if (!is_array($data)) {
        return false;
    }

    if (array_keys($data) !== range(0, count($data) - 1)) {
        $flag = true;
    }

    return $flag;
}

/**
 * 从Laravel以及Machine获取环境变量
 * @param string $name 环境变量名称
 * @param $default null 默认值
 * @return array|false|mixed|string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-03-11 17:22
 */
function getLaravelAndMachineEnv(string $name, $default = null)
{
    $laravelName = str_replace("__", "_", $name);
    return empty(getenv($name)) ? (empty(env($laravelName)) ? $default : env($laravelName)) : getenv($name);
}

/**
 * @throws Exception
 */
function convertOtherCoordinateToGcJ02ByGdApi(array $coordinates, string $originalCoordinateType = 'baidu'): array
{
    $domain = AuthConfigData::getAuthConfigValByName("GD_COORDINATE_CONVERT_URL");
    $convertedCoordinates = [];
    $cacheCoordinatesData = [];
    $cacheCoordinatesMapping = dealHmGet(
        array_values($coordinates),
        app('redis')->hmget(
            "coordinate_converted_result_$originalCoordinateType",
            array_values($coordinates)
        )
    );
    foreach ($coordinates as $ck => $cv) {
        if (isset($cacheCoordinatesMapping[$cv])) {
            $convertedCoordinate = explode('|', $cacheCoordinatesMapping[$cv]);
            $convertedCoordinates[$cv] = [
                'lng' => $convertedCoordinate[0],
                'lat' => $convertedCoordinate[1],
            ];
            unset($coordinates[$ck]);
        }
    }
    $coordinatesCount = count($coordinates);
    $coordinatesTotalPage = ceil($coordinatesCount / 40);
    for ($p = 0; $p < $coordinatesTotalPage; $p++) {
        $currentCoordinates = array_slice($coordinates, $p * 40, 40);
        $convertedCoordinatesMapping = [];

        foreach ($currentCoordinates as $k => $v) {
            $convertedCoordinatesMapping[$v] = $k;
        }

        $requestParams = [
            'key'       => AuthConfigData::getAuthConfigValByName("GD_AK"),
            'locations' => implode('|', $currentCoordinates),
            'coordsys'  => $originalCoordinateType,
        ];
        Log::handle("Request gd started", "高德", "", [
            "requestParams" => $requestParams,
            "realUrl"       => $domain
        ], "", "info");
        $result = curl($domain, $requestParams, 60, [], 'get');
        Log::handle("Request gd finished", "高德", "", [
            "requestParams" => $requestParams,
            "realUrl"       => $domain
        ], $result, "info");

        if (isset($result["infocode"]) and $result["infocode"] != '10000' and in_array(
                $result['infocode'],
                array_keys(config('error.GD_PERMISSION_INVALID_CODE'))
            )) {
            (new FeiShu())->gdPermissionInvalid([
                'code' => $result['infocode'],
                'msg'  => config("error.GD_PERMISSION_INVALID_CODE.{$result["infocode"]}", $result['info']),
            ]);
        }

        if (!array_has($result, ["status", "locations"])) {
            throw new Exception('', 5000008);
        }

        if ($result["status"] != 1) {
            throw new Exception($result['info'], 5009999);
        }

        $gcJ02Coordinates = explode(";", $result["locations"]);
        foreach ($convertedCoordinatesMapping as $ccMk => $ccMv) {
            $convertedCoordinatesTemp = explode(",", $gcJ02Coordinates[$ccMv]);
            $convertedCoordinates[$ccMk] = [
                'lng' => $convertedCoordinatesTemp[0],
                'lat' => $convertedCoordinatesTemp[1],
            ];
            $cacheCoordinatesData[$ccMk] = $convertedCoordinatesTemp[0] . '|' . $convertedCoordinatesTemp[1];
        }
    }
    if ($cacheCoordinatesData) {
        app('redis')->hmset(
            "coordinate_converted_result_$originalCoordinateType",
            $cacheCoordinatesData
        );
    }
    return $convertedCoordinates;
}

/**
 * @param array $coordinates
 * @return array
 * @throws Throwable
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019/12/20 2:41 下午
 */
function getRegionForCoordinateByGdApi(array $coordinates)
{
    $domain = AuthConfigData::getAuthConfigValByName("GD_COORDINATE_REGION_URL");
    $convertedRegions = [];
    $cacheConvertedRegions = [];
    $cacheRegionsMapping = dealHmGet(
        array_values($coordinates),
        app('redis')->hmget(
            "coordinate_convert_region_result",
            array_values($coordinates)
        )
    );
    foreach ($coordinates as $ck => $cv) {
        if (isset($cacheRegionsMapping[$cv])) {
            $convertedRegions[$cv] = json_decode($cacheRegionsMapping[$cv], true);
            unset($coordinates[$ck]);
        }
    }
    $coordinatesCount = count($coordinates);
    $coordinatesTotalPage = ceil($coordinatesCount / 20);
    for ($p = 0; $p < $coordinatesTotalPage; $p++) {
        $currentCoordinates = array_slice($coordinates, $p * 20, 20);
        $convertedRegionsMapping = [];

        foreach ($currentCoordinates as $k => $v) {
            $convertedRegionsMapping[$v] = $k;
        }

        $requestParams = [
            'key'      => AuthConfigData::getAuthConfigValByName("GD_AK"),
            'location' => implode('|', $currentCoordinates),
            'output'   => 'json',
            'batch'    => 'true',
        ];
        Log::handle("Request gd started", "高德", "", [
            "requestParams" => $requestParams,
            "realUrl"       => $domain
        ], "", "info");
        $result = curl($domain, $requestParams, 60, [], 'get');
        Log::handle("Request gd finished", "高德", "", [
            "requestParams" => $requestParams,
            "realUrl"       => $domain
        ], $result, "info");

        if (isset($result["infocode"]) and $result["infocode"] != '10000' and in_array(
                $result['infocode'],
                array_keys(config('error.GD_PERMISSION_INVALID_CODE'))
            )) {
            (new FeiShu())->gdPermissionInvalid([
                'code' => $result['infocode'],
                'msg'  => config("error.GD_PERMISSION_INVALID_CODE.{$result["infocode"]}", $result['info']),
            ]);
        }

        if (!array_has($result, ["status"])) {
            throw new Exception('', 5000008);
        }

        if ($result["status"] != 1) {
            throw new Exception($result["info"], 5009999);
        }

        $regionInfos = [];

        foreach ($result['regeocodes'] as &$v) {
            $v['addressComponent']['province_n'] = str_replace('省', '', $v['addressComponent']['province']);
            $v['addressComponent']['province_n'] = str_replace('自治区', '', $v['addressComponent']['province_n']);
            $v['addressComponent']['province_n'] = str_replace('壮族', '', $v['addressComponent']['province_n']);
            $v['addressComponent']['province_n'] = str_replace('维吾尔', '', $v['addressComponent']['province_n']);
            $v['addressComponent']['province_n'] = str_replace('回族', '', $v['addressComponent']['province_n']);
            $v['addressComponent']['province_n'] = str_replace('市', '', $v['addressComponent']['province_n']);
            $v['addressComponent']['province_n'] = is_string(
                $v['addressComponent']['province_n']
            ) ? $v['addressComponent']['province_n'] : '';
            $regionInfos[] = $v['addressComponent']['province_n'];

            if (is_string($v['addressComponent']['city'])) {
                $v['addressComponent']['city_n'] = str_replace('市', '', $v['addressComponent']['city']);
            } else {
                $v['addressComponent']['city_n'] = str_replace('市', '', $v['addressComponent']['district']);
            }
            $v['addressComponent']['city_n'] = str_replace('区', '', $v['addressComponent']['city_n']);
            $v['addressComponent']['city_n'] = str_replace('县', '', $v['addressComponent']['city_n']);

            $v['addressComponent']['city_n'] = is_string(
                $v['addressComponent']['city_n']
            ) ? $v['addressComponent']['city_n'] : '';
            $regionInfos[] = $v['addressComponent']['city_n'];
        }

        $regionInfos = array_filter($regionInfos);
        $regionCodeMapping = RegionalInfoData::getCityCodeByNameFilterUnit($regionInfos, true);

        foreach ($convertedRegionsMapping as $ccMk => $ccMv) {
            if (!isset($result['regeocodes'][$ccMv])) {
                continue;
            }
            $provinceName = $result['regeocodes'][$ccMv]['addressComponent']['province_n'];
            $provinceCode = $regionCodeMapping["{$provinceName}2"]['city_code'] ?? '';
            $cityName = $result['regeocodes'][$ccMv]['addressComponent']['city_n'];

            if (isset($regionCodeMapping["{$cityName}3"]) and
                !empty($regionCodeMapping["{$cityName}3"]['city_code'])) {
                $cityCode = $regionCodeMapping["{$cityName}3"]['city_code'];
            } else {
                $cityCode = $regionCodeMapping["{$cityName}4"]['city_code'] ?? '';
            }

            $convertedRegions[$ccMk] = [
                'provinceName' => $provinceName,
                'cityName'     => $cityName,
                'provinceCode' => $provinceCode,
                'cityCode'     => $cityCode,
            ];
            $cacheConvertedRegions[$ccMk] = json_encode($convertedRegions[$ccMk]);
        }
    }
    if ($cacheConvertedRegions) {
        app('redis')->hmset(
            "coordinate_convert_region_result",
            $cacheConvertedRegions
        );
    }
    return $convertedRegions;
}

function getUniqueStringFor16()
{
    return substr(md5(Uuid::uuid()), 8, 16);
}

function checkIsMunicipality($cityCode = '')
{
    $checkCityCode = ['************', '110100000000', '120100000000', '************'];

    if (in_array($cityCode, $checkCityCode)) {
        return true;
    }

    return false;
}

function getMillisecond()
{
    list($t1, $t2) = explode(' ', microtime());
    return (int)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
}

function php2JavaArrayValuesToString(array $data)
{
    $returnStr = '[';
    $dataArr = [];

    foreach ($data as $v) {
        if ($v instanceof stdClass or is_array($v)) {
            $tempV = json_encode($v, 320);
        } elseif (is_string($v)) {
            $tempV = !empty($v) ? "$v" : '';
        } else {
            $tempV = $v;
        }

        $dataArr[] = $tempV;
    }

    $returnStr .= implode(', ', $dataArr);
    $returnStr .= ']';
    return $returnStr;
}

/**
 * @throws Exception
 */
function createXmlData(string $rootName, array $data)
{
    $xmlObj = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><' . $rootName . ' />');

    foreach ($data as $k => $v) {
        if (is_array($v) or is_object($v)) {
            $curNode = $xmlObj->addChild($k);
            recursionCreateXmlNode($v, $curNode);
            continue;
        }

        $xmlObj->addChild($k, $v);
    }

    return $xmlObj->asXML();
}

function recursionCreateXmlNode(
    array $data,
    SimpleXMLElement $rootNode = null,
    SimpleXMLElement $childNode = null,
    bool $isChild = false
): SimpleXMLElement {
    $curNode = $isChild ? ($childNode === null ? $rootNode : $childNode) : $rootNode;

    if ($curNode === null) {
        return new SimpleXMLElement("");
    }

    foreach ($data as $k => $v) {
        if (is_array($v)) {
            $childNode = $curNode->addChild($k);
            recursionCreateXmlNode($v, $rootNode, $childNode, true);
        } elseif (is_object($v)) {
            $childNode = $curNode->addChild($k);
            recursionCreateXmlNode(get_object_vars($v), $rootNode, $childNode, true);
        } else {
            $curNode->addChild($k, $v);
        }
    }

    return $rootNode;
}

if (!function_exists('url_safe_base64_encode')) {
    function url_safe_base64_encode($data)
    {
        return str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($data));
    }
}

if (!function_exists("config_path")) {
    function config_path()
    {
        return base_path("config/");
    }
}

if (!function_exists('url_safe_base64_decode')) {
    function url_safe_base64_decode($data)
    {
        $base_64 = str_replace(array('-', '_'), array('+', '/'), $data);
        return base64_decode($base_64);
    }
}

function getPemFromJavaRsa(string $publicKey): string
{
    $pem = chunk_split($publicKey, 64);
    return "-----BEGIN PUBLIC KEY-----\n" . $pem . "-----END PUBLIC KEY-----\n";
}

function dealHmGet(array $hFields, array $data): array
{
    $returnData = [];
    foreach ($hFields as $k => $v) {
        $returnData[$v] = $data[$k];
    }
    return $returnData;
}

/**
 * 随机生成手机号
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019-08-02 18:58
 */
function randomPhone()
{
    $twoArr = [3, 4, 5, 6, 7, 8,];
    $tArr = array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);
    $ntArr = array(0, 1, 2, 3, 5, 6, 7, 8, 9);
    $phone[0] = 1;

    for ($j = 1; $j < 11; $j++) {
        if ($j == 1) {
            $t = rand(0, 2);
            $phone[$j] = $twoArr[$t];
        } elseif ($j == 2 && $phone[1] != 3) {
            $th = rand(0, 8);
            $phone[$j] = $ntArr[$th];
        } else {
            $th = rand(0, 9);
            $phone[$j] = $tArr[$th];
        }
    }

    return implode('', $phone);
}

function getCurFirstDayOfWeek(string $format = 'Y-m-d H:i:s')
{
    $w = date('w', time());
    return date($format, strtotime("-" . ($w ? $w - 1 : 6) . ' days'));
}

function getSevenDaysAgoOfCurDay(string $format = 'Y-m-d H:i:s')
{
    return date($format, strtotime("-7 days"));
}

/**
 * 通过参数渲染指定消息模版
 * @param array $template
 * @param array $options
 * @return string
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2019/9/4 10:05 下午
 */
function renderMessageTemplate(array $template, array $options): string
{
    foreach ($options as $k => $v) {
        foreach ($template as &$t) {
            $v = is_bool($v) ? (string)$v : $v;
            if (!is_object($v) and !is_array($v)) {
                $t = str_replace("{" . $k . "}", $v, $t);
            }
        }
    }

    return implode("\n", $template);
}

function generateQrCode(string $text, string $key)
{
    $ymd = time();
    $string = $text . '|' . $ymd;
    $md5str = preg_replace('|[0-9/]+|', '', md5($key));
    $key = substr($md5str, 0, 2);
    $textLen = strlen($string);
    $rand_key = md5($key);
    $resultStr = "";

    for ($i = 0; $i < $textLen; $i++) {
        $resultStr .= $string[$i] ^ $rand_key[$i % 32];
    }

    $resultStr = trim(base64_encode($resultStr), "=");
    return $key . substr(md5($resultStr), 0, 3) . $resultStr;
}

function parseQrCode(string $encode)
{
    $key = substr($encode, 0, 2);
    $tex = substr($encode, 2);
    $verity_str = substr($tex, 0, 3);
    $tex = substr($tex, 3);

    if ($verity_str != substr(md5($tex), 0, 3)) {
        //完整性验证失败
        return '';
    }

    $tex = base64_decode($tex);
    $textLen = strlen($tex);
    $resultStr = "";
    $rand_key = md5($key);

    for ($i = 0; $i < $textLen; $i++) {
        $resultStr .= $tex[$i] ^ $rand_key[$i % 32];
    }

    $de_arr = explode('|', $resultStr);

    return $de_arr[0];
}

/**
 * 判断变量值是否为空并赋值默认值
 * @param $checkValue
 * @param $default
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2020/1/9 4:51 下午
 */
function checkAndAssignmentEmptyAndNull(&$checkValue, $default)
{
    if (empty($checkValue) or $checkValue == 'null') {
        $checkValue = $default;
    }
}

function padZeroForLength(string $text, int $length): string
{
    $padLength = $length - strlen($text);

    $text .= str_repeat('0', $padLength);

    return $text;
}

function updateLngOrLat(string $text): string
{
    if (empty($text)) {
        return '';
    }

    $dealText = explode('.', $text);
    $dealText[1] = padZeroForLength($dealText[1] ?? '', 6);
    return $dealText[0] . '.' . substr($dealText[1], 0, 6);
}


/**
 * 格式化sql语句
 * @param mixed $sqlArr
 * @return array
 * ---------------------------------------------------
 * <AUTHOR> <<EMAIL>>
 * @since 2020/6/23 8:20 下午
 */
function getRealQuerySql($sqlArr = []): array
{
    $returnSqlArr = [];

    if (is_array($sqlArr)) {
        foreach ($sqlArr as $v) {
            if (isset($v['query']) and isset($v['bindings'])) {
                $returnSqlArr[] = sprintf(str_replace('?', "'%s'", $v['query']), ...$v['bindings']);
            }
        }
    }

    return $returnSqlArr;
}

/**
 * 数字保留0(小于10的数时)
 * @param $number
 * @return string
 * @throws Exception
 * <AUTHOR> <<EMAIL>>
 * @since 2020/10/21 11:17 上午
 */
function getZeroPrefixNumber($number)
{
    if (!is_numeric($number)) {
        throw new Exception("Argument for first isn't a number");
    }

    if ($number < 10) {
        $number = "0$number";
    } else {
        $number = (string)$number;
    }

    return $number;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForWjy(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'code'    => $code,
        'result'  => $data,
        'message' => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $code
 * @param array|null|stdClass $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForSaic(int $code = 0, $data = [], bool $send = false, string $customMsg = ''): JsonResponse
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg,
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForGb(int $code = 0, ?array $data = [], bool $send = false, string $customMsg = ''): JsonResponse
{
    $responseArgs = [
        'resultCode' => $code,
        'resultData' => $data,
        'resultMsg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForCnpc(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'status'    => $code != 0 ? -1 : 0,
        'result'    => $code != 0 ? 'error' : 'success',
        'errorCode' => $code,
        'data'      => $data,
        'info'      => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function parseResponseHeader(string $responseHeader): array
{
    $responseHeaders = explode("\r\n", $responseHeader);
    $returnData = [];
    foreach ($responseHeaders as $v) {
        $temp = explode(":", trim($v));
        if (empty($temp[0]) or count($temp) < 2) {
            continue;
        }
        $returnData[$temp[0]] = trim($temp[1]);
    }
    return $returnData;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return Response
 * <AUTHOR> <<EMAIL>>
 * @since 2021/11/4 11:35 上午
 */
function responseFormatForDt(int $code = 0, ?array $data = [], bool $send = false, string $customMsg = ''): Response
{
    $responseArgs = [
        'code'    => $code,
        'data'    => $data,
        'message' => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForYgy(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'code'   => $code,
        'result' => $data,
        'msg'    => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @throws Exception
 */
function releaseRedisLock($redis, $lockKey, $lockValue): bool
{
    if (!($redis instanceof Predis\Client or $redis instanceof RedisManager)) {
        throw new Exception("The redis instance is invalid");
    }
    return (bool)$redis->eval(
        "if redis.call('get', KEYS[1]) == ARGV[1] then redis.call('del', KEYS[1])
    return 1 else return 0 end",
        1,
        $lockKey,
        $lockValue
    );
}

function responseFormatForJHCX(int $code = 1, ?array $data = [], bool $send = false, string $customMsg = ''): Response
{
    $responseArgs = array_merge([
        'result' => $code != 1 ? 0 : 1,
        'error'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ], $data ?? []);
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $status
 * @param array|null $info
 * @param bool $send
 * @param string $customMsg
 * @param string $actionType
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/11/4 11:35 上午
 */
function responseFormatForHbkj(
    int $status = 0,
    ?array $info = [],
    bool $send = false,
    string $customMsg = '',
    string $actionType = ''
): Response {
    $merchantKey = AuthConfigData::getAuthConfigValByName('HBKJ_MERCHANT_KEY');
    $data['status'] = $status == 0 ? 1 : 2;
    $data['info'] = (!$customMsg or $customMsg == '') ? config("error.$status") : $customMsg;
    $signResult = HBKJRequest::sign($data, $merchantKey);
    $responseArgs = [
        'postMessage' => [
            'head' => [
                'verifyCode'  => $signResult['verifyCode'],
                'requestType' => $actionType,
            ],
            'body' => $signResult['body'],
        ],
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle([
        'responseDataEntry' => [
            'status' => $status == 0 ? 1 : 2,
            'info'   => $info,
            'msg'    => (!$customMsg or $customMsg == '') ? config("error.$status") : $customMsg
        ],
        'responseData'      => $responseArgs
    ]);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param int $code
 * @param array|null $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse
 * <AUTHOR> <<EMAIL>>
 * @since 2021/1/18 3:40 下午
 */
function responseFormatForSqzl(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'code'      => $code == 0 ? 200 : $code,
        'result'    => $data,
        'resultMsg' => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function transJavaRsaKeyToPhpOpenSSL($content, $isPublic = true)
{
    if ($content) {
        if ($isPublic) {
            return "-----BEGIN PUBLIC KEY-----\n" . trim(chunk_split($content, 64)) . "\n-----END PUBLIC KEY-----\n";
        }

        return "-----BEGIN PRIVATE KEY-----\n" . trim(chunk_split($content, 64)) . "\n-----END PRIVATE KEY-----\n";
    }
    return false;
}

function responseFormatForZhyk(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'code' => $code == 0 ? 100 : $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @param bool $curlFormat 是否以curl header模式返回，即a: b
 * @return array
 */
function getTraceInjectHeaders(bool $curlFormat = false): array
{
    if (php_sapi_name() === 'cli' || php_sapi_name() === 'phpdbg') {
        return [];
    }
    $injectHeaders = TraceContext::instance()->getTracer()->getInjectHeaders();
    if ($curlFormat) {
        return array_map(function ($v, $k) {
            return "$k: $v";
        }, $injectHeaders, array_keys($injectHeaders));
    }
    return $injectHeaders;
}

function responseFormatForZdc(
    $code,
    ?array $data = [],
    string $customMsg = '',
    bool $send = false,
    int $format = 0
): JsonResponse {
    $customMsg = config("error.$code", $customMsg);
    if ($format == 0) {
        $responseArgs = [
            'code'    => (int)$code,
            'message' => $customMsg,
            'result'  => $data,
        ];
    } elseif ($format == 1) {
        $responseArgs = array_merge([
            'respCode' => $code,
            'respMsg'  => $customMsg,
        ], $data);
    } elseif ($format == 2) {
        $responseArgs = array_merge([
            'respCode' => $code,
            'respMsg'  => $customMsg,
            'code'     => (int)$code,
            'message'  => $customMsg,
            'result'   => $data,
        ], $data);
    } else {
        $responseArgs = [
            'code'    => (int)$code,
            'message' => $customMsg,
            'result'  => $data,
        ];
    }
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

/**
 * @throws Exception
 */
function responseFormatForAd(int $code = 0, $data = null, bool $send = false, string $customMsg = '')
{
    $appSecret = AuthConfigData::getAuthConfigValByName("AD_APP_SECRET");
    $dataEncKey = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_KEY");
    $dataEncIv = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_IV");
    $responseData = [
        "appId"       => AuthConfigData::getAuthConfigValByName("AD_APP_KEY"),
        "timestamp"   => time(),
        "encryptData" => base64_encode(
            (new EvitSM4Encryption([
                "key"  => $dataEncKey,
                "iv"   => $dataEncIv,
                "mode" => "cbc",
            ]))->encrypt(
                json_encode(
                    array_merge($data ?? [], [
                        'code'    => $code,
                        'message' => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg,
                    ])
                )
            )
        )
    ];
    $responseData['digest'] = base64_encode(
        md5(
            urlencode(
                $responseData['encryptData'] . $responseData['timestamp'] . $appSecret
            ),
            true
        )
    );
    $response = response()->json($responseData);
    ResponseLog::handle([
        'responseArgs' => func_get_args(),
        'responseData' => $responseData
    ]);
    if ($send) {
        $response->send();
        exit();
    }
    return $response;
}

function responseFormatForZwl(
    int $code = 0,
    ?array $data = [],
    bool $send = false,
    string $customMsg = ''
): JsonResponse {
    $responseArgs = [
        'error_code' => $code,
        'result'     => $data ?? new stdClass(),
        'message'    => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForXyn(int $code = 0, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'code' => $code == 0 ? 20000 : $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForSh(
    int $code = 0,
    ?array $data = [],
    string $customMsg = ''
): JsonResponse {
    $data['status'] = $code == 0 ? 'success' : 'fail';
    $data['code'] = $code;
    $data['msg'] = (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg;
    $response = response()->json($data);
    ResponseLog::handle($data);
    $response->send();
    exit();
}

function responseFormatForJt(int $code = 200, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForJH(int $code = 200, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForQK(int $code = 200, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForJTX(int $code = 200, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'code' => $code,
        'data' => $data,
        'msg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForXmsk(int $code = 0, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'status'  => $code,
        'data'    => $data,
        'message' => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }
    return $response;
}

/**
 * @param int $code
 * @param array $data
 * @param bool $send
 * @param string $customMsg
 * @return JsonResponse|void
 */
function responseFormatForDh(int $code = 1, array $data = [], bool $send = false, string $customMsg = '')
{
    $response = response()->json(array_merge([
        'respCode' => $code,
        'msg'      => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ], $data));
    ResponseLog::handle(func_get_args());
    if ($send) {
        $response->send();
        exit();
    }
    return $response;
}

function responseFormatForYUNDATONG(
    int $code = 200,
    ?array $data = [],
    string $customMsg = ''
): JsonResponse {
    $data['code'] = $code;
    $data['message'] = (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg;
    $response = response()->json($data);
    ResponseLog::handle($data);
    $response->send();
    exit();
}

function responseFormatForGaoDeng(
    int $code = 0,
    ?array $data = [],
    string $customMsg = ''
): JsonResponse {
    $data['respCode'] = $code == 0 ? '00000' : $code;
    $data['respMsg'] = (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg;
    $data['bizCode'] = $code == 0 ? 'SUCCESS' : "FAIL";
    $data['bizMsg'] = (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg;
    $response = response()->json($data);
    ResponseLog::handle($data);
    $response->send();
    exit();
}

function responseFormatForJq(
    $code,
    ?array $data = [],
    string $customMsg = '',
    bool $send = false,
    int $format = 0
): JsonResponse {
    $customMsg = config("error.$code", $customMsg);
    if ($format == 0) {
        $responseArgs = [
            'code'    => (int)$code,
            'message' => $customMsg,
            'result'  => $data,
        ];
    } elseif ($format == 1) {
        $responseArgs = array_merge([
            'respCode' => $code,
            'respMsg'  => $customMsg,
        ], $data);
    } elseif ($format == 2) {
        $responseArgs = array_merge([
            'respCode' => $code,
            'respMsg'  => $customMsg,
            'code'     => (int)$code,
            'message'  => $customMsg,
            'result'   => $data,
        ], $data);
    } else {
        $responseArgs = [
            'code'    => (int)$code,
            'message' => $customMsg,
            'result'  => $data,
        ];
    }
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);

    if ($send) {
        $response->send();
        exit();
    }

    return $response;
}

function responseFormatForFy(int $code = 0, $data = null, bool $send = false, string $customMsg = ''): JsonResponse
{
    $responseArgs = [
        'status' => [
            'code' => $code,
            'desc'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
        ],
        'data' => empty($data) ? null : (checkIsAssocArray($data) ? [$data] : $data),
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }
    return $response;
}

function responseFormatForZHUOYI(int $code = 200, $data = null, bool $send = false, string $customMsg = '')
{
    $responseArgs = [
        'respCode' => $code,
        'data'     => $data,
        'respMsg'  => (!$customMsg or $customMsg == '') ? config("error.$code") : $customMsg
    ];
    $response = response()->json($responseArgs);
    ResponseLog::handle($responseArgs);
    if ($send) {
        $response->send();
        exit();
    }
    return $response;
}

function getRouteActionName(Request $request)
{
    $route = $request->route()[1]['uses'];
    $realRoute = explode("\\", $route);
    $method = $realRoute[count($realRoute) - 1];
    $methodParse = explode("@", $method);
    return $methodParse[1];
}