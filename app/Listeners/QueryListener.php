<?php

namespace App\Listeners;


use DateTime;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\Log as LogFacade;
use Throwable;

class QueryListener
{
    public function handle(QueryExecuted $event)
    {
        try {
            $sql = str_replace("?", "'%s'", $event->sql);
            foreach ($event->bindings as $i => $binding) {
                if ($binding instanceof DateTime) {
                    $event->bindings[$i] = $binding->format('\'Y-m-d H:i:s\'');
                } elseif (is_string($binding)) {
                    $event->bindings[$i] = "$binding";
                }
            }
            global $rootSpan;
            if ($rootSpan) {
                $rootSpan->log([
                    'sql'  => vsprintf($sql, $event->bindings),
                    'cost' => $event->time . 'ms',
                ]);
            }
        } catch (Throwable $throwable) {
            LogFacade::error("Log sql failed", [
                'sql'       => $event->sql,
                'bindings'  => $event->bindings,
                'exception' => $throwable,
            ]);
        }
    }
}
