<?php

namespace App\Exceptions;

use App\Models\Data\Log\ResponseLog;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Lara<PERSON>\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    private $responseCallback = [];

    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        ValidationException::class,
    ];

    public function __construct()
    {
        $this->loadResponseCallback();
    }

    public function loadResponseCallback()
    {
        $this->responseCallback['fy'] = function ($responseCode, $responseMsg = '') {
            return responseFormatForFy($responseCode, null, true, $responseMsg);
        };
    }

    /**
     * @param Exception $e
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-01-08 15:04
     */
    public function report(Exception $e)
    {
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->logException($e);
        }
        parent::report($e);
    }

    /**
     * 修改错误信息渲染方式
     *
     * @param Request $request
     * @param Exception $e
     * @return JsonResponse
     */
    public function render($request, Exception $e)
    {
        $msg = null;

        if ($e instanceof ModelNotFoundException) {
            $code = 5000001;
            ResponseLog::handle([
                'exception' => $e
            ]);
        } elseif ($e instanceof AuthorizationException) {
            $code = 4030001;
            ResponseLog::handle([
                'exception' => $e
            ]);
        } elseif ($e instanceof ValidationException) {
            $code = 4120001;
            ResponseLog::handle([
                'exception' => $e
            ]);
        } elseif ($e instanceof NotFoundHttpException) {
            $code = 4040001;
        } elseif ($e instanceof MethodNotAllowedHttpException) {
            $code = 4050001;
        } else {
            if ($e->getCode() > 3000000) {
                $code = $e->getCode();
                $msg = $e->getMessage();
            } else {
                $code = 5000001;
                $msg = config('error.5000001');
            }
            ResponseLog::handle([
                'exception' => $e
            ]);
        }
        if (isset(
            $this->responseCallback[$request->input(
                'auth_data.name_abbreviation',
                ''
            )]
        )) {
            return $this->responseCallback[$request->input(
                'auth_data.name_abbreviation',
                ''
            )](
                $code,
                empty($msg) ? config("error.$code") : $msg
            );
        }
        return response()->json([
            'code' => $code,
            'msg'  => empty($msg) ? config("error.$code") : $msg,
            'data' => null,
        ]);
    }
}
