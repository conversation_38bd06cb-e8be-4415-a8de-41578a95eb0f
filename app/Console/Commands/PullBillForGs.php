<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\Mail;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Request\GS as GSRequest;
use Throwable;

class PullBillForGs extends PullBill
{
    protected $nameAbbreviation = 'gs';
    protected $statusMapping = [
        '1' => '支付成功',
        '2' => '支付失败',
    ];

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:gs {--billDate=}';
    protected $name = 'pull bill for gs';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill for gs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/9 5:05 下午
     */
    public function handle()
    {
        $billDate = $this->option("billDate");
        if (empty($billDate)) {

            $billDate = date("Ymd", strtotime("-1 day"));
        }

        $page = 1;
        $billData = [];
        try {

            do {

                $data = GSRequest::handle("ReportForm_Api/getDetail", [
                    "starttime" => "{$billDate}000000",
                    "endtime"   => "{$billDate}235959",
                    "page_num"  => $page,
                    "page_cnt"  => 100,
                ]);
                $totalPage = ceil($data['data_cnt'] / 100);
                array_push($billData, ...(json_decode($data['data'], true) ?? []));
                $page++;
            } while ($page <= $totalPage);
        } catch (Throwable $exception) {

            Log::handle('Failed to pull gs bill.', [
                'exception' => $exception,
            ], DockingPlatformInfoData::getPlatformNameByNameAbbreviation('gs'),
                'pull_bill', 'error');
            return;
        }
        $selfOrderIds = array_column($billData, 'order_no');
        $orderAssocData = convertListToDict(OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => 'in',
                'value'    => $selfOrderIds,
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->nameAbbreviation,
            ],
        ], [
            'self_order_id',
            'extend',
            'platform_order_id',
            'platform_order_status',
        ], false), 'self_order_id');
        foreach ($billData as &$v) {

            if (isset($v['order_no'])) {

                if (isset($orderAssocData[$v['order_no']])) {

                    $extend = json_decode($orderAssocData[$v['order_no']]['extend'], true);
                    $v['oil_title'] = $extend['owner']['oil_name'];
                    $v['self_order_id'] = $extend["owner"]["trades_no"] ?? '';
                    $v['card_no'] = $extend['owner']['vice_no'];
                    $v['station_name'] = $extend['owner']['trade_place'];
                    $v['write_off_desc'] = $orderAssocData[$v['order_no']]['platform_order_status'] == 1 ?
                        '核销成功' : '未核销';
                }

                $v['deduct_money'] = bcdiv($v['deduct_money'] ?? 0, 100, 2);
                $v['pay_money'] = bcdiv($v['pay_money'] ?? 0, 100, 2);
                $v['pay_realmoney'] = bcdiv($v['pay_realmoney'] ?? 0, 100, 2);
                $v['discount_money'] = bcdiv($v['discount_money'] ?? 0, 100, 2);
                $v['card_no'] = (string)$v['card_no'];
                $v['status'] = $this->statusMapping[$v['status']] ?? '';
            }
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $titCol = 'A';
        $title = [
            '交易时间'      => 'time',
            '加油站编码'     => 'point_no',
            '加油站名称'     => 'station_name',
            '油品'        => 'oil_title',
            '港森订单号'     => 'serial_no',
            'G7流水号'     => 'self_order_id',
            '支付状态'      => 'status',
            '核销状态'      => 'write_off_desc',
            '扣除G7总账户金额' => 'deduct_money',
            '司机应付金额'    => 'pay_money',
            '司机实付金额'    => 'pay_realmoney',
            '优惠金额'      => 'discount_money',
            '司机卡号'      => 'card_no',
        ];
        foreach ($title as $key => $value) {

            $sheet->getStyle($titCol . '1')->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical'   => Alignment::VERTICAL_CENTER,
                ],
            ]);
            $sheet->setCellValue($titCol . '1', $key);
            $titCol++;
        }
        $row = 2;
        foreach ($billData as $item) {
            $dataCol = 'A';

            foreach ($title as $value) {

                $sheet->setCellValue($dataCol . $row, $item[$value] ?? '');
                $sheet->getStyle($dataCol . $row)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);
                if (is_numeric($item[$value] ?? '')) {

                    if (strpos($item[$value], '.') !== false) {

                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    } else {

                        $sheet->getCell($dataCol . $row)->setValueExplicit($item[$value]
                            ?? '', DataType::TYPE_STRING);
                    }
                }
                if (in_array($value, [
                    'station_name',
                    'oil_title',
                    'status',
                    'write_off_desc'
                ])) {

                    $sheet->getColumnDimension($dataCol)
                          ->setWidth(mb_strlen($item[$value] ?? '') * 17 / 5);
                } else {

                    $sheet
                        ->getColumnDimension($dataCol)
                        ->setAutoSize(true);
                }

                $dataCol++;
            }

            $row++;
        }
        $filePath = resource_path("downloads/bill/" . $billDate . '港森账单.xlsx');
        $writer = IOFactory::createWriter($spreadsheet, "Xlsx");
        $writer->save($filePath);

        Mail::send("emails/bill/simple", [
            "billDate"     => $billDate,
            "tradeCount"   => count($billData),
            "tradeTotal"   => round(array_sum(array_column($billData, 'pay_realmoney'))),
            "platformName" => "港森",
            "remark"       => "此账单为港森通过接口提供，次日8:00发送。详细账单请见附件。",
        ], function ($message) use ($filePath, $billDate) {
            $message->subject("{$billDate}港森账单");
            $message->to(json_decode(AuthConfigData::getAuthConfigValByName("BILL_MAIL_RECEIVE"),
                true));
            $message->attach($filePath);
        });
        @unlink($filePath);
    }
}
