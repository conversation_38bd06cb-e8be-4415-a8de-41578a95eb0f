<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Dao\OrderPushRecord;
use App\Models\Data\Common;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use Illuminate\Console\Command;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class CheckPushOrderStatus extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'check-order-push-status';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'check-order-push-status.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $redis = app('redis');
        $lastCheckMaxId = $redis->get('check_order_push_status_last_max_id');
        $orderQuery = (new OrderPushRecord())->where(
            'push_time',
            '<=',
            date(
                'Y-m-d H:i:s',
                strtotime('-5 minute')
            )
        )->where('push_time', '>=', date('Y-m-d 00:00:00'))
        ->where('platform_name_abbreviation', '=', 'ad');
        if ($lastCheckMaxId) {
            $orderQuery->where('self_order_id', '>', $lastCheckMaxId);
        }
        $waitCheckOrderData = $orderQuery->get();
        if (!$waitCheckOrderData or $waitCheckOrderData->count() < 0) {
            return 0;
        }
        $orderInfos = convertListToDict(
            FOSS_ORDERRequest::handle(
                "/api/order/getBatchOrderItem",
                [
                    "order_ids" => $waitCheckOrderData->pluck('self_order_id')->toArray(),
                ]
            )["data"] ?? [],
            "order_id"
        );
        $alarmOrderInfos = [];
        foreach ($waitCheckOrderData->toArray() as $v) {
            if ($v['self_order_status'] != $orderInfos[$v['self_order_id']]['order_status']) {
                $alarmOrderInfos[] = $v;
            }
        }
        $platformInfos = convertListToDict(
            DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                array_column($alarmOrderInfos, 'platform_name_abbreviation')
            ),
            'name_abbreviation'
        );
        foreach ($alarmOrderInfos as $value) {
            (new FeiShu())->pushOrderStatusFailed([
                'platform_name'               => $platformInfos[$value['platform_name_abbreviation']]['platform_name'],
                'self_order_id'               => $value['self_order_id'],
                'platform_order_id'           => $value['platform_order_id'],
                'push_self_order_status_desc' => Common::G7_ORDER_STATUS_DESC[$value['self_order_status']],
                'self_order_status_desc'      => Common::G7_ORDER_STATUS_DESC[$orderInfos[$value['self_order_id']]['order_status']],
                'reason'                      => '推送时订单状态与当前订单状态不一致',
                'push_time'                   => $value['push_time'],
            ]);
        }
        $redis->set('check_order_push_status_last_max_id', max($waitCheckOrderData->pluck('self_order_id')->toArray()));
        return 0;
    }
}
