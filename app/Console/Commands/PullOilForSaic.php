<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Exception;
use Request\SAIC as SAICRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PullOilForSaic extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:saic {--supplier_identifier=}';
    protected $name      = 'pull oil for saic';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for saic
                                   -supplier_identifier: aj|fl|ajsw|yc.';

    protected $nameAbbreviation = 'saic';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle(array $parameters = [])
    {
        $supplierIdentifier = $parameters['supplier_identifier'] ?? '';
        if (empty($supplierIdentifier)) {
            $supplierIdentifier = $this->options()["supplier_identifier"] ?? '';
        }
        $thirdAuthInfo = json_decode(
                             AuthConfigData::getAuthConfigValByName(
                                 "SAIC_APP_AUTH_" .
                                 strtoupper($supplierIdentifier)
                             ),
                             true
                         ) ?? [];
        if (empty($thirdAuthInfo)) {
            throw new Exception("the supplier_identifier is invalid");
        }
        $authInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation . '_' .
            strtolower($supplierIdentifier)
        );
        $this->platformCode = $authInfo['role_code'] ?? '';
        $this->nameAbbreviation = $authInfo['name_abbreviation'];
        $page = 1;
        $totalPage = 0;
        $single = false;
        $existsStationIds = [];
        if (!$path = AuthConfigData::getAuthConfigValByName(
            "SAIC_" . strtolower($supplierIdentifier) .
            "_STATION_QUERY_API_PATH"
        )) {
            $path = AuthConfigData::getAuthConfigValByName("SAIC_STATION_QUERY_API_PATH");
        }

        do {
            try {
                if (!empty($parameters['station_id'])) {
                    $requestData = [
                        "merchantGasStationIds" => $parameters['station_id'],
                        "ps"                    => 100,
                        "pn"                    => $page,
                    ];
                    $single = true;
                } else {
                    $requestData = [
                        "ps" => 100,
                        "pn" => $page,
                    ];
                }
                $data = SAICRequest::handle($thirdAuthInfo, $path, $requestData);
                $totalPage = ceil($data['tc'] / 100);
                $regionCodes = [];
                $waitCoordinates = [];
                foreach ($data['datas'] as &$ov) {
                    $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                    $ov['cityCode'] = "{$ov['cityCode']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data['datas'] as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes) and !empty($v['provinceCode']) and
                                                                          !empty($v['cityCode'])) {
                        $waitCoordinates[] = "{$v["gdLng"]},{$v["gdLat"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['datas'] as $v) {
                    try {
                        //油站数据
                        $existsStationIds[] = $v['merchantGasStationId'];
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['merchantGasStationId'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['name'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;

                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["gdLng"]},{$v["gdLat"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["gdLng"]},{$v["gdLat"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['gdLng'];
                        $oilTemp['lat'] = $v['gdLat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = $v['status'] == 'online' ? 0 : 1;
                        $oilTemp['station_brand'] = $v['bizParams']['brandName'] == "中石油" ? 2 : 8;
                        $oilTemp['is_highway'] = $v['bizParams']['isHighspeed'] ? 1 : 0;
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = $v['status'] == 'online' ? 1 : 2;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['extends'] = [];
                        $stationOilPrice = [];
                        foreach ($v['bizParams']['fuels'] as $cv) {
                            if (!isset($stationOilPrice[$cv['fuelNo']])) {
                                $stationOilPrice[$cv['fuelNo']] = [];
                            }
                            $stationOilPrice[$cv['fuelNo']][] = $cv['price'];
                            $stationOilPrice[$cv['fuelNo']] = array_unique($stationOilPrice[$cv['fuelNo']]);
                        }
                        //油站价格数据
                        foreach ($v['bizParams']['fuels'] as $cv) {
                            $realOilInfo = explode("_", config("oil.oil_mapping.saic.{$cv['fuelNo']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "上汽", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$realOilInfo[1]] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = "";
                            if (isset($stationOilPrice[$cv['fuelNo']]) and count($stationOilPrice[$cv['fuelNo']]) > 1) {
                                if ($oilTemp['is_stop'] == 0) {
                                    (new FeiShu())->stationOilRepeat([
                                        'platformName' => '上汽',
                                        'stationName'  => $oilTemp['station_name'],
                                        'oilName' => $insertOilPriceTemp['oil_no'] .
                                                     $insertOilPriceTemp['oil_type'] .
                                                     $insertOilPriceTemp['oil_level'],
                                        'price'        => implode(',', $stationOilPrice[$cv['fuelNo']]),
                                    ]);
                                }
                                continue 2;
                            }
                            $oilPriceTemp['price'] = $cv['price'];
                            $oilPriceTemp['mac_price'] = 0.00;
                            $insertOilPriceTemp['gun_no'] = '1';
                            $insertOilPriceTemp['sale_price'] = bcmul($cv['price'], 100);
                            $insertOilPriceTemp['listing_price'] = bcmul($cv['originalPrice'], 100);
                            $insertOilPriceTemp['issue_price'] = bcmul($cv['guidePrice'], 100);
                            $oilTemp['price_list'][$cv['fuelNo']] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][$cv['fuelNo']] = $insertOilPriceTemp;
                            $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}";
                            $insertStationPriceData['extends'][$skuKey] = [
                                'id'       => $cv['id'],
                                'type'     => $cv['type'],
                                'fuelNo'   => $cv['fuelNo'],
                                'fuelName' => $cv['fuelName'],
                            ];
                        }
                        $oilTemp['price_list'] = array_values($oilTemp['price_list']);
                        $insertStationPriceData['oil_price_list'] = array_values(
                            $insertStationPriceData['oil_price_list']
                        );
                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                        if (empty($oilTemp['price_list'])) {
                            continue;
                        }
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['merchantGasStationId'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                            'station'   => $v,
                        ], "上汽", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "上汽", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        if (!$single) {
            $this->dealNotExistsStation($existsStationIds);
        }
    }
}
