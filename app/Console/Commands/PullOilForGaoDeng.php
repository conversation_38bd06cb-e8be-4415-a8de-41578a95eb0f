<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\GAODENG as GAODENGRequest;
use Throwable;


class PullOilForGaoDeng extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:gaodeng';
    protected $name      = 'pull oil for gaodeng';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for gaodeng';

    protected $nameAbbreviation = 'gaodeng';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {
            try {
                $data = GAODENGRequest::handle('firm/third/gateway/standard/queryStationInfo', [
                    'current' => $page,
                    'size'    => 1000,
                ])['respData'];
                $totalPage = ceil($data['total'] / 1000);

                $regionCodes = [];
                foreach ($data['records'] as &$ov) {
                    $ov['provinceId'] = "{$ov['provinceId']}000000";
                    $ov['cityId'] = "{$ov['cityId']}000000";
                    $regionCodes[] = $ov['provinceId'];
                    $regionCodes[] = $ov['cityId'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                $waitCoordinates = [];
                foreach ($data['records'] as $v) {
                    if (!in_array($v['provinceId'], $existsRegionCodes) or
                        !in_array($v['cityId'], $existsRegionCodes) and !empty($v['provinceId']) and
                                                                          !empty($v['cityId'])) {
                        $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
                    }
                }
                $regionData = [];
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['records'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['oilStationNo'];
                        $oilTemp['is_stop'] = $v['oilStationStatus'] - 1;
                        $oilTemp['assoc_id'] = $v['oilStationNo'];
                        $existsStationIds[] = $v['oilStationNo'];
                        $oilTemp['station_name'] = $v['oilStationName'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = config("station_type.gaodeng.{$v['businessType']}", 2);
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['station_brand'] = 8;
                        $oilTemp['station_oil_unit'] = $v['keepAccounts'];
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['business_hours'] = "24小时";
                        $oilTemp['contact'] = '高灯驿能';
                        $oilTemp['contact_phone'] = $v['contactMobile'] ?? '';

                        if (!in_array($v['provinceId'], $existsRegionCodes) or
                            !in_array($v['cityId'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceId'];
                            $oilTemp['city_code'] = $v['cityId'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }
                        if ($v['confirmMode'] != 1) {
                            $oilTemp['is_stop'] = 1;
                        }

                        $oilTemp['lng'] = $v['longitude'];
                        $oilTemp['lat'] = $v['latitude'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        if (isset($v['oilInfoList']) and is_array($v['oilInfoList'])) {
                            //油站价格数据
                            foreach ($v['oilInfoList'] as $cv) {
                                $realOilInfo = explode(
                                    "_",
                                    config(
                                        "oil.oil_mapping.gaodeng.oil_no.{$cv['oilNo']}"
                                    )
                                );
                                $oilPriceTemp = [];
                                $insertOilPriceTemp = [];
                                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                                if (empty($oilPriceTemp['oil_name'])) {
                                    continue;
                                }
                                $insertOilPriceTemp['oil_type'] = array_flip(
                                                                      config("oil.oil_type")
                                                                  )[$oilPriceTemp['oil_name']] ?? '';
                                $insertOilPriceTemp['oil_no'] = str_replace(
                                    $insertOilPriceTemp['oil_type'],
                                    '',
                                    array_flip(
                                        config("oil.oil_no")
                                    )[$oilPriceTemp['oil_type']] ?? ''
                                );
                                $insertOilPriceTemp['oil_level'] = array_flip(
                                                                       config("oil.oil_level")
                                                                   )[$oilPriceTemp['oil_level']] ?? '';

                                $oilPriceTemp['price'] = bcdiv($cv['oilPrice'], 100, 2);
                                $oilPriceTemp['mac_price'] = bcdiv($cv['oilPrice'], 100, 2);
                                $insertOilPriceTemp['gun_no'] = $cv['oilGunNoList'];
                                $insertOilPriceTemp['sale_price'] = $cv['oilPrice'];
                                $insertOilPriceTemp['listing_price'] = $cv['oilPrice'];
                                $oilTemp['price_list'][] = $oilPriceTemp;
                                $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            }

                            $insertStationPriceData['platform_code'] = $this->platformCode;
                        } else {
                            Log::handle("Oil's price data doesn't exist", [
                                "data" => $v
                            ], "高灯", "oil_station_data", "error");
                        }
                        if (empty($oilTemp['price_list'])) {
                            $oilTemp['is_stop'] = 1;
                        }
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        $insertStationPriceData['station_id'] = $v['oilStationNo'];
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "高灯", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "高灯", "oil_station_data", "error");

                if ($page == 1) {
                    throw $throwable;
                }
            }
        } while ($page <= $totalPage);

        $this->dealNotExistsStation($existsStationIds);
    }
}
