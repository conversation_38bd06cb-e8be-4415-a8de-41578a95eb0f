<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\YUNDATONG as YUNDATONGRequest;
use Throwable;


class PullOilForYunDaTong extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:yundatong';
    protected $name      = 'pull oil for yundatong';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for yundatong';

    protected $nameAbbreviation = 'yundatong';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle(array $parameters = [])
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];
        do {
            try {
                $requestData = [
                    "pageNo"   => $page,
                    "pageSize" => 100,
                ];
                $data = YUNDATONGRequest::handle('open/api/customer/station/list', $requestData)['data'];
                $totalPage = $data['totalPage'];
                $regionCodes = [];
                $waitCoordinates = [];
                foreach ($data['rows'] as &$ov) {
                    $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                    $ov['cityCode'] = "{$ov['cityCode']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data['rows'] as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes) and !empty($v['provinceCode']) and
                                                                          !empty($v['cityCode'])) {
                        $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['rows'] as $v) {
                    try {
                        //油站数据
                        $existsStationIds[] = $v['stationId'];
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['stationId'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['stationName'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = 2;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['business_hours'] = "24小时";
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['is_stop'] = $v['status'];
                        if ($v['verifyMode'] != 0) {
                            $oilTemp['is_stop'] = 1;
                        }
                        if ($v['payMode'] != 0) {
                            $oilTemp['is_stop'] = 1;
                        }

                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['lng'];
                        $oilTemp['lat'] = $v['lat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['station_brand'] = 8;
                        $oilTemp['contact'] = '运达通';
                        $oilTemp['contact_phone'] = $v['contactPhone'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        $insertStationPriceData['oil_price_list'] = [];
                        //油站价格数据
                        $stationOilPrice = [];
                        $oilGunNos = [];
                        foreach ($v['priceList'] as $cv) {
                            if (!isset($stationOilPrice[$cv['oilNo']])) {
                                $stationOilPrice[$cv['oilNo']] = [];
                                $oilGunNos[$cv['oilNo']] = [];
                            }
                            if (!empty($cv['gunList'])) {
                                array_push($oilGunNos[$cv['oilNo']], ...$cv['gunList']);
                            }
                            $stationOilPrice[$cv['oilNo']][] = $cv['price'] . '_' . $cv['priceGun'];
                            $stationOilPrice[$cv['oilNo']] = array_unique($stationOilPrice[$cv['oilNo']]);
                        }
                        foreach ($v['priceList'] as $cv) {
                            if (isset($stationOilPrice[$cv['oilNo']]) and count($stationOilPrice[$cv['oilNo']]) > 1) {
                                continue;
                            }
                            if (empty($oilGunNos[$cv['oilNo']])) {
                                continue;
                            }
                            $realOilInfo = explode("_", config("oil.oil_mapping.yundatong.{$cv['oilNo']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "运达通", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$realOilInfo[1]] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config('oil.oil_level')
                                                               )[$realOilInfo[2]] ?? '';
                            $oilPriceTemp['price'] =$cv['priceGun'];
                            $oilPriceTemp['mac_price'] = $cv['priceGun'];
                            $insertOilPriceTemp['gun_no'] = implode(',', $oilGunNos[$cv['oilNo']]);
                            $insertOilPriceTemp['sale_price'] = bcmul($cv['price'], 100);
                            $insertOilPriceTemp['listing_price'] = bcmul($cv['priceGun'], 100);
                            $insertOilPriceTemp['issue_price'] = bcmul($cv['priceOfficial'], 100);
                            $oilTemp['price_list'][$cv['oilNo']] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][$cv['oilNo']] = $insertOilPriceTemp;
                        }
                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        if (empty($oilTemp['price_list'])) {
                            $oilTemp['is_stop'] = 1;
                        }
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['stationId'];
                        $oilTemp['price_list'] = array_values($oilTemp['price_list']);
                        unset($insertStationPriceData['price_list']);
                        $insertStationPriceData['oil_price_list'] = array_values(
                            $insertStationPriceData['oil_price_list']
                        );
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                            'station'   => $v,
                        ], "运达通", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "运达通", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        $this->dealNotExistsStation($existsStationIds);
    }
}
