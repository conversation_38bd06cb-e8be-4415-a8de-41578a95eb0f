<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Console\Command;
use Request\DT as DTRequest;
use Throwable;

class ReleaseFrozenQuotaForDt extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'release-frozen-quota:dt {order_ids}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'release frozen quota for dt.
                                  -order_ids: order id.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $orderIds = explode('|', $this->argument("order_ids") ?? '');
        $orderData = convertListToDict(
            OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => 'in',
                    'value'    => $orderIds,
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => 'dt',
                ],
            ], ['self_order_id', 'platform_order_id', 'extend'], false),
            'self_order_id'
        );
        foreach ($orderIds as $v) {
            try {
                $extend = json_decode($orderData[$v]['extend'], true);
                $redis = app('redis');
                if (!$userInfo = json_decode(
                    $redis->hget("dt_user_mapping", $extend['owner']['drivertel']),
                    true
                )) {
                    $userInfo = DTRequest::handle("user/thirdUserRegister", [
                        'mobile'    => $extend['owner']['drivertel'],
                        'outUserId' => md5($extend['owner']['drivertel']),
                    ]);
                    $redis->hset("dt_user_mapping", $extend['owner']['drivertel'], json_encode($userInfo));
                }
                DTRequest::handle('authOrder/cancelAuthOrder', [
                    'orderId' => $orderData[$v]['platform_order_id'],
                    'userId'  => $userInfo['userId'],
                ]);
            } catch (Throwable $throwable) {
            }
        }
    }
}
