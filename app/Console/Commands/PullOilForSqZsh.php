<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Request\SQZSH as SQZSHRequest;
use Throwable;


class PullOilForSqZsh extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:sqZsh';
    protected $name      = 'pull oil for sqZsh';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for sqZsh.';

    protected $nameAbbreviation = 'sqZsh';

    protected $authData = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->authData = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        );
        $this->platformCode = $this->authData['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $data = SQZSHRequest::handle('/station/queryOilStation');

        $waitCoordinates = [];
        foreach ($data['data'] as $v) {
            $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
        }
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }

        $existsStationIds = [];
        $stopStations = [];
        foreach ($data['data'] as $v) {
            try {
                //油站数据
                $existsStationIds[] = $v['stationId'];
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['id'] = $v['stationId'];
                $oilTemp['assoc_id'] = $oilTemp['id'];
                $oilTemp['station_name'] = $v['stationName'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['business_hours'] = $v['openTime'] ?? '';

                $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['lng'] = $v['lng'];
                $oilTemp['lat'] = $v['lat'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                $oilTemp['station_type'] = 2;
                $oilTemp['trade_type'] = 3;
                $oilTemp['station_oil_unit'] = 1;
                $oilTemp['allow_switch_unit'] = 0;
                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = $v['status'] == 'online' ? 0 : 1;
                $oilTemp['station_brand'] = config("brand.$this->nameAbbreviation.{$v['type']}", 8);
                $oilTemp['is_highway'] = (int)($v['isHighspeed'] == 'B');
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = $v['status'] == 'online' ? 1 : 2;
                $insertStationPriceData['oil_price_list'] = [];
                $insertStationPriceData['extends'] = [];
                //油站价格数据
                foreach ($v['oilGunBeans'] as $cv) {
                    $realOilInfo = explode("_", config("oil.oil_mapping.$this->nameAbbreviation.{$cv['fuelNo']}"));
                    $oilPriceTemp = [];
                    $insertOilPriceTemp = [];
                    $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                    $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                    $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                    if (empty($oilPriceTemp['oil_name'])) {
                        Log::handle("Oil's attribute is invalid", [
                            "data" => $v
                        ], "上汽中石化", "oil_station_data", "error");
                        continue;
                    }
                    $insertOilPriceTemp['oil_type'] = array_flip(
                                                          config("oil.oil_type")
                                                      )[$oilPriceTemp['oil_name']] ?? '';
                    $insertOilPriceTemp['oil_no'] = str_replace(
                        $insertOilPriceTemp['oil_type'],
                        '',
                        array_flip(
                            config("oil.oil_no")
                        )[$oilPriceTemp['oil_type']] ?? ''
                    );
                    $insertOilPriceTemp['oil_level'] = array_flip(
                                                           config("oil.oil_level")
                                                       )[$oilPriceTemp['oil_level']] ?? '';

                    $oilPriceTemp['price'] = $cv['lvPrice'];
                    $oilPriceTemp['mac_price'] = $cv['lvPrice'];
                    $insertOilPriceTemp['sale_price'] = bcmul($cv['lvPrice'], 100);
                    $oilTemp['price_list'][$cv['fuelNo']] = $oilPriceTemp;
                    $insertStationPriceData['oil_price_list'][$cv['fuelNo']] = $insertOilPriceTemp;
                    $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}";
                    $insertStationPriceData['extends'][$skuKey] = $cv;
                }
                $oilTemp['price_list'] = array_values($oilTemp['price_list']);
                $insertStationPriceData['oil_price_list'] = array_values(
                    $insertStationPriceData['oil_price_list']
                );
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                if (empty($oilTemp['price_list'])) {
                    $stopStations[] = $v['stationId'];
                    continue;
                }
                $insertStationPriceData['station_id'] = $v['stationId'];
                StationPriceData::create($insertStationPriceData);
                BasicJob::pushStationToStationHub($oilTemp);
            } catch (Throwable $throwable) {
                Log::handle("The info of site is invalid", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $throwable,
                                       ]),
                                       true
                                   )['exception'],
                    'station'   => $v,
                ], "上汽中石化", "oil_station_data", "error");
            }
        }
        $this->dealNotExistsStation($existsStationIds);
        if (!empty($stopStations)) {
            try {
                $total = count($stopStations);
                $totalPage = ceil($total / 50);
                $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation;
                $cacheStation = app('redis')->hmget($oilStationCacheCheckKey, $stopStations);
                $disabledStation = [];
                foreach ($cacheStation as $k => $v) {
                    $stationData = json_decode($v, true) ?? [];
                    if (!$stationData) {
                        continue;
                    }
                    $cacheStation[$stationData['id']] = $stationData;
                    unset($cacheStation[$k]);
                    if ($stationData['is_stop'] == 1) {
                        $disabledStation[] = $stationData['id'];
                    }
                }
                $stopStations = array_diff($stopStations, $disabledStation);
                if (empty($stopStations)) {
                    return;
                }
                for ($p = 0; $p < $totalPage; $p++) {
                    $currentData = array_slice($stopStations, $p * 50, 50);
                    foreach ($currentData as &$cv) {
                        $cv = [
                            "app_station_id" => $cv,
                            "pcode"          => $this->platformCode,
                        ];
                    }
                    try {
                        FOSS_STATIONRequest::handle("v1/station/batchStop", [
                            "id_list" => $currentData,
                            "app_key" => $this->authData['access_key'],
                        ]);
                    } catch (Throwable $exception) {
                        Log::handle("Stop failed about the station's price of sqZsh", [
                            "data"      => $currentData,
                            'exception' => json_decode(Log::getMessage([
                                'exception' => $exception,
                            ]), true)['exception'],
                        ], '上汽中石化', 'oil_station_data', 'error');
                    }
                }
                StationPriceData::updateByWhere([
                    [
                        'field'    => "station_id",
                        'operator' => "in",
                        'value'    => $stopStations,
                    ],
                    [
                        'field'    => "platform_code",
                        'operator' => "=",
                        'value'    => $this->platformCode,
                    ],
                ], [
                    'enabled_state' => 2,
                ]);
                $updateStationCache = [];
                foreach ($stopStations as $stv) {
                    if (!isset($cacheStation[$stv])) {
                        continue;
                    }
                    $cacheStation[$stv]['is_stop'] = 1;
                    $updateStationCache[$stv] = json_encode($cacheStation);
                }
                if (!empty($updateStationCache)) {
                    app('redis')->hmset($oilStationCacheCheckKey, $updateStationCache);
                }
            } catch (Throwable $exception) {
                Log::handle("Stop failed about the station's price of czb", [
                    "stopStations" => $stopStations,
                    'exception'    => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], '上汽中石化', 'oil_station_data', 'error');
            }
        }
    }
}
