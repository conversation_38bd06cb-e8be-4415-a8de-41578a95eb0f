<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\BDT;
use Throwable;

class PullOilForBdt extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:bdt';
    protected $name = 'pull oil for bdt';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for bdt';

    protected $nameAbbreviation = 'bdt';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $data = BDT::handle("1", [
            "start"  => "0",
            'length' => "-1"
        ]);

        Log::handle("Pull oil data by bdt", [
            "data" => $data
        ], "宝兑通", "oil_station_data", "info");

        if (!is_array($data)) {

            Log::handle("Pull oil data failed by bdt", [
                "data" => $data
            ], "宝兑通", "oil_station_data", "warning");
            return;
        }

        $platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation("bdt")['role_code'];
        $provinceData = array_column($data, 'provinceName');
        $cityData = array_column($data, 'cityName');
        $provinceStr = str_replace('省', '', implode(',', $provinceData));
        $provinceStr = str_replace('市', '', $provinceStr);
        $cityStr = str_replace('市', '', implode(',', $cityData));
        $provinceData = explode(',', $provinceStr);
        $cityData = explode(',', $cityStr);
        $provinceMapping = RegionalInfoData::getCityCodeByName($provinceData, true);
        $cityMapping = RegionalInfoData::getCityCodeByName($cityData);

        try {

            //通过高德API转换坐标体系
            $coordinates = array_columns($data, ['shop_longotude', 'shop_latitude']);
            $waitCoordinates = [];

            foreach ($coordinates as &$v) {

                if ((is_null($v['shop_longotude']) or is_null($v['shop_latitude'])) or
                    (empty($v['shop_longotude']) or empty($v['shop_latitude'])) or
                    ($v['shop_longotude'] == 'null' or $v['shop_latitude'] == 'null')) {

                    continue;
                }

                $v["shop_longotude"] = trim($v["shop_longotude"], " .");
                $v["shop_latitude"] = trim($v["shop_latitude"], " .");
                $waitCoordinates[] = "{$v["shop_longotude"]},{$v["shop_latitude"]}";
            }

            $convertedCoordinates = convertOtherCoordinateToGcJ02ByGdApi($waitCoordinates);
        } catch (Throwable $exception) {

            Log::handle("Convert the station's Coordinate of bdt failed", [
                'exception' => $exception,
            ], "宝兑通", "oil_station_data", "error");
            return;
        }

        unset($v);
        foreach ($data as $v) {

            try {

                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $v['shop_name'];
                $oilTemp['station_type'] = 4;

                //转换省份、城市代码
                $mappingProvinceName = str_replace('省', '', $v['provinceName']);
                $mappingProvinceName = str_replace('市', '', $mappingProvinceName);
                $mappingCityName = str_replace('市', '', $v['cityName']);

                if (strpos($v['provinceName'], "市")) {

                    $oilTemp['province_code'] = $provinceMapping[$mappingProvinceName . "1"]['city_code'] ?? '';
                    $oilTemp['city_code'] = $provinceMapping[$mappingProvinceName . "2"]['city_code'] ?? '';
                } else {

                    $oilTemp['province_code'] = $provinceMapping[$mappingProvinceName . "2"]['city_code'] ?? '';
                    $oilTemp['city_code'] = $cityMapping[$mappingCityName]['city_code'] ?? '';
                }

                if (checkIsMunicipality($oilTemp['city_code'])) {

                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['area_code'] = $oilTemp['city_code'];

                //转换经纬度坐标系
                $v["shop_longotude"] = trim($v["shop_longotude"], " .");
                $v["shop_latitude"] = trim($v["shop_latitude"], " .");
                $convertedCoordinatesTemp = $convertedCoordinates["{$v["shop_longotude"]},{$v["shop_latitude"]}"] ?? [];
                $oilTemp['lng'] = '';
                $oilTemp['lat'] = '';

                if (empty($oilTemp['province_code']) or empty($oilTemp['city_code'])) {

                    Log::handle("The address code of oil station doesn't exist", [
                        "data" => $v
                    ], "宝兑通", "oil_station_data", "warning");
                    continue;
                }

                if (!empty($convertedCoordinatesTemp)) {

                    $oilTemp['lng'] = $convertedCoordinatesTemp['lng'] ?? '';
                    $oilTemp['lat'] = $convertedCoordinatesTemp['lat'] ?? '';
                }

                if (strpos($oilTemp['lng'], '.') !== false) {

                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }

                if (strpos($oilTemp['lat'], '.') !== false) {

                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                if (empty($oilTemp['lng']) or empty($oilTemp['lat'])) {

                    Log::handle("The Coordinates of oil station doesn't exist", [
                        "data" => $v
                    ], "宝兑通", "oil_station_data", "warning");
                    continue;
                }

                $oilTemp['address'] = $v['shop_address'];
                $oilTemp['is_stop'] = 0;

                if ($v['shop_status'] == -1) {

                    $oilTemp['is_stop'] = 1;
                }

                $oilPriceTemp = [];
                $oilPriceTemp['oil_type'] = '';
                $oilPriceTemp['oil_level'] = '';
                $oilPriceTemp['oil_name'] = config('oil.oil_type.液化天然气');
                $oilPriceTemp['price'] = $v['lng_price'];
                $oilTemp['price_list'][] = $oilPriceTemp;
                $oilTemp['name_abbreviation'] = 'bdt';
                $oilTemp['id'] = $v['shop_id'];
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['station_id'] = $insertStationPriceData['id'];
                $insertStationPriceData['platform_code'] = $platformCode;
                $insertStationPriceData['enabled_state'] = $insertStationPriceData['is_stop'] + 1;
                $insertStationPriceData['oil_price_list'] = [
                    [
                        'sale_price' => bcmul($v['lng_price'], 100),
                        'oil_no'     => '',
                        'oil_type'   => '液化天然气',
                        'oil_level'  => '',
                        'gun_no'     => '',
                    ]
                ];
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {

                Log::handle("Pull oil for bdt has exception", [
                    'exception' => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                    'data'      => $v,
                ], "宝兑通", "oil_station_data", "error");
            }
        }
    }
}
