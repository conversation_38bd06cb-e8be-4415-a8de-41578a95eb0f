<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\HSY;
use Throwable;


class PullOilForHsy extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:hsy';
    protected $name      = 'pull oil for hsy';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for hsy';

    protected $nameAbbreviation = 'hsy';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {
            try {
                $data = HSY::handle('station/list', 'get', [
                    'source'    => AuthConfigData::getAuthConfigValByName('HSY_APP_SOURCE'),
                    'page'      => $page,
                    'perPage'   => 1000,
                    'appSource' => AuthConfigData::getAuthConfigValByName('HSY_APP_SOURCE'),
                    "lng"       => "116.397451",
                    "lat"       => "39.909187",
                ])['data'];
                $totalPage = ceil($data['totalCount'] / 1000);

                $waitCoordinates = [];
                foreach ($data['items'] as $v) {
                    $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                }
                $convertedCoordinates = convertOtherCoordinateToGcJ02ByGdApi($waitCoordinates);
                $waitCoordinates = [];
                foreach ($data['items'] as &$dv) {
                    $convertedCoordinatesTemp = $convertedCoordinates["{$dv["lng"]},{$dv["lat"]}"] ?? [];
                    $dv['lngGcj02'] = $convertedCoordinatesTemp['lng'] ?? '';
                    $dv['latGcj02'] = $convertedCoordinatesTemp['lat'] ?? '';
                    if (!empty($convertedCoordinatesTemp)) {
                        $waitCoordinates[] = "{$dv["lngGcj02"]},{$dv["latGcj02"]}";
                    }
                }
                $regionData = [];
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['items'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['id'];
                        $oilTemp['assoc_id'] = $v['id'];
                        $existsStationIds[] = $v['id'];
                        $oilTemp['station_name'] = $v['name'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['province_code'] = $regionData["{$v["lngGcj02"]},{$v["latGcj02"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["lngGcj02"]},{$v["latGcj02"]}"]['cityCode'] ?? '';
                        $oilTemp['station_type'] = 2;
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['station_brand'] = config("brand.hsy.{$v['type']}", 8);
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['business_hours'] = "24小时";
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['lngGcj02'];
                        $oilTemp['lat'] = $v['latGcj02'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = 0;
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['enabled_state'] = 1;
                        //如果油站油品价格数据不存在则舍弃该数据
                        if (isset($v['prices']) and is_array($v['prices'])) {
                            //油站价格数据
                            foreach ($v['prices'] as $cv) {
                                $realOilInfo = explode("_", config("oil.oil_mapping.hsy.{$cv['oilNo']}"));
                                $oilPriceTemp = [];
                                $insertOilPriceTemp = [];
                                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                                if (empty($oilPriceTemp['oil_name'])) {
                                    Log::handle("Oil's attribute is invalid", [
                                        "data" => $v
                                    ], "华商云", "oil_station_data", "error");
                                    continue;
                                }
                                $insertOilPriceTemp['oil_type'] = array_flip(
                                                                      config("oil.oil_type")
                                                                  )[$oilPriceTemp['oil_name']] ?? '';
                                $insertOilPriceTemp['oil_no'] = str_replace(
                                    $insertOilPriceTemp['oil_type'],
                                    '',
                                    array_flip(
                                        config("oil.oil_no")
                                    )[$oilPriceTemp['oil_type']] ?? ''
                                );
                                $insertOilPriceTemp['oil_level'] = array_flip(
                                                                       config("oil.oil_level")
                                                                   )[$oilPriceTemp['oil_level']] ?? '';

                                $oilPriceTemp['price'] = $cv['priceYfq'];
                                $oilPriceTemp['mac_price'] = $cv['priceGun'];
                                $insertOilPriceTemp['gun_no'] = implode(
                                    ',',
                                    array_column($cv['gunNos'] ?? [], 'gunNo')
                                );
                                $insertOilPriceTemp['sale_price'] = bcmul($cv['priceYfq'], 100);
                                $insertOilPriceTemp['listing_price'] = bcmul($cv['priceGun'], 100);
                                $oilTemp['price_list'][] = $oilPriceTemp;
                                $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            }

                            $insertStationPriceData['platform_code'] = $this->platformCode;
                        } else {
                            Log::handle("Oil's price data doesn't exist", [
                                "data" => $v
                            ], "华商云", "oil_station_data", "error");
                        }
                        if (empty($oilTemp['price_list'])) {
                            continue;
                        }

                        $insertStationPriceData['station_id'] = $v['id'];
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "华商云", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "华商云", "oil_station_data", "error");

                if ($page == 1) {
                    throw $throwable;
                }
            }
        } while ($page <= $totalPage);

        $this->dealNotExistsStation($existsStationIds);
    }
}
