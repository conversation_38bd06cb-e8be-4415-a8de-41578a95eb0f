<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\CY as CYRequest;
use Throwable;


class PullOilForCy extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:cy';
    protected $name = 'pull oil for cy';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for cy';

    protected $nameAbbreviation = 'cy';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {

            try {

                $data = CYRequest::handle("/thirdApi/station/list", [
                    "pageSize" => 100,
                    "page"     => $page,
                ])['data'];
                $totalPage = ceil($data['count'] / 100);

                foreach ($data['list'] as $v) {

                    try {

                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['id'];
                        $oilTemp['assoc_id'] = $v['id'];
                        $existsStationIds[] = $v['id'];
                        $oilTemp['station_name'] = $v['station_name'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['province_code'] = $v['province_code'] . '000000';
                        $oilTemp['city_code'] = $v['city_code'] . '000000';
                        $oilTemp['station_type'] = $v['station_type'];
                        $oilTemp['trade_type'] = $v['trade_type'];
                        $oilTemp['is_highway'] = $v['is_highway'];
                        $oilTemp['contact_phone'] = $v['contact_phone'];
                        $oilTemp['station_brand'] = $v['station_brand_name'] == '中石油' ? 2 : 8;
                        $oilTemp['station_oil_unit'] = $v['oil_uint'];
                        $oilTemp['business_hours'] = "{$v['beginTime']}-{$v['endTime']}";
                        if (checkIsMunicipality($oilTemp['city_code'])) {

                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['lng'];
                        $oilTemp['lat'] = $v['lat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {

                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {

                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = $v['is_stop'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['enabled_state'] = $v['is_stop'] + 1;
                        //如果油站油品价格数据不存在则舍弃该数据
                        if (isset($v['price_list']) and is_array($v['price_list'])) {

                            //油站价格数据
                            foreach ($v['price_list'] as $cv) {
                                $realOilInfo = explode("_", config("oil.oil_mapping.cy.{$cv['oil_number']}"));
                                $oilPriceTemp = [];
                                $insertOilPriceTemp = [];
                                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                                if (empty($oilPriceTemp['oil_name'])) {
                                    Log::handle("Oil's attribute is invalid", [
                                        "data" => $v
                                    ], "畅油", "oil_station_data", "error");
                                    continue;
                                }
                                $insertOilPriceTemp['oil_type'] = array_flip(
                                                                      config("oil.oil_type")
                                                                  )[$oilPriceTemp['oil_name']] ?? '';
                                $insertOilPriceTemp['oil_no'] = str_replace(
                                    $insertOilPriceTemp['oil_type'],
                                    '',
                                    array_flip(
                                        config("oil.oil_no")
                                    )[$oilPriceTemp['oil_type']] ?? ''
                                );
                                $insertOilPriceTemp['oil_level'] = array_flip(
                                                                       config("oil.oil_level")
                                                                   )[$oilPriceTemp['oil_level']] ?? '';

                                $oilPriceTemp['price'] = $cv['price'];
                                $oilPriceTemp['mac_price'] = $cv['gun_price'];
                                $insertOilPriceTemp['gun_no'] = implode(',', $cv['gun_numbers'] ?? []);
                                $insertOilPriceTemp['sale_price'] = bcmul($cv['price'], 100);
                                $insertOilPriceTemp['listing_price'] = bcmul($cv['gun_price'], 100);
                                $oilTemp['price_list'][] = $oilPriceTemp;
                                $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            }

                            $insertStationPriceData['platform_code'] = $this->platformCode;
                        } else {

                            Log::handle("Oil's price data doesn't exist", [
                                "data" => $v
                            ], "畅油", "oil_station_data", "error");
                        }
                        if (empty($oilTemp['price_list'])) {

                            continue;
                        }

                        $insertStationPriceData['station_id'] = $v['id'];
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {

                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "畅油", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {

                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "畅油", "oil_station_data", "error");

                if ($page == 1) {

                    throw $throwable;
                }
            }
        } while ($page <= $totalPage);

        $this->dealNotExistsStation($existsStationIds);
    }
}
