<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\Pay\Main as PayMainLogic;
use Illuminate\Console\Command;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Request\HBKJ as HBKJRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PullVerificationResultForHbkj extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-verification-result:hbkj';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull verification result for hbkj.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return int
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle(): int
    {
        $authInfoData = AuthInfoData::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => '=',
                'value'    => 'hbkj',
            ],
        ], ['*']);
        $platformOrderIds = array_column(
            FOSS_STATIONRequest::handle(
                "api/coupon/v1/getUnExpiredAndUnVerifiedCoupons",
                [
                    'pcode'             => $authInfoData['role_code'],
                    'tripartite_status' => 10,
                ]
            )["data"] ?? [],
            "tripartite_voucher"
        );
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => 'hbkj',
            ],
            [
                'field'    => 'platform_order_id',
                'operator' => 'in',
                'value'    => $platformOrderIds,
            ],
        ], ['id', 'platform_order_id', 'self_order_id', 'extend'], false);
        foreach ($orderAssocData as $v) {
            try {
                $response = HBKJRequest::handle('getCouponState', [
                    'businessId' => $v['self_order_id'],
                    'voucher'    => $v['platform_order_id'],
                ]);
                if ($response['state'] == 90) {
                    try {
                        $data = FOSS_STATIONRequest::handle(
                            'api/coupon/v1/getTripartiteCouponByTripartiteVoucherAndSupplierCode', [
                            'pcode'              => $authInfoData['role_code'],
                            'tripartite_voucher' => $v['platform_order_id'],
                        ]);
                        if ($data['data'][0]['tripartite_status'] == 25) {
                            continue;
                        }
                    } catch (Throwable $exception) {
                        continue;
                    }
                    $extend = json_decode($v['extend'], true) ?? [];
                    $ownerOrderInfo = $extend['owner'] ?? $extend;
                    $orgOrderInfo = OrderAssocData::getOrderInfoByWhere([
                        [
                            'field'    => 'self_order_id',
                            'operator' => '=',
                            'value'    => $v['self_order_id'],
                        ],
                        [
                            'field'    => 'platform_name',
                            'operator' => '!=',
                            'value'    => 'hbkj',
                        ],
                    ], ['self_order_id', 'platform_order_id', 'platform_name'], true, true);
                    (new FeiShu())->supplierRefundAlarm([
                        'supplier_order_id' => $v['platform_order_id'],
                        'self_order_id'     => $v['self_order_id'],
                        'platform_order_id' => $orgOrderInfo->platform_order_id ?? '',
                        'supplier_name'     => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                            'hbkj'
                        ),
                        'station_name'      => $ownerOrderInfo['trade_place'],
                        'org_name'          => $ownerOrderInfo['org_name'],
                        'price'             => $ownerOrderInfo['trade_price'],
                        'oil_num'           => $ownerOrderInfo['oil_num'],
                        'money'             => $ownerOrderInfo['trade_money'],
                    ]);
                    continue;
                }
                if ($response['state'] == 40) {
                    try {
                        (new PayMainLogic([
                            'auth_data' => $authInfoData,
                            'realData'  => array_merge($response, [
                                'couponNo' => $v['platform_order_id'],
                                'useTime'  => $response['usedTime']
                            ]),
                        ]))->handle();
                    } catch (Throwable $throwable) {
                        Log::handle(
                            'Push coupon verification result failed.',
                            [
                                'exception' => $throwable,
                            ],
                            DockingPlatformInfoData::getPlatformNameByNameAbbreviation('hbkj'),
                            'pull_verification_result',
                            'error'
                        );
                    }
                }
            } catch (Throwable $throwable) {
                continue;
            }
        }
        return 0;
    }
}
