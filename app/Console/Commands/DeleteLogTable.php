<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\Log\QueueLog as Log;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PDO;
use Throwable;


class DeleteLogTable extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'log-table:delete {--type=} {--week=}';
    protected $allowType = [
        'queue',
        'response',
        'receive',
        'request',
        'system',
    ];

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'delete log table.
                                  --type: queue|response|receive|request|system
                                  --week: which week.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        if (getLaravelAndMachineEnv("RUN__ENVIRONMENT") != "test") {

            return;
        }

        $type = $this->option("type");
        if (!in_array($type, $this->allowType)) {

            throw new Exception("the type is invalid");
        }

        $week = $this->option("week");
        if (empty($week)) {

            $week = date('W', strtotime('-2 week'));
        }

        $currentYear = Carbon::now()->yearIso;
        $connection = DB::connection("mysql");
        $tablePrefix = $connection->getTablePrefix();
        $pdo = $connection->getPdo();
        $prevTable = "$tablePrefix{$type}_log_{$currentYear}_$week";
        $this->deleteTable($pdo, [
            $prevTable,
        ]);
    }

    public static function deleteTable(PDO $pdo, array $deleteTables)
    {
        Log::handle("Delete table started", func_get_args(), "系统",
            "delete_table", "info");
        foreach ($deleteTables as $v) {

            try {

                $pdo->exec("drop table if exists $v;");
            } catch (Throwable $exception) {

                Log::handle("Delete table failed", func_get_args(), "系统",
                    "delete_table", "error");
            }
        }
        Log::handle("Delete table finished", func_get_args(), "系统",
            "delete_table", "info");
    }
}
