<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\HYT as HYTRequest;
use Throwable;

class PullOilForHyt extends PullOil
{
    protected $nameAbbreviation = 'hyt';
    protected $platformCode     = '';

    private $stationTypeMapping = [
        0 => 2,
        2 => 4,
    ];

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:hyt';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for hyt';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @return void
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle($parameters = [])
    {
        $single = false;
        if (!isset($parameters['stationId'])) {
            $stationData = HYTRequest::handle("dispark/gasoil/list", [
                "currentPage" => 1,
                "pageSize"    => 50000,
                "type"        => 0,
                "longitude"   => "116.397451",
                "latitude"    => "39.909187",
            ], 'get')['data']['list'] ?? [];
            $responseData = HYTRequest::handle("dispark/gasoil/list", [
                "currentPage" => 1,
                "pageSize"    => 50000,
                "type"        => 1,
                "longitude"   => "116.397451",
                "latitude"    => "39.909187",
            ], 'get')['data']['list'] ?? [];
            $stationData = array_merge($stationData, $responseData);
        } else {
            $single = true;
            if (isset($parameters['station_type'])) {
                $responseData = HYTRequest::handle("dispark/gasoil/info", [
                    "stationId" => $parameters['station_id'],
                    "type"      => $parameters['station_type'],
                ], 'get');
                $stationData = [$responseData['data'] ?? []];
            } else {
                $responseData = HYTRequest::handle("dispark/gasoil/info", [
                    "stationId" => $parameters['station_id'],
                    "type"      => 0,
                ], 'get');
                $stationData = [$responseData['data'] ?? []];
                $responseData = HYTRequest::handle("dispark/gasoil/info", [
                    "stationId" => $parameters['station_id'],
                    "type"      => 1,
                ], 'get');
                $stationData = array_merge($stationData, [$responseData['data'] ?? []]);
            }
        }

        //通过高德API进行逆地理编码
        $coordinates = array_columns($stationData, ['addressGpsY', 'addressGpsX']);
        $waitCoordinates = [];
        $existsStationIds = [];
        foreach ($coordinates as $v) {
            if ((is_null($v['addressGpsY']) or is_null($v['addressGpsX'])) or
                (empty($v['addressGpsY']) or empty($v['addressGpsX'])) or
                ($v['addressGpsY'] == 'null' or $v['addressGpsX'] == 'null')) {
                continue;
            }
            $waitCoordinates[] = "{$v["addressGpsY"]},{$v["addressGpsX"]}";
        }
        $regionData = getRegionForCoordinateByGdApi($waitCoordinates);

        foreach ($stationData as $sv) {
            try {
                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $sv['stationName'];
                $oilTemp['station_type'] = $this->stationTypeMapping[$sv['stationType']] ?? 5;
                $existsStationIds[] = $sv['id'];
                $oilTemp['assoc_id'] = $sv['id'];
                $oilTemp["lng"] = $sv["addressGpsY"];
                $oilTemp["lat"] = $sv["addressGpsX"];
                $oilTemp['address'] = $sv['addressDetail'];
                $oilTemp['is_stop'] = $sv['status'] - 1;
                $oilTemp['contact_phone'] = $sv['linkMobile'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['id'] = $sv['id'];
                $oilTemp['province_code'] = $regionData["{$sv["addressGpsY"]},{$sv["addressGpsX"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$sv["addressGpsY"]},{$sv["addressGpsX"]}"]['cityCode'] ?? '';
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                if (checkIsMunicipality($oilTemp['province_code'])) {
                    $oilTemp['city_code'] = $oilTemp['province_code'];
                }
                if (!in_array("0", $sv["payTypes"])) {
                    $oilTemp['is_stop'] = 1;
                }

                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['extends'] = [];
                foreach ($sv['goodsList'] as $scv) {
                    $oilAttributesMapped = explode('_', config('oil.oil_mapping.hyt')[$scv['name']]);
                    $oilPriceTemp = [];
                    $oilPriceTemp['oil_name'] = $oilAttributesMapped[0] ?? '';
                    $oilPriceTemp['oil_type'] = $oilAttributesMapped[1] ?? '';
                    $oilPriceTemp['oil_level'] = $oilAttributesMapped[2] ?? '';
                    if (empty($oilPriceTemp['oil_name'])) {
                        Log::handle("Oil's attribute is invalid", [
                            "data" => $sv
                        ], "惠运通", "oil_station_data", "error");
                        continue;
                    }
                    $oilPriceTemp['price'] = $scv['driverPrice'];
                    $oilPriceTemp['mac_price'] = $scv['publicPrice'];
                    $oilTemp['price_list'][] = $oilPriceTemp;
                    $insertOilPriceTemp = $oilPriceTemp;
                    $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$oilAttributesMapped[0]] ?? '';
                    $insertOilPriceTemp['oil_no'] = str_replace(
                        $insertOilPriceTemp['oil_type'],
                        '',
                        array_flip(config("oil.oil_no"))[$oilAttributesMapped[1]] ?? ''
                    );
                    $insertOilPriceTemp['oil_level'] = array_flip(
                                                           config("oil.oil_level")
                                                       )[$oilAttributesMapped[2]] ?? '';
                    $insertOilPriceTemp['sale_price'] = bcmul(100, $scv['driverPrice']);
                    $insertOilPriceTemp['listing_price'] = bcmul(100, $scv['publicPrice']);
                    $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                    $insertStationPriceData['extends'][$insertOilPriceTemp['oil_type'] .
                                                       $insertOilPriceTemp['oil_no'] . $insertOilPriceTemp['oil_level']] = [
                        'goods_id'     => $scv['id'],
                        'station_type' => $sv['stationType'],
                    ];
                }
                $insertStationPriceData['enabled_state'] = $sv['status'];
                $insertStationPriceData['station_id'] = $sv['id'];
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                $oilTemp['trade_type'] = 3;
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {
                Log::handle(
                    "Pull station failed",
                    [
                        'stationData' => $sv,
                        'exception'   => json_decode(
                                             Log::getMessage([
                                                 'exception' => $exception,
                                             ]),
                                             true
                                         )['exception'],
                    ],
                    DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->nameAbbreviation,
                        "platform_name",
                        ""
                    )['platform_name'],
                    "oil_station_data",
                    "error"
                );
            }
        }

        if (!$single) {
            $this->dealNotExistsStation($existsStationIds);
        }
    }
}
