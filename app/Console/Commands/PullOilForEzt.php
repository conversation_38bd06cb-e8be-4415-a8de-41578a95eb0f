<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\EZT as EZTRequest;
use Throwable;

class PullOilForEzt extends PullOil
{
    protected $nameAbbreviation = 'ezt';
    protected $platformCode = '';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:ezt';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for ezt';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle()
    {
        $stationData = EZTRequest::handle("syncStations");

        //通过高德API进行逆地理编码
        $coordinates = array_columns($stationData["data"], ['longitude', 'latitude']);
        $waitCoordinates = [];
        $existsStationIds = [];

        foreach ($coordinates as &$v) {

            if ((is_null($v['longitude']) or is_null($v['latitude'])) or
                (empty($v['longitude']) or empty($v['latitude'])) or
                ($v['longitude'] == 'null' or $v['latitude'] == 'null')) {

                continue;
            }

            $v["longitude"] = trim($v["longitude"], " .");
            $v["latitude"] = trim($v["latitude"], " .");
            $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
        }

        $regionData = getRegionForCoordinateByGdApi($waitCoordinates);

        unset($v);
        foreach ($stationData["data"] as $v) {

            try {

                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $v['stationName'];
                $oilTemp['station_type'] = 4;
                $existsStationIds[] = $v['stationId'];
                $oilTemp['assoc_id'] = $v['stationId'];
                $oilTemp["lng"] = $v["longitude"];
                $oilTemp["lat"] = $v["latitude"];
                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = 0;
                $oilTemp['contact_phone'] = $v['phone'];
                $oilPriceTemp = [];
                $oilPriceTemp['oil_type'] = '';
                $oilPriceTemp['oil_level'] = '';
                $oilPriceTemp['oil_name'] = config('oil.oil_type.液化天然气');
                $oilPriceTemp['price'] = $v['price'];
                $oilPriceTemp['mac_price'] = $v['listingPrice'];
                $oilTemp['price_list'][] = $oilPriceTemp;
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['id'] = $v['stationId'];
                $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';

                if (strpos($oilTemp['lng'], '.') !== false) {

                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }

                if (strpos($oilTemp['lat'], '.') !== false) {

                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                if (checkIsMunicipality($oilTemp['province_code'])) {

                    $oilTemp['city_code'] = $oilTemp['province_code'];
                }

                $insertOilPriceTemp = $oilPriceTemp;
                $insertOilPriceTemp['oil_type'] = '液化天然气';
                $insertOilPriceTemp['sale_price'] = bcmul(100, $v['price']);
                $insertOilPriceTemp['listing_price'] = bcmul(100, $v['listingPrice']);

                if ($v['isOpen'] != 1) {

                    $oilTemp['is_stop'] = 1;
                    $insertStationPriceData['enabled_state'] = 2;
                }

                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                $insertStationPriceData['station_id'] = $v['stationId'];
                $insertStationPriceData['platform_code'] = $this->platformCode;
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {

                Log::handle("Pull station failed", [
                    'stationData' => $v,
                    'exception'   => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], DockingPlatformInfoData::getFieldsByNameAbbreviation($this->nameAbbreviation, "platform_name",
                    "")['platform_name'], "oil_station_data", "error");
            }
        }

        $this->dealNotExistsStation($existsStationIds);
    }
}
