<?php
// 卓壹(油)(油)
namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\ZHUOYIY as ZHUOYIYRequest;
use Throwable;


class PullOilForZHUOYIY extends PullOil
{
    private static $statusMapping = [
        0 => 1,
        1 => 0,
    ];
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:zhuoyiy';
    protected $name      = 'pull oil for zhuoyiy';
    /**
     * 命令描述
     *
     * @var string
     */
    protected $description      = 'pull oil for zhuoyiy';
    protected $nameAbbreviation = 'zhuoyiy';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    public static function getStatusMapping(): array
    {
        return self::$statusMapping;
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];
        do {
            try {
                $data = ZHUOYIYRequest::handle("pullOilstationList", [
                    "pageSize"    => 100,
                    "currentPage" => $page,
                ]);
                $totalPage = ceil($data['total'] / 100);
                $regionCodes = [];
                foreach ($data['resultList'] as &$ov) {
                    $ov['provinceId'] = "{$ov['province']}000000";
                    $ov['cityId'] = "{$ov['city']}000000";
                    $regionCodes[] = $ov['provinceId'];
                    $regionCodes[] = $ov['cityId'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                $waitCoordinates = [];
                foreach ($data['resultList'] as $v) {
                    if (!in_array($v['provinceId'], $existsRegionCodes) or
                        !in_array($v['cityId'], $existsRegionCodes) and !empty($v['provinceId']) and
                                                                        !empty($v['cityId'])) {
                        $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                    }
                }
                $regionData = [];
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }
                foreach ($data['resultList'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['stationId'];
                        $oilTemp['assoc_id'] = $v['stationId'];
                        $existsStationIds[] = $v['stationId'];
                        $oilTemp['station_name'] = $v['stationName'];
                        $oilTemp['station_type'] = 2;
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['is_highway'] = $v['highspeed'];
                        $oilTemp['contact'] = '官方电话';
                        $oilTemp['contact_phone'] = $v['tel'] ?? '';
                        $oilTemp['payment_certificate_type'] = 0;
                        if ($v['payType'] == 2) {
                            $oilTemp['payment_certificate_type'] = 1;
                        }
                        $oilTemp['station_brand'] = config(
                            "oil.brand.$this->nameAbbreviation." .
                            $v['stationBrand'],
                            8
                        );
                        $oilTemp['station_oil_unit'] = $v['oilUnit'];
                        $oilTemp['business_hours'] = $v['openTime'];
                        if (!in_array($v['provinceId'], $existsRegionCodes) or
                            !in_array($v['cityId'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceId'];
                            $oilTemp['city_code'] = $v['cityId'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['lng'];
                        $oilTemp['lat'] = $v['lat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = self::$statusMapping[$v['station_status']] ?? 1;
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        //如果油站油品价格数据不存在则舍弃该数据
                        if (!empty($v['oilList']) and is_array($v['oilList'])) {
                            //油站价格数据
                            foreach ($v['oilList'] as $cv) {
                                $realOilInfo = explode(
                                    "_",
                                    config(
                                        "oil.oil_mapping.$this->nameAbbreviation.code.{$cv['oilId']}",
                                        ""
                                    )
                                );
                                $oilPriceTemp = [];
                                $insertOilPriceTemp = [];
                                $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                                $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                                $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                                if (empty($oilPriceTemp['oil_name'])) {
                                    Log::handle("Oil's attribute is invalid", [
                                        "data" => $v
                                    ], "卓壹(油)", "oil_station_data", "error");
                                    continue;
                                }
                                $insertOilPriceTemp['oil_type'] = array_flip(
                                                                      config("oil.oil_type")
                                                                  )[$oilPriceTemp['oil_name']] ?? '';
                                $insertOilPriceTemp['oil_no'] = str_replace(
                                    $insertOilPriceTemp['oil_type'],
                                    '',
                                    array_flip(
                                        config("oil.oil_no")
                                    )[$oilPriceTemp['oil_type']] ?? ''
                                );
                                $insertOilPriceTemp['oil_level'] = array_flip(
                                                                       config("oil.oil_level")
                                                                   )[$oilPriceTemp['oil_level']] ?? '';
                                $oilPriceTemp['price'] = $cv['stationPrice'];
                                $oilPriceTemp['mac_price'] = $cv['stationPrice'];
                                $insertOilPriceTemp['sale_price'] = bcmul($cv['stationPrice'], 100);
                                $insertOilPriceTemp['listing_price'] = bcmul($cv['stationPrice'], 100);
                                $oilTemp['price_list'][] = $oilPriceTemp;
                                $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            }
                            $insertStationPriceData['platform_code'] = $this->platformCode;
                        } else {
                            Log::handle("Oil's price data doesn't exist", [
                                "data" => $v
                            ], "卓壹(油)", "oil_station_data", "error");
                        }
                        $insertStationPriceData['station_id'] = $v['stationId'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "卓壹(油)", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "卓壹(油)", "oil_station_data", "error");

                if ($page == 1) {
                    throw $throwable;
                }
            }
        } while ($page <= $totalPage);
        $this->dealNotExistsStation($existsStationIds);
    }
}
