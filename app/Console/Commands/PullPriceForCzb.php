<?php

namespace App\Console\Commands;


use App\Jobs\SubPullPriceInfoForCzb;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use Illuminate\Support\Facades\Queue;
use Throwable;

class PullPriceForCzb extends PullPrice
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-price:czb';
    protected $name = "pull price for czb";

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull price for czb';

    protected $nameAbbreviation = 'czb';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_czb";
        $stationId = app('redis')->hkeys($oilStationCacheCheckKey);
        $stationCount = count($stationId);
        $stationPage = ceil($stationCount / 99);
        $stationIds = '';

        for ($i = 0; $i < $stationPage; $i++) {

            try {

                $stationIds = array_slice($stationId, $i * 99, 99);
                Queue::push(new SubPullPriceInfoForCzb($stationIds), '', 'supplier_station_queue');
            } catch (Throwable $exception) {

                Log::handle("Get failed about the station's price of czb", [
                    "stationIds" => $stationIds,
                    'exception'  => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], '车主邦', 'oil_station_data', 'error');
            }
        }
    }
}
