<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\Pay\Main as PayMainLogic;
use Illuminate\Console\Command;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\HSY as HSYRequest;
use Throwable;


class PullVerificationResultForHsy extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-verification-result:hsy';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull verification result for hsy.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return int
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle(): int
    {
        $orderAssocData = convertListToDict(OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => 'hsy',
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 2,
            ],
            [
                'field'    => 'self_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'created_at',
                'operator' => '>=',
                'value'    => date('Y-m-d H:i:s', strtotime('-1 month')),
            ],
        ], ['id', 'platform_order_id', 'self_order_id'], false), 'self_order_id');
        $orderData = convertListToDict(FOSS_ORDERRequest::handle(
            "/api/order/getBatchOrderItem", [
            "order_ids" => array_column($orderAssocData, 'self_order_id'),
        ])["data"] ?? [], "order_id");
        foreach ($orderAssocData as $k => $v) {

            if (!isset($orderData[$k])) {

                unset($orderAssocData[$k]);
                continue;
            }
            if ($orderData[$k]['order_status'] != 2) {

                unset($orderAssocData[$k]);
            }
        }
        $orderAssocData = array_values($orderAssocData);
        foreach ($orderAssocData as $v) {

            try {

                $response = HSYRequest::handle('order/query', 'get', [
                    'orderId'   => $v['platform_order_id'],
                    'appSource' => AuthConfigData::getAuthConfigValByName('HSY_APP_SOURCE'),
                ]);
                if ($response['data']['settlementState'] == 1) {

                    $authInfoData = AuthInfoData::getAuthInfoByWhere([
                        [
                            'field'    => 'name_abbreviation',
                            'operator' => '=',
                            'value'    => 'hsy',
                        ],
                    ], ['*']);
                    (new PayMainLogic([
                        'auth_data' => $authInfoData,
                        'orderId'   => $v['platform_order_id'],
                        'status'    => $response['data']['payStatus'],
                    ]))->handle();
                }
            } catch (Throwable $throwable) {

                Log::handle('Query coupon verification result failed.', [
                    'exception' => $throwable,
                ], DockingPlatformInfoData::getPlatformNameByNameAbbreviation('hsy'),
                    'pull_verification_result', 'error');
            }
        }
        return 0;
    }
}
