<?php

namespace App\Console\Commands;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Request\CZB as CZBRequest;
use Throwable;

class PullOilForCzb extends PullOil
{
    private static $nameAbbreviationMapping = [
        ''   => 'czb',
        '10' => 'czb_sq',
    ];
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:czb';
    protected $name = 'pull oil for czb';
    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for czb';
    protected $nameAbbreviation = 'czb';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    public static function getNameAbbreviations(): array
    {
        return array_values(self::$nameAbbreviationMapping);
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        try {

            $data = CZBRequest::handle("gas/queryGasInfoListOilNoNew", [
                'channelId' => AuthConfigData::getAuthConfigValByName("CZB_PLATFORM_NO")
            ], 240);
            if (!is_array($data)) {

                Log::handle("Pull oil data failed by czb", [
                    "data" => $data
                ], "车主邦", "oil_station_data", "error");
                return;
            }

            $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_czb";
            $regionCodes = [];
            $waitCoordinates = [];

            foreach ($data['result'] as &$ov) {

                $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                $ov['cityCode'] = "{$ov['cityCode']}000000";
                $regionCodes[] = $ov['provinceCode'];
                $regionCodes[] = $ov['cityCode'];
            }

            $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));

            foreach ($data['result'] as $v) {

                if (!in_array($v['provinceCode'], $existsRegionCodes) or !in_array($v['cityCode'], $existsRegionCodes)) {

                    $waitCoordinates[] = "{$v["gasAddressLongitude"]},{$v["gasAddressLatitude"]}";
                }
            }

            if ($waitCoordinates) {

                $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
            }

            foreach ($data['result'] as $v) {

                try {

                    //油站数据
                    $oilTemp = [];
                    $oilTemp['id'] = $v['gasId'];
                    $cacheOilTemp = json_decode(app('redis')->hget($oilStationCacheCheckKey, $v['gasId']), true);
                    $oilTemp['gasId'] = $v['gasId'];
                    $oilTemp['station_brand'] = config("brand.czb")[$v['gasType']] ?? 8;
                    $oilTemp['station_name'] = $v['gasName'];
                    $oilTemp["lng"] = updateLngOrLat($v["gasAddressLongitude"] ?? '');
                    $oilTemp["lat"] = updateLngOrLat($v["gasAddressLatitude"] ?? '');
                    $oilTemp['address'] = $v['gasAddress'];
                    $oilTemp['business_hours'] = $v['businessHours'];
                    $gasSourceId = !empty($v['gasSourceId']) ? (string)$v['gasSourceId'] : "";
                    if (!isset(self::$nameAbbreviationMapping[$gasSourceId])) {
                        continue;
                    }
                    $oilTemp['real_name_abbreviation'] = self::$nameAbbreviationMapping[$gasSourceId];
                    $oilTemp['price_list'] = [];

                    if (!in_array($v['provinceCode'], $existsRegionCodes) or !in_array(
                            $v['cityCode'],
                            $existsRegionCodes
                        )) {
                        $oilTemp['province_code'] = $regionData["{$v["gasAddressLongitude"]},{$v["gasAddressLatitude"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["gasAddressLongitude"]},{$v["gasAddressLatitude"]}"]['cityCode'] ?? '';
                    } else {
                        $oilTemp['province_code'] = $v['provinceCode'];
                        $oilTemp['city_code'] = $v['cityCode'];
                    }
                    if (checkIsMunicipality($oilTemp['city_code'])) {

                        $oilTemp['province_code'] = $oilTemp['city_code'];
                    }

                    if ($oilTemp != $cacheOilTemp) {

                        $oilTemp['isPush'] = true;
                    }
                    app('redis')->hset($oilStationCacheCheckKey, $v['gasId'], json_encode($oilTemp));
                } catch (Throwable $exception) {

                    Log::handle("Cache failed about the station of czb", [
                        'data'      => $oilTemp,
                        'initData'  => $v,
                        'exception' => json_decode(Log::getMessage([
                            'exception' => $exception,
                        ]), true)['exception'],
                    ], '车主邦', 'oil_station_data', 'error');
                }
            }
        } catch (Throwable $exception) {

            Log::handle("Failed to preprocess site data", [
                'exception' => json_decode(Log::getMessage([
                    'exception' => $exception,
                ]), true)['exception'],
            ], '车主邦', 'oil_station_data', 'error');
        }
    }
}
