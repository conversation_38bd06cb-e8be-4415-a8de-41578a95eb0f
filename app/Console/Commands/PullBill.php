<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use Illuminate\Console\Command;

class PullBill extends Command
{
    protected $nameAbbreviation = 'base';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:base';
    protected $name = 'pull bill';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return true|void
     */
    public function handle()
    {
        return true;
    }
}
