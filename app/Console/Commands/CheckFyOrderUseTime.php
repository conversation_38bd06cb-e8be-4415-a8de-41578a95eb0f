<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\OrderAssoc;
use Illuminate\Console\Command;
use Throwable;
use Tool\Alarm\FeiShu;

class CheckFyOrderUseTime extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'check-fy-order-use-time';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'check-fy-order-use-time.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $waitCheckOrderData = OrderAssoc::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => 'fy',
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'created_at',
                'operator' => '>=',
                'value'    => date(
                    'Y-m-d H:i:s',
                    strtotime('-5 minute')
                ),
            ],
        ], ['self_order_id', 'platform_order_id', 'extend'], false, true);
        foreach ($waitCheckOrderData as $v) {
            $extend = json_decode($v->extend, true) ?? [];
            if (bccomp(
                    bcsub(
                        $extend['__order_end_time'] ?? 0,
                        $extend['__order_start_time'] ?? 0,
                        2
                    ),
                    3,
                    2
                ) > 0) {
                try {
                    (new FeiShu())->fyOrderUseTimeExceptionAlarm([
                        'self_order_id'     => $v->self_order_id,
                        'platform_order_id' => $v->platform_order_id,
                        'station_name'      => $extend['station_name'],
                        'oil_num'           => $extend['oil_num'],
                        'money'             => $extend['money'],
                        'use_time'          => bcsub(
                            $extend['__order_end_time'] ?? 0,
                            $extend['__order_start_time'] ?? 0,
                            2
                        ),
                        'receive_time'      => date(
                            'Y-m-d H:i:s',
                            $extend['__order_start_time'] ?? 0
                        ),
                        'order_trace_id'    => $extend['__order_trace_id'] ?? '',
                        'pay_trace_id'      => $extend['__pay_trace_id'] ?? '',
                    ]);
                } catch (Throwable $throwable) {
                    continue;
                }
            }
        }
        return 0;
    }
}
