<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\XYN as XY_NEWRequest;
use Throwable;


class PullOilForXYN extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:xyn';
    protected $name      = 'pull oil for xyn';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for xyn';

    protected $nameAbbreviation = 'xyn';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $existsStationIds = [];

        $data = XY_NEWRequest::handle("oil-site/openPlatform/getAllSiteInfos", [
            'companyId' => AuthConfigData::getAuthConfigValByName('XYN_APP_COMPANY_ID'),
        ])['parsed_data'];

        $regionCodes = [];
        $waitCoordinates = [];
        foreach ($data as &$ov) {
            $ov['provinceCode'] = "{$ov['provinceCode']}000000";
            $ov['cityCode'] = "{$ov['cityCode']}000000";
            $regionCodes[] = $ov['provinceCode'];
            $regionCodes[] = $ov['cityCode'];
        }
        $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
        foreach ($data as $v) {
            if (!in_array($v['provinceCode'], $existsRegionCodes) or !in_array(
                    $v['cityCode'],
                    $existsRegionCodes
                )) {
                $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
            }
        }

        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }

        foreach ($data as $v) {
            try {
                //油站数据
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['id'] = $v['id'];
                $oilTemp['assoc_id'] = $oilTemp['id'];
                $existsStationIds[] = $oilTemp['id'];
                $oilTemp['station_name'] = $v['siteName'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;

                if (!in_array($v['provinceCode'], $existsRegionCodes) or !in_array(
                        $v['cityCode'],
                        $existsRegionCodes
                    )) {
                    $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                    $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                } else {
                    $oilTemp['province_code'] = $v['provinceCode'];
                    $oilTemp['city_code'] = $v['cityCode'];
                }
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['lng'] = $v['longitude'];
                $oilTemp['lat'] = $v['latitude'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = 1;
                if ($v['globalEnable'] == 1 and $v['putawayMark'] == 1 and $v['activePay'] == 1) {
                    $oilTemp['is_stop'] = 0;
                }
                $oilTemp['station_type'] = 2;
                $oilTemp['contact'] = $v['siteContacts'];
                $oilTemp['contact_phone'] = $v['siteContactPhone'];
                $oilTemp['business_hours'] = '24小时';
                $oilTemp['is_highway'] = $v['highSpeedMark'];
                $oilTemp['station_brand'] = config("brand.$this->nameAbbreviation")[$v['siteBrand']] ?? 8;
                $oilTemp['station_oil_unit'] = 1;
                $oilTemp['allow_switch_unit'] = 0;
                if ($v['activePay'] == 1) {
                    $oilTemp['trade_type'] = 3;
                } elseif ($v['qrcodePay'] == 1) {
                    $oilTemp['trade_type'] = 1;
                } elseif ($v['qrcodeSitePay'] == 1) {
                    $oilTemp['trade_type'] = 4;
                }
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                $insertStationPriceData['extends'] = [];
                //如果油站油品价格数据不存在则舍弃该数据
                if (isset($v['oilInfoDtos']) and is_array($v['oilInfoDtos'])) {
                    //油站价格数据
                    foreach ($v['oilInfoDtos'] as $cv) {
                        if ($cv['enableMark'] != 1) {
                            continue;
                        }
                        $oilInfo = explode("_", config("oil.oil_mapping.$this->nameAbbreviation.{$cv['oilsCode']}"));
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $oilInfo[1] ?? "";
                        $oilPriceTemp['oil_level'] = $oilInfo[2] ?? "";
                        $oilPriceTemp['oil_name'] = $oilInfo[0] ?? "";
                        if (empty($oilPriceTemp['oil_name'])) {
                            Log::handle("Oil's attribute is invalid", [
                                "data" => $v
                            ], "星油(新)", "oil_station_data", "error");
                            continue;
                        }
                        $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$oilInfo[0]] ?? '';
                        $insertOilPriceTemp['oil_no'] = count($oilInfo) > 1 ? str_replace(
                            $insertOilPriceTemp['oil_type'],
                            '',
                            array_flip(config("oil.oil_no"))[$oilInfo[1]] ?? ''
                        ) : "";
                        $insertOilPriceTemp['oil_level'] = array_flip(
                                                               config('oil.oil_level')
                                                           )[$oilInfo[2]] ?? '';

                        $oilPriceTemp['price'] = (float)$cv['oilsPrice'];
                        $oilPriceTemp['mac_price'] = (float)$cv['oilsSitePrice'];
                        $insertOilPriceTemp['gun_no'] = $cv['oilsBar'];
                        $insertOilPriceTemp['sale_price'] = bcmul($cv['oilsPrice'], 100);
                        $insertOilPriceTemp['listing_price'] = bcmul($cv['oilsSitePrice'], 100);
                        $insertOilPriceTemp['issue_price'] = bcmul($cv['marketPrice'], 100);
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                        $skuKey = "{$oilPriceTemp['oil_type']}{$oilPriceTemp['oil_name']}{$oilPriceTemp['oil_level']}";
                        $insertStationPriceData['extends'][$skuKey] = $cv;
                    }

                    $insertStationPriceData['platform_code'] = $this->platformCode;
                    $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                    $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                } else {
                    Log::handle("Oil's price data doesn't exist", [
                        "data" => $v
                    ], "星油(新)", "oil_station_data", "error");
                }
                if (empty($oilTemp['price_list'])) {
                    if (empty($stationData['price_list'])) {
                        $oilTemp['is_stop'] = 1;
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                    }
                }
                $insertStationPriceData['station_id'] = $v['id'];
                $oilTemp['clearGunCache'] = true;
                $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                StationPriceData::create($insertStationPriceData);
                BasicJob::pushStationToStationHub($oilTemp);
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "星油(新)", "oil_station_data", "error");
            }
        }

        $this->dealNotExistsStation($existsStationIds);
    }
}
