<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\GB as GBRequest;
use Throwable;


class PullOilForGB extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:gb {--name_abbreviation=}';
    protected $name      = 'pull oil for gb';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for gb
                                   -name_abbreviation: gb|gb_tj.';

    protected $nameAbbreviation = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle(?int $stationId = null, string $nameAbbreviation = '')
    {
        $this->nameAbbreviation = $nameAbbreviation;
        if ($this->input) {
            $this->nameAbbreviation = $this->option('name_abbreviation');
        }
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
        $page = 1;
        $totalPage = 0;
        $single = false;
        $existsStationIds = [];

        do {
            try {
                if (!empty($stationId)) {
                    $requestMethod = "station/$stationId";
                    $single = true;
                } else {
                    $requestMethod = "$page,50/station";
                }
                $data = GBRequest::handle($requestMethod, 'get', [], $this->nameAbbreviation)['resultData'];
                $totalPage = ceil($data['tc'] / 50);
                $regionCodes = [];
                $waitCoordinates = [];
                if (is_array($data['datas']) and checkIsAssocArray($data['datas'])) {
                    $data['datas'] = [$data['datas']];
                }
                foreach ($data['datas'] as &$ov) {
                    $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                    $ov['cityCode'] = "{$ov['cityCode']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data['datas'] as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes)) {
                        $waitCoordinates[] = "{$v["gdLng"]},{$v["gdLat"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['datas'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['merchantGasStationId'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $existsStationIds[] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['name'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;

                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["gdLng"]},{$v["gdLat"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["gdLng"]},{$v["gdLat"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['gdLng'];
                        $oilTemp['lat'] = $v['gdLat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = $v['status'] == 'online' ? 0 : 1;
                        $oilTemp['station_brand'] = 2;
                        $oilTemp['is_highway'] = $v['isHighSpeed'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = $v['status'] == 'online' ? 1 : 2;
                        $insertStationPriceData['extends'] = [];

                        //油站价格数据
                        $gunNumber = 0;
                        foreach ($v['products'] as $cv) {
                            $realOilInfo = explode("_", config("oil.oil_mapping.gb.{$cv['spec']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "固本能源", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(
                                                                  config("oil.oil_type")
                                                              )[$oilPriceTemp['oil_name']] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$oilPriceTemp['oil_type']] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config("oil.oil_level")
                                                               )[$oilPriceTemp['oil_level']] ?? '';

                            $oilPriceTemp['price'] = bcdiv($cv['price'], 100, 2);
                            $oilPriceTemp['mac_price'] = bcdiv($cv['price'], 100, 2);
                            $insertOilPriceTemp['gun_no'] = $gunNumber;
                            $insertOilPriceTemp['sale_price'] = $cv['price'];
                            $insertOilPriceTemp['listing_price'] = $cv['price'];
                            $insertOilPriceTemp['issue_price'] = 0;
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}";
                            $insertStationPriceData['extends'][$skuKey] = [
                                'spec' => $cv['spec'],
                            ];
                            $gunNumber++;
                        }

                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                        if (empty($oilTemp['price_list'])) {
                            continue;
                        }
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['merchantGasStationId'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                        ], "固本能源", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "固本能源", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        if (!$single) {
            $this->dealNotExistsStation($existsStationIds);
        }
    }
}
