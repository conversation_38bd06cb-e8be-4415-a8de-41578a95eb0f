<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\SH as SHRequest;
use Throwable;


class PullOilForSh extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:sh';
    protected $name      = 'pull oil for sh';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for sh';

    protected $nameAbbreviation = 'sh';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 0;
        $hasNext = false;
        $existsStationIds = [];
        do {
            try {
                $data = SHRequest::handle("api/channel/stationList", [
                    "limit" => 199,
                    "page"  => $page,
                ], 'post');
                $hasNext = $data['hasNext'];
                $waitCoordinates = [];
                foreach ($data['data'] as $v) {
                    $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                }
                $regionData = getRegionForCoordinateByGdApi($waitCoordinates);

                foreach ($data['data'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['id'] = $v['id'];
                        $oilTemp['assoc_id'] = $v['id'];
                        $existsStationIds[] = $v['id'];
                        $oilTemp['station_name'] = $v['title'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = 2;
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['station_brand'] = config(
                            'brand.' . $this->nameAbbreviation . '.' . $v['oil_brand'],
                            8
                        );
                        if (!empty($v['open_start']) or !empty($v['open_end'])) {
                            $oilTemp['business_hours'] = "{$v['open_start']}~{$v['open_end']}";
                        }
                        $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['lng'];
                        $oilTemp['lat'] = $v['lat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }
                        $oilTemp['contact'] = '刘荣虎';
                        $oilTemp['contact_phone'] = '18301509835';
                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = 0;
                        $oilTemp['price_list'] = [];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        //如果油站油品价格数据不存在则舍弃该数据
                        //油站价格数据
                        // 经善宏确认，安徽站点目前不提供油枪数据，需自行模拟
                        if ($oilTemp['province_code'] == '340000000000' and empty($v['gun'])) {
                            $v['gun'] = [
                                [
                                    'oil' => [
                                        'id' => getLaravelAndMachineEnv("RUN__ENVIRONMENT") != 'prod' ? 2 : 2,
                                    ],
                                ]
                            ];
                        }
                        $checkOilRepeat = [];
                        foreach ($v['gun'] as $cv) {
                            if (in_array($cv['oil']['id'], $checkOilRepeat)) {
                                continue;
                            }
                            $realOilInfo = explode(
                                "_",
                                config(
                                    "oil.oil_mapping.sh.{$cv['oil']['id']}",
                                    ''
                                )
                            );
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "善宏", "oil_station_data", "error");
                                continue;
                            }
                            $oilPriceTemp['price'] = 0;
                            $insertOilPriceTemp['oil_type'] = array_flip(
                                                                  config("oil.oil_type")
                                                              )[$oilPriceTemp['oil_name']] ?? '';
                            $insertOilPriceTemp['oil_no'] = str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(
                                    config("oil.oil_no")
                                )[$oilPriceTemp['oil_type']] ?? ''
                            );
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config("oil.oil_level")
                                                               )[$oilPriceTemp['oil_level']] ?? '';
                            $oilPriceTemp['price'] = 0;
                            $oilPriceTemp['mac_price'] = 0;
                            $insertOilPriceTemp['sale_price'] = 0;
                            $insertOilPriceTemp['listing_price'] = 0;
                            $insertOilPriceTemp['issue_price'] = 0;
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            $checkOilRepeat[] = $cv['oil']['id'];
                        }
                        if (empty($oilTemp['price_list'])) {
                            $oilTemp['is_stop'] = 1;
                        }
                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        $insertStationPriceData['station_id'] = $v['id'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "善宏", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "善宏", "oil_station_data", "error");
            }
        } while ($hasNext);

        $this->dealNotExistsStation($existsStationIds);
    }
}
