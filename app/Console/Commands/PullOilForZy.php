<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use App\Models\Logic\Station\Receive\ZY as ZYReceive;
use Request\ZY as ZYRequest;
use Throwable;


class PullOilForZy extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:zy';
    protected $name      = 'pull oil for zy';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for zy';

    protected $nameAbbreviation    = 'zy';
    protected $subNameAbbreviation = 'zyLng';

    protected $subPlatformCode = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
        $this->subPlatformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->subNameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {
            try {
                $data = ZYRequest::handle("station.search", [
                    "rows" => 100,
                    "page" => $page,
                ])['data'];
                $totalPage = ceil($data['total'] / 100);
                $regionCodes = [];
                $waitCoordinates = [];

                foreach ($data['list'] as &$ov) {
                    $ov['province'] = "{$ov['province']}000000";
                    $ov['city'] = "{$ov['city']}000000";
                    $regionCodes[] = $ov['province'];
                    $regionCodes[] = $ov['city'];
                }

                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));

                foreach ($data['list'] as $v) {
                    if (!in_array($v['province'], $existsRegionCodes) or !in_array($v['city'], $existsRegionCodes)) {
                        $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                    }
                }

                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['list'] as $v) {
                    //油站数据
                    $oilTemp = [];
                    $oilTemp['price_list'] = [];
                    $oilTemp['id'] = $v['stationId'];
                    $oilTemp['assoc_id'] = $oilTemp['id'];
                    $existsStationIds[] = $oilTemp['id'];
                    $oilTemp['station_name'] = $v['stationName'];
                    $oilTemp['name_abbreviation'] = $this->nameAbbreviation;

                    if (!in_array($v['province'], $existsRegionCodes) or !in_array($v['city'], $existsRegionCodes)) {
                        $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                    } else {
                        $oilTemp['province_code'] = $v['province'];
                        $oilTemp['city_code'] = $v['city'];
                    }
                    if (checkIsMunicipality($oilTemp['city_code'])) {
                        $oilTemp['province_code'] = $oilTemp['city_code'];
                    }

                    $oilTemp['lng'] = $v['lng'];
                    $oilTemp['lat'] = $v['lat'];
                    if (strpos($oilTemp['lng'], '.') !== false) {
                        $dealLng = explode('.', $oilTemp['lng']);
                        $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                    }
                    if (strpos($oilTemp['lat'], '.') !== false) {
                        $dealLat = explode('.', $oilTemp['lat']);
                        $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                    }

                    $oilTemp['address'] = $v['address'];
                    $oilTemp['is_stop'] = $v['status'] == 'online' ? 0 : 1;
                    $oilTemp['business_hours'] = $v['opentime'];
                    $insertStationPriceData = $oilTemp;
                    $insertStationPriceData['enabled_state'] = $v['status'] == 'online' ? 1 : 2;
                    $insertStationPriceData['extends'] = [];
                    //如果油站油品价格数据不存在则舍弃该数据
                    if (isset($v['guns']) and is_array($v['guns'])) {
                        $skuCodes = array_unique(array_column($v['guns'], 'skuCode'));
                        $onlyLngFlag = (in_array("T100L01", $skuCodes) and count($skuCodes) == 1);
                        $checkOilRepeat = [];

                        //油站价格数据
                        foreach ($v['guns'] as $cv) {
                            if (in_array($cv['skuCode'], $checkOilRepeat)) {
                                continue;
                            }
                            if (!$onlyLngFlag and $cv['skuCode'] == 'T100L01') {
                                continue;
                            }

                            $oilInfo = explode(" ", $cv['skuName']);
                            $realOilInfo = explode("_", config("oil.oil_mapping.zy.{$cv['skuCode']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "找油", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_no'] = count($oilInfo) > 1 ? $oilInfo[0] : '';
                            $insertOilPriceTemp['oil_level'] = $oilInfo[2] ?? '';
                            $insertOilPriceTemp['oil_type'] = count($oilInfo) > 1 ? $oilInfo[1] :
                                ZYReceive::$specialOilNameMapping[$oilInfo[0]];

                            $oilPriceTemp['price'] = $cv['lvPrice'];
                            $oilPriceTemp['mac_price'] = $cv['listedPrice'];
                            $insertOilPriceTemp['gun_no'] = $cv['gunId'];
                            $insertOilPriceTemp['sale_price'] = bcmul($cv['lvPrice'], 100);
                            $insertOilPriceTemp['listing_price'] = bcmul($cv['listedPrice'], 100);
                            $insertOilPriceTemp['issue_price'] = bcmul($cv['basePrice'], 100);
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            $skuKey = "{$insertOilPriceTemp['oil_no']}{$insertOilPriceTemp['oil_type']}{$insertOilPriceTemp['oil_level']}_skuCode";
                            $insertStationPriceData['extends'][$skuKey] = $cv['skuCode'];
                            $checkOilRepeat[] = $cv['skuCode'];
                        }

                        $insertStationPriceData['platform_code'] = $onlyLngFlag ? $this->subPlatformCode :
                            $this->platformCode;
                        $oilTemp['name_abbreviation'] = $onlyLngFlag ? $this->subNameAbbreviation :
                            $this->nameAbbreviation;
                        $insertStationPriceData['extends'] = json_encode($insertStationPriceData['extends']);
                    } else {
                        Log::handle("Oil's price data doesn't exist", [
                            "data" => $v
                        ], "找油", "oil_station_data", "error");
                    }
                    if (empty($oilTemp['price_list'])) {
                        continue;
                    }
                    $oilTemp['trade_type'] = 1;
                    if (isset($v['sellType'])) {
                        if ($onlyLngFlag) {
                            if (in_array('userss', $v['sellType'])) {
                                $oilTemp['trade_type'] = 3;
                            } elseif (in_array('oilerqr', $v['sellType'])) {
                                $oilTemp['trade_type'] = 1;
                            } else {
                                $oilTemp['is_stop'] = 1;
                            }
                        } elseif (in_array('oilerqr', $v['sellType'])) {
                            $oilTemp['trade_type'] = 1;
                        } elseif (in_array('userss', $v['sellType'])) {
                            $oilTemp['trade_type'] = 3;
                        } else {
                            $oilTemp['is_stop'] = 1;
                        }
                    }

                    $insertStationPriceData['station_id'] = $v['stationId'];
                    $oilTemp['clearGunCache'] = true;
                    $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                    StationPriceData::create($insertStationPriceData);
                    BasicJob::pushStationToStationHub($oilTemp);
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "找油", "oil_station_data", "error");

                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);

        $this->dealNotExistsStation($existsStationIds);
    }
}
