<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\TBJX as TBJXRequest;
use Throwable;


class PullOilForTBJX extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:tbjx';
    protected $name      = 'pull oil for tbjx';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for tbjx';

    protected $nameAbbreviation = 'tbjx';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $existsStationIds = [];
        $data = TBJXRequest::handle('query', [
            'serviceType' => 'SITE_QUERY',
        ])['data'];
        $waitCoordinates = [];
        foreach ($data as $v) {
            $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
        }
        $regionData = [];
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }
        $stationCacheIds = array_flip(app('redis')->hkeys(
            self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation
        ));
        foreach ($data as $v) {
            try {
                //油站数据
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['id'] = $v['id'];
                $oilTemp['is_stop'] = 0;
                $oilTemp['assoc_id'] = $v['id'];
                $existsStationIds[] = $v['id'];
                $oilTemp['station_name'] = $v['name'];
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['station_type'] = 2;
                $oilTemp['trade_type'] = 3;
                $oilTemp['station_brand'] = 8;
                $oilTemp['station_oil_unit'] = 1;
                $oilTemp['allow_switch_unit'] = 0;
                $oilTemp['business_hours'] = "24小时";
                $oilTemp['contact'] = $v['contact_user'];
                $oilTemp['contact_phone'] = $v['contact_phone'] ?? '';

                $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }
                $oilTemp['lng'] = $v['longitude'];
                $oilTemp['lat'] = $v['latitude'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                if (!isset($stationCacheIds[$v['id']])) {
                    $oilTemp['address'] = $v['address_desc'];
                }
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['oil_price_list'] = [];
                if (isset($v['site_oil_code']) and is_array($v['site_oil_code'])) {
                    //油站价格数据
                    foreach ($v['site_oil_code'] as $cv) {
                        $realOilInfo = explode(
                            "_",
                            config(
                                "oil.oil_mapping.tbjx.{$cv['name']}"
                            )
                        );
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                        $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                        $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                        if (empty($oilPriceTemp['oil_name'])) {
                            continue;
                        }
                        $insertOilPriceTemp['oil_type'] = array_flip(
                                                              config("oil.oil_type")
                                                          )[$oilPriceTemp['oil_name']] ?? '';
                        $insertOilPriceTemp['oil_no'] = str_replace(
                            $insertOilPriceTemp['oil_type'],
                            '',
                            array_flip(
                                config("oil.oil_no")
                            )[$oilPriceTemp['oil_type']] ?? ''
                        );
                        $insertOilPriceTemp['oil_level'] = array_flip(
                                                               config("oil.oil_level")
                                                           )[$oilPriceTemp['oil_level']] ?? '';

                        $oilPriceTemp['price'] = bcdiv($cv['price'], 100, 2);
                        $oilPriceTemp['mac_price'] = bcdiv($cv['price'], 100, 2);
                        $insertOilPriceTemp['sale_price'] = $cv['price'];
                        $insertOilPriceTemp['listing_price'] = $cv['price'];
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                    }
                    $insertStationPriceData['platform_code'] = $this->platformCode;
                } else {
                    Log::handle("Oil's price data doesn't exist", [
                        "data" => $v
                    ], "通宝吉祥", "oil_station_data", "error");
                }
                if (empty($oilTemp['price_list'])) {
                    $oilTemp['is_stop'] = 1;
                }
                $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                $insertStationPriceData['station_id'] = $v['id'];
                StationPriceData::create($insertStationPriceData);
                BasicJob::pushStationToStationHub($oilTemp);
            } catch (Throwable $throwable) {
                Log::handle("Push station failed", [
                    'exception'   => $throwable,
                    'stationData' => $v,
                ], "通宝吉祥", "oil_station_data", "error");
            }
        }
        $this->dealNotExistsStation($existsStationIds);
    }
}
