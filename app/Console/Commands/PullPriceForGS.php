<?php

namespace App\Console\Commands;


use App\Jobs\SendPullPriceInfoForGS;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;

class PullPriceForGS extends PullPrice
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-price:gs';
    protected $name = "pull price for gs";

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull price for gs';

    protected $nameAbbreviation = 'gs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation($this->nameAbbreviation)['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $station = FOSS_STATIONRequest::handle('v1/station/getAppStationIdByPcode', [
            'pcode' => $this->platformCode
        ]);
        if (empty($station['data'])) {

            Log::handle("The gs site doesn't exist in the site center", [
                'exception' => $station,
            ], '港森', 'oil_station_data', 'error');
            return;
        }

        $station = convertListToDict($station['data'], 'app_station_id');
        $stationIds = array_filter(array_keys($station));
        $stationCount = count($stationIds);
        $stationPage = ceil($stationCount / 99);

        for ($i = 0; $i < $stationPage; $i++) {

            try {

                $stationIdsTemp = array_slice($stationIds, $i * 99, 99);
                Queue::push(new SendPullPriceInfoForGS($stationIdsTemp), '', 'supplier_station_queue');
            } catch (Throwable $exception) {

                Log::handle("Get failed about the station's price of gs", [
                    "stationIds" => $stationIds,
                    'exception'  => json_decode(Log::getMessage([
                        'exception' => $exception,
                    ]), true)['exception'],
                ], '港森', 'oil_station_data', 'error');
            }
        }
    }
}
