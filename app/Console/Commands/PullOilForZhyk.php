<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\ZHYK as ZHYKRequest;
use Throwable;


class PullOilForZhyk extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:zhyk';
    protected $name      = 'pull oil for zhyk';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for zhyk';

    protected $nameAbbreviation = 'zhyk';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle(array $parameters = [])
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];
        $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation;
        do {
            try {
                $requestData = [
                    "pageNo"   => $page,
                    "pageSize" => 100,
                ];
                $data = ZHYKRequest::handle('station/stationlist.action', $requestData, 'get');
                $totalPage = ceil($data['totalCount'] / 100);
                $regionCodes = [];
                $waitCoordinates = [];
                foreach ($data['data'] as &$ov) {
                    $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                    $ov['cityCode'] = "{$ov['cityCode']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data['data'] as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes) and !empty($v['provinceCode']) and
                                                                          !empty($v['cityCode'])) {
                        $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['data'] as $v) {
                    try {
                        //油站数据
                        $existsStationIds[] = $v['stationCode'];
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['stationCode'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['stationName'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = 2;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['business_hours'] = "24小时";

                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['longitude'];
                        $oilTemp['lat'] = $v['latitude'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = 0;
                        $oilTemp['station_brand'] = config('brand.zhyk')[$v['brand']] ?? 8;
                        $oilTemp['contact'] = $v['leaderName'];
                        $oilTemp['contact_phone'] = $v['leaderPhone'];
                        $oilTemp['trade_type'] = 3;
                        $cacheOilTemp = json_decode(
                            app('redis')->hget($oilStationCacheCheckKey, $v['stationCode']),
                            true
                        );
                        if ($v['refuelMode'] == 3) {
                            if (empty($cacheOilTemp)) {
                                continue;
                            }
                            $oilTemp['trade_type'] = 1;
                            $oilTemp['is_stop'] = 1;
                        }
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        $insertStationPriceData['oil_price_list'] = [];
                        //油站价格数据
                        $stationOilPrice = [];
                        $oilGunNos = [];
                        foreach ($v['gunList'] as $cv) {
                            if (!isset($stationOilPrice[$cv['plateOilCode']])) {
                                $stationOilPrice[$cv['plateOilCode']] = [];
                                $oilGunNos[$cv['plateOilCode']] = [];
                            }
                            array_push($oilGunNos[$cv['plateOilCode']], ...explode(',', $cv['gunNo']));
                            $stationOilPrice[$cv['plateOilCode']][] = $cv['oilPrice'] . '_' . $cv['discountOilPrice'];
                            $stationOilPrice[$cv['plateOilCode']] = array_unique($stationOilPrice[$cv['plateOilCode']]);
                        }
                        foreach ($v['gunList'] as $cv) {
                            if (isset($stationOilPrice[$cv['plateOilCode']]) and count(
                                                                                     $stationOilPrice[$cv['plateOilCode']]
                                                                                 ) > 1) {
                                if (empty($cacheOilTemp)) {
                                    continue 2;
                                }
                                $oilTemp['is_stop'] = 1;
                            }
                            $realOilInfo = explode("_", config("oil.oil_mapping.zhyk.{$cv['plateOilCode']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "智慧油客", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$realOilInfo[1]] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config('oil.oil_level')
                                                               )[$realOilInfo[2]] ?? '';

                            $oilPriceTemp['price'] = bcdiv($cv['discountOilPrice'], 100, 2);
                            $oilPriceTemp['mac_price'] = bcdiv($cv['oilPrice'], 100, 2);
                            $insertOilPriceTemp['gun_no'] = implode(',', $oilGunNos[$cv['plateOilCode']]);
                            $insertOilPriceTemp['sale_price'] = $cv['discountOilPrice'];
                            $insertOilPriceTemp['listing_price'] = $cv['oilPrice'];
                            $insertOilPriceTemp['issue_price'] = $cv['NDRCOilPrice'] ?? 0;
                            $oilTemp['price_list'][$cv['plateOilCode']] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][$cv['plateOilCode']] = $insertOilPriceTemp;
                        }
                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        if (empty($oilTemp['price_list'])) {
                            continue;
                        }
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['stationCode'];
                        $oilTemp['price_list'] = array_values($oilTemp['price_list']);
                        unset($insertStationPriceData['price_list']);
                        $insertStationPriceData['oil_price_list'] = array_values(
                            $insertStationPriceData['oil_price_list']
                        );
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                            'station'   => $v,
                        ], "智慧油客", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "智慧油客", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        $this->dealNotExistsStation($existsStationIds);
    }
}
