<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\Mail;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Request\KL as KLRequest;
use Throwable;

class PullBillForKl extends PullBill
{
    protected $nameAbbreviation = 'kl';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:kl {--billDate=}';
    protected $name = 'pull bill for kl';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill for kl';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/9 5:05 下午
     */
    public function handle()
    {
        $billDate = $this->option("billDate");
        if (empty($billDate)) {

            $billDate = date("Y-m-d", strtotime('-1 day'));
        }
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => 'kl',
            ],
        ], ['*'], false);
        $billData = [];
        do {

            $response = KLRequest::handle("orders/", [
                'order_no' => implode(',', array_column($orderInfo, 'platform_order_id'))
            ], true, 'get', [
                'Expect:'
            ]);
            $dataUrl = $response['next'] ?? '';
            if (array_has($response, 'results') and is_array($response['results'])) {

                array_push($billData, ...$response['results']);
            } else {

                Log::handle('Bill of kl is empty.', $response,
                    DockingPlatformInfoData::getPlatformNameByNameAbbreviation('kl'),
                    'pull_bill', 'info');
            }
        } while (!empty($dataUrl));
        // write excel header
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $titCol = 'A';
        $title = [
            'G7订单编号' => 'g7_order',
            '昆仑订单编号' => 'order_no',
            '商品名称'   => 'title',
            '商品分类'   => 'cate_name',
            '商品规格'   => 'model',
            '单位'     => 'offer_unit',
            '数量'     => 'num',
            '单价'     => 'modelprice',
            '商品总价'   => 'total_fakeprice',
            '优惠值'    => 'total_discountprice',
            '实付金额'   => 'totalprice',
            '物流单号'   => 'logistics_no',
            '物流状态'   => 'logistics_status',
            '发票类型'   => 'invoice_type',
            'G7用户卡号' => 'g7_card_no',
            '下单时间'   => 'date',
            '订单状态'   => 'status',
        ];
        foreach ($title as $key => $value) {

            $sheet->getStyle($titCol . '1')->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical'   => Alignment::VERTICAL_CENTER,
                ],
            ]);
            $sheet->setCellValue($titCol . '1', $key);
            $titCol++;
        }
        // write excel body
        $row = 2;
        foreach ($billData as &$item) {
            $dataCol = 'A';
            $item['date'] = date("Y-m-d H:i:s", strtotime($item['date']));

            foreach ($title as $value) {

                $sheet->setCellValue($dataCol . $row, $item[$value] ?? '');
                $sheet->getStyle($dataCol . $row)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);
                if (is_numeric($item[$value] ?? '')) {

                    if (strpos($item[$value], '.') !== false) {

                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    } else {

                        $sheet->getCell($dataCol . $row)->setValueExplicit($item[$value]
                            ?? '', DataType::TYPE_STRING);
                    }
                }
                if (in_array($value, [
                    'title',
                    'cate_name',
                    'model',
                    'offer_unit',
                    'logistics_status',
                    'invoice_type',
                    'status',
                ])) {

                    $sheet->getColumnDimension($dataCol)
                          ->setWidth(mb_strlen($item[$value] ?? '') * 17 / 5);
                } else {

                    $sheet
                        ->getColumnDimension($dataCol)
                        ->setAutoSize(true);
                }
                $sheet
                    ->getColumnDimension($dataCol)
                    ->setAutoSize(true);
                $dataCol++;
            }

            $row++;
        }
        // Calculate email body data
        $tradeCount = 0;
        $tradeTotal = 0;
        foreach ($billData as $v) {

            if ($v['date'] >= "$billDate 00:00:00" and $v['date'] <= "$billDate 23:59:59") {

                $tradeCount += 1;
                $tradeTotal = bcadd($tradeTotal, $v['totalprice'], 2);
            }
        }
        $filePath = resource_path("/downloads/bill/" . date("Ymd", strtotime(
                "$billDate 00:00:00")) . "昆仑润滑油账单.xlsx");
        $writer = IOFactory::createWriter($spreadsheet, "Xlsx");
        $writer->save($filePath);
        Mail::send("emails/bill/simple", [
            "billDate"     => date("Ymd", strtotime("$billDate 00:00:00")),
            "tradeCount"   => $tradeCount,
            "tradeTotal"   => $tradeTotal,
            "platformName" => "昆仑润滑油",
            "remark"       => "此账单为昆仑润滑油通过接口提供，次日8:00发送。详细账单请见附件。",
        ], function ($message) use ($filePath, $billDate) {
            $message->subject(date("Ymd", strtotime("$billDate 00:00:00")) . "昆仑润滑油账单");
            $message->to(json_decode(AuthConfigData::getAuthConfigValByName("BILL_MAIL_RECEIVE"),
                true));
            $message->attach($filePath);
        });
        @unlink($filePath);
    }
}
