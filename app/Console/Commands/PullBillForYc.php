<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Illuminate\Support\Facades\Mail;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Request\YC as YCRequest;
use Throwable;

class PullBillForYc extends PullBill
{
    protected $nameAbbreviation = 'yc';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-bill:yc {--billDate=}';
    protected $name = 'pull bill for yc';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull bill for yc';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/9 5:05 下午
     */
    public function handle()
    {
        $billDate = $this->option("billDate");

        if (empty($billDate)) {

            $billDate = date("Ymd", strtotime('-1 day'));
        }

        $billData = YCRequest::handle("reconciliation/purchase", [
            "date" => $billDate,
        ]);

        if (!isset($billData['data'])) {

            Log::handle('Bill of yc is empty.', $billData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('yc'),
                'pull_bill', 'info');
            return;
        }

        if (!is_array($billData['data'])) {

            Log::handle('Bill of yc is empty.', $billData,
                DockingPlatformInfoData::getPlatformNameByNameAbbreviation('yc'),
                'pull_bill', 'info');
            return;
        }

        $platformOrderIds = array_column($billData['data'], 'orderID');
        $orderAssocData = convertListToDict(OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => 'in',
                'value'    => $platformOrderIds,
            ],
        ], [
            'self_order_id',
            'extend',
            'platform_order_id'
        ], false), 'platform_order_id');

        foreach ($billData['data'] as &$v) {

            if (isset($v['orderID'])) {

                if (isset($orderAssocData[$v['orderID']])) {

                    $extend = json_decode($orderAssocData[$v['orderID']]['extend'], true);
                    $v['orderSn'] = $extend["owner"]["trades_no"];
                    $v['oil_time'] = $extend['owner']['trade_time'];
                    $v['money'] = $extend['owner']['trade_money'];
                    $v['oil_num'] = $extend['owner']['trade_num'];
                    $v['price'] = $extend['owner']['trade_price'];
                    $v['oil_title'] = $extend['owner']['oil_name'];
                    $v['station_name'] = $extend['owner']['trade_place'];
                }
            }
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $titCol = 'A';
        $title = [
            '广东壳牌流水号'    => 'fleetOrderID',
            '油站Id'       => 'stationID',
            '油站名称'       => 'station_name',
            '油品名称'       => 'oilNo',
            '交易时间'       => 'transactionTime',
            '单价'         => 'price',
            '数量'         => 'litre',
            '交易金额'       => 'amount',
            'G7能源订单号'    => 'orderSn',
            'G7能源司机实付金额' => 'money',
        ];

        foreach ($title as $key => $value) {

            $sheet->getStyle($titCol . '1')->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical'   => Alignment::VERTICAL_CENTER,
                ],
            ]);
            $sheet->setCellValue($titCol . '1', $key);
            $titCol++;
        }

        $row = 2;

        foreach ($billData['data'] as $item) {
            $dataCol = 'A';

            foreach ($title as $value) {

                $sheet->getStyle($dataCol . $row)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                if (is_numeric($item[$value])) {

                    if (strpos($item[$value], '.') !== false) {

                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    } else {

                        $sheet
                            ->getStyle($dataCol . $row)
                            ->getNumberFormat()
                            ->setFormatCode(NumberFormat::FORMAT_NUMBER);
                    }
                }

                $sheet
                    ->getColumnDimension($dataCol)
                    ->setAutoSize(true);
                $sheet->setCellValue($dataCol . $row, $item[$value] ?? '');
                $dataCol++;
            }

            $row++;
        }

        $filePath = resource_path("/downloads/bill/" . $billDate . '广东壳牌账单.xlsx');
        $writer = IOFactory::createWriter($spreadsheet, "Xlsx");
        $writer->save($filePath);
        Mail::send("emails/bill/simple", [
            "billDate"     => $billDate,
            "tradeCount"   => $billData['totalNumber'],
            "tradeTotal"   => $billData['totalAmount'],
            "platformName" => "广东壳牌",
            "remark"       => "此账单为广东壳牌通过接口提供，次日8:00发送。详细账单请见附件。",
        ], function ($message) use ($filePath, $billDate) {
            $message->subject("{$billDate}广东壳牌账单");
            $message->to(json_decode(AuthConfigData::getAuthConfigValByName("BILL_MAIL_RECEIVE"),
                true));
            $message->attach($filePath);
        });
        @unlink($filePath);
    }
}
