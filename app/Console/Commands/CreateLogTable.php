<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Data\Log\QueueLog as Log;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PDO;
use Throwable;

class CreateLogTable extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'log-table:create {type}';
    protected $allowType = [
        'queue',
        'response',
        'receive',
        'request',
        'system',
    ];

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'create log table.
                                  -type: queue|response|receive|request|system.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $type = $this->argument("type");

        if (!in_array($type, $this->allowType)) {

            throw new Exception("the type is invalid");
        }

        $currentCarbon = Carbon::now();
        $currentYear = $currentCarbon->yearIso;
        $currentWeek = $currentCarbon->weekOfYear;
        $nextWeekCarbon = $currentCarbon->addWeek();
        $nextYear = $nextWeekCarbon->yearIso;
        $nextWeek = $nextWeekCarbon->weekOfYear;
        $nextNWeekCarbon = $currentCarbon->addWeek();
        $nextNYear = $nextNWeekCarbon->yearIso;
        $nextNWeek = $nextNWeekCarbon->weekOfYear;
        $connection = DB::connection("mysql");
        $tablePrefix = $connection->getTablePrefix();
        $pdo = $connection->getPdo();
        $curTable = "$tablePrefix{$type}_log_{$currentYear}_" . getZeroPrefixNumber($currentWeek);
        $nextTable = "$tablePrefix{$type}_log_{$nextYear}_" . getZeroPrefixNumber($nextWeek);
        $nextNTable = "$tablePrefix{$type}_log_{$nextNYear}_" . getZeroPrefixNumber($nextNWeek);
        $this->createTable($pdo, $curTable, $nextTable);
        $this->createTable($pdo, $curTable, $nextNTable);
    }

    public static function createTable(PDO $pdo, string $oldTable, string $newTable)
    {
        Log::handle("Create $newTable table started", func_get_args(), "系统",
            "create_table", "info");
        $count = $pdo->query("select * from `information_schema`.`TABLES` where `TABLE_NAME` = '$newTable'")
                     ->rowCount();

        if ($count <= 0) {

            try {

                $pdo->exec("create table $newTable like $oldTable");
            } catch (Throwable $e) {

                Log::handle("Create $newTable table failed", func_get_args(), "系统",
                    "create_table", "error");
            }
        }

        Log::handle("Create $newTable table finished", func_get_args(), "系统",
            "create_table", "info");
    }
}
