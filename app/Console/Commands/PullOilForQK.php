<?php
// 汽开
namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\QK as QKRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class PullOilForQK extends PullOil
{
    public static $payTypeMapping = [
        1 => 1,
        2 => 3,
    ];
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:qk';
    protected $name      = 'pull oil for qk';
    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for qk.';
    protected $nameAbbreviation = 'qk';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {
            try {
                $data = QKRequest::handle('dispense.pullOilstationList', [
                    'currentPage' => $page,
                    'pageSize'    => 100,
                ])['data'];
                $totalPage = ceil($data['total'] / 100);
                $regionCodes = [];
                $waitCoordinates = [];
                foreach ($data['resultList'] as &$ov) {
                    $ov['provinceCode'] = "{$ov['province']}000000";
                    $ov['cityCode'] = "{$ov['city']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data['resultList'] as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes)) {
                        $waitCoordinates[] = "{$v["lng"]},{$v["lat"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['resultList'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['stationId'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $existsStationIds[] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['stationName'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = 2;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['contact'] = '钟成磊';
                        $oilTemp['contact_phone'] = $v['tel'];
                        $oilTemp['business_hours'] = $v['openTime'];
                        $oilTemp['is_highway'] = $v['tagType'] == 'B' ? 1 : 0;
                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = $v['status'] == 'online' ? 0 : 1;
                        $oilTemp['station_brand'] = config(
                            "brand.{$this->nameAbbreviation}.{$v['stationBrand']}",
                            8
                        );
                        if (!isset(self::$payTypeMapping[$v['payType']])) {
                            continue;
                        }
                        $oilTemp['trade_type'] = self::$payTypeMapping[$v['payType']];
                        if ($oilTemp['trade_type'] == 1) {
                            $oilTemp['is_stop'] = 1;
                        }
                        if (!empty($v['isVerificationCode']) and $v['isVerificationCode'] == 1) {
                            $oilTemp['is_stop'] = 1;
                        }
                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["lng"]},{$v["lat"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }
                        $oilTemp['lng'] = $v['lng'];
                        $oilTemp['lat'] = $v['lat'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;

                        //油站价格数据
                        $guns = json_decode($v['guns'], true) ?? [];
                        $oilGun = [];
                        $stationOilPrice = [];
                        $gunMapping = [];
                        foreach ($guns as $cv) {
                            if (!isset($oilGun[$cv['skuCode']])) {
                                $oilGun[$cv['skuCode']] = [];
                            }
                            $oilGun[$cv['skuCode']][] = $cv['gunId'];
                            if (!isset($stationOilPrice[$cv['skuCode']])) {
                                $stationOilPrice[$cv['skuCode']] = [
                                    'marketPrice'   => [],
                                ];
                            }
                            $stationOilPrice[$cv['skuCode']]['marketPrice'][] = $cv['marketPrice'];
                            $stationOilPrice[$cv['skuCode']]['marketPrice'] = array_unique(
                                $stationOilPrice[$cv['skuCode']]['marketPrice']
                            );
                            if (!isset($gunMapping[$cv['skuCode']])) {
                                $gunMapping[$cv['skuCode']] = [];
                            }
                            $gunMapping[$cv['skuCode']][$cv['gunId']] = $cv['gunName'];
                        }
                        $oilRepeat = [];
                        foreach ($guns as $cv) {
                            if (in_array($cv['skuCode'], $oilRepeat)) {
                                continue;
                            }
                            $realOilInfo = explode(
                                "_",
                                config(
                                    "oil.oil_mapping.$this->nameAbbreviation.oil.{$cv['skuCode']}"
                                )
                            );
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "汽开", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(
                                                                  config("oil.oil_type")
                                                              )[$oilPriceTemp['oil_name']] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$oilPriceTemp['oil_type']] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config("oil.oil_level")
                                                               )[$oilPriceTemp['oil_level']] ?? '';
                            if (count($stationOilPrice[$cv['skuCode']]['marketPrice']) > 1) {
                                (new FeiShu())->stationOilRepeat([
                                    'platformName' => '汽开',
                                    'stationName'  => $oilTemp['station_name'],
                                    'oilName'      => $insertOilPriceTemp['oil_no'] .
                                                      $insertOilPriceTemp['oil_type'] .
                                                      $insertOilPriceTemp['oil_level'],
                                    'price' => implode(
                                        ',',
                                        $stationOilPrice[$cv['skuCode']]['marketPrice']
                                    ),
                                ]);
                                continue;
                            }
                            $oilPriceTemp['price'] = $cv['marketPrice'];
                            $oilPriceTemp['mac_price'] = $cv['marketPrice'];
                            $insertOilPriceTemp['gun_no'] = implode(',', $oilGun[$cv['skuCode']]);
                            $insertOilPriceTemp['sale_price'] = bcmul($cv['marketPrice'], 100);
                            $insertOilPriceTemp['listing_price'] = bcmul($cv['marketPrice'], 100);
                            $insertOilPriceTemp['issue_price'] = bcmul($cv['guidePrice'], 100);
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                            $oilRepeat[] = $cv['skuCode'];
                        }

                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        if (empty($oilTemp['price_list'])) {
                            $oilTemp['is_stop'] = 1;
                        }
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['stationId'];
                        $insertStationPriceData['extends'] = json_encode([
                            'gun_mapping' => $gunMapping,
                        ]);
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                        ], "汽开", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "汽开", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        $this->dealNotExistsStation($existsStationIds);
    }
}
