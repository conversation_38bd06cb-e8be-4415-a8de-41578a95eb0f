<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Models\Dao\OrderPushRecord;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\OrderAssoc;
use Illuminate\Console\Command;
use Throwable;
use Tool\Alarm\FeiShu;

class TradeResultCallbackCheck extends Command
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'check-order-push-status';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'check-order-push-status.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/3/3 10:35 下午
     */
    public function handle()
    {
        $waitCheckOrderData = OrderAssoc::getOrderInfoByWhere(
            [
                [
                    'field'    => 'platform_name_abbreviation',
                    'operator' => '=',
                    'value'    => 'fy',
                ],
                [
                    'field'    => 'push_time',
                    'operator' => '<=',
                    'value'    => date(
                        'Y-m-d H:i:s',
                        strtotime('-5 minute')
                    )
                ]
            ],
            ['*'],
            false,
            true
        );
        if (!$waitCheckOrderData or $waitCheckOrderData->count() < 0) {
            return 0;
        }
        $orderPushRecords = (new OrderPushRecord())->whereIn(
            'self_order_id',
            $waitCheckOrderData->pluck('self_order_id')->toArray()
        )->get()->keyBy('self_order_id');
        $alarmOrderInfos = [];
        foreach ($waitCheckOrderData as $v) {
            if (!isset($orderPushRecords[$v->self_order_id]) or
                $orderPushRecords[$v->self_order_id]['status'] != 1) {
                $alarmOrderInfos[] = $v;
            }
        }
        $platformInfos = convertListToDict(
            DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                array_column($alarmOrderInfos, 'platform_name_abbreviation')
            ),
            'name_abbreviation'
        );
        foreach ($alarmOrderInfos as $value) {
            (new FeiShu())->pushOrderStatusFailed([
                'platform_name'     => $platformInfos[$value->platform_name]['platform_name'],
                'self_order_id'     => $value->self_order_id,
                'platform_order_id' => $value->platform_order_id,
                'reason'            => '订单支付结果推送' .
                                       $platformInfos[$value->platform_name]['platform_name'] .
                                       '未成功',
                'push_time'         => $orderPushRecords[$value->self_order_id]['push_time'] ?? '',
            ]);
        }
        return 0;
    }
}
