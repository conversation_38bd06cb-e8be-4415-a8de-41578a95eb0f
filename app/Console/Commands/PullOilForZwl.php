<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\ZWL as ZWLRequest;
use Throwable;


class PullOilForZwl extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:zwl';
    protected $name      = 'pull oil for zwl';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for zwl';

    protected $nameAbbreviation = 'zwl';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $hasNext = true;
        $existsStationIds = [];

        do {
            try {
                $data = ZWLRequest::handle("station", [
                    "size" => 99,
                    "page" => $page,
                ], 'get')['result'];
                $hasNext = $data['has_next'];
                foreach ($data['items'] as $v) {
                    $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
                }
                $regionData = getRegionForCoordinateByGdApi($waitCoordinates);

                foreach ($data['items'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['id'] = $v['id'];
                        $oilTemp['assoc_id'] = $v['id'];
                        $existsStationIds[] = $v['id'];
                        $oilTemp['station_name'] = $v['name'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = 2;
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['allow_switch_unit'] = 0;
                        $oilTemp['station_brand'] = config(
                            'brand.' . $this->nameAbbreviation . '.' . $v['supplier_type']
                        );
                        $oilTemp['business_hours'] = "24小时";
                        $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['longitude'];
                        $oilTemp['lat'] = $v['latitude'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['address'];
                        $oilTemp['is_stop'] = (int)!$v['state'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['oil_price_list'] = [];
                        $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                        //如果油站油品价格数据不存在则舍弃该数据
                        //油站价格数据
                        foreach ($v['oil_type'] as $cv) {
                            $realOilInfo = explode("_", array_flip(config("oil.oil_mapping.zwl"))[$cv['id']] ?? '');
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "中物流", "oil_station_data", "error");
                                continue;
                            }
                            $oilPriceTemp['price'] = 0;
                            $insertOilPriceTemp['oil_type'] = array_flip(
                                                                  config("oil.oil_type")
                                                              )[$oilPriceTemp['oil_name']] ?? '';
                            $insertOilPriceTemp['oil_no'] = str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(
                                    config("oil.oil_no")
                                )[$oilPriceTemp['oil_type']] ?? ''
                            );
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config("oil.oil_level")
                                                               )[$oilPriceTemp['oil_level']] ?? '';
                            $oilPriceTemp['price'] = 0;
                            $oilPriceTemp['mac_price'] = 0;
                            $insertOilPriceTemp['sale_price'] = 0;
                            $insertOilPriceTemp['listing_price'] = 0;
                            $insertOilPriceTemp['issue_price'] = 0;
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                        }

                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        $insertStationPriceData['station_id'] = $v['id'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception' => $throwable,
                            'stationData' => $v,
                        ], "中物流", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "中物流", "oil_station_data", "error");

                if ($page == 1) {
                    throw $throwable;
                }
            }
        } while ($hasNext);

        $this->dealNotExistsStation($existsStationIds);
    }
}
