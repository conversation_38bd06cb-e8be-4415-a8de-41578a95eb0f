<?php

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\MTLSY as MTLSYRequest;
use Throwable;


class PullOilForMtlSy extends PullOil
{
    protected $nameAbbreviation = 'mtlSy';
    protected $platformCode     = '';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:mtlSy {--stationId=}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for mtlSy';

    protected $authData = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->authData = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        );
        $this->platformCode = $this->authData['role_code'] ?? '';
    }

    /**
     * @return void
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 4:23 下午
     */
    public function handle($parameters = [])
    {
        $existsStationIds = [];
        if (!isset($parameters['station_id'])) {
            $stationData = MTLSYRequest::handle("gasList", [])['data'];
        } else {
            array_push($existsStationIds, ...$this->getExistsStation());
            $existsStationIds = array_diff($existsStationIds, [$parameters['station_id']]);
            try {
                $stationData = [
                    MTLSYRequest::handle("gasById", [
                        'id' => $parameters['station_id'],
                    ], 20, 512, true)['data']
                ];
            } catch (Throwable $throwable) {
                // 因供应商要求403错误码固定为站点不向G7开放，需要停用站点
                if ($throwable->getCode() != 403) {
                    throw $throwable;
                }
                $stationData = [];
            }
        }
        $waitCoordinates = [];
        foreach ($stationData as $v) {
            $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
        }
        if ($waitCoordinates) {
            $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
        }
        $stopStations = [];
        foreach ($stationData as $v) {
            try {
                if ($v['payOilFeeType'] == 3) {
                    continue;
                }
                //油站数据处理
                $oilTemp = [];
                $oilTemp['price_list'] = [];
                $oilTemp['station_name'] = $v['stationName'];
                $oilTemp['station_type'] = $v['typeOfOilAndGas'] != 0 ? 2 : 4;
                $existsStationIds[] = $v['id'];
                $oilTemp['assoc_id'] = $v['id'];
                $oilTemp['lng'] = $v['longitude'];
                $oilTemp['lat'] = $v['latitude'];
                if (strpos($oilTemp['lng'], '.') !== false) {
                    $dealLng = explode('.', $oilTemp['lng']);
                    $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                }
                if (strpos($oilTemp['lat'], '.') !== false) {
                    $dealLat = explode('.', $oilTemp['lat']);
                    $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                }

                $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                if (checkIsMunicipality($oilTemp['city_code'])) {
                    $oilTemp['province_code'] = $oilTemp['city_code'];
                }

                $oilTemp['address'] = $v['address'];
                $oilTemp['is_stop'] = 0;
                $oilTemp['station_oil_unit'] = $v['payOilFeeType'];
                $oilTemp['allow_switch_unit'] = 0;
                $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                $oilTemp['business_hours'] = '24小时';
                $oilTemp['id'] = $v['id'];
                $oilTemp['trade_type'] = 3;
                $insertStationPriceData = $oilTemp;
                $insertStationPriceData['enabled_state'] = $oilTemp['is_stop'] + 1;
                $insertStationPriceData['platform_code'] = $this->platformCode;
                $insertStationPriceData['station_id'] = $v['id'];
                //如果油站油品价格数据不存在则舍弃该数据
                if (isset($v['list']) and is_array($v['list'])) {
                    //油站价格数据
                    foreach ($v['list'] as $cv) {
                        $realOilInfo = explode(
                            "_",
                            config("oil.oil_mapping.$this->nameAbbreviation.oil.{$cv['moldType']}", "")
                        );
                        $oilPriceTemp = [];
                        $insertOilPriceTemp = [];
                        $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? '';
                        $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? '';
                        $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? '';
                        if (empty($oilPriceTemp['oil_name'])) {
                            Log::handle(
                                "Oil's attribute is invalid",
                                [
                                    "data" => $v
                                ],
                                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                                    $this->nameAbbreviation,
                                    "platform_name",
                                    ""
                                )['platform_name'],
                                "oil_station_data",
                                "error"
                            );
                            continue;
                        }
                        $insertOilPriceTemp['oil_type'] = array_flip(config("oil.oil_type"))[$realOilInfo[0]] ?? '';
                        $insertOilPriceTemp['oil_no'] = str_replace(
                            $insertOilPriceTemp['oil_type'],
                            '',
                            array_flip(config("oil.oil_no"))[$realOilInfo[1]] ?? ''
                        );
                        $insertOilPriceTemp['oil_level'] = array_flip(config("oil.oil_level"))[$realOilInfo[2]] ?? '';
                        $insertOilPriceTemp['gun_no'] = $cv['gunNumber'];
                        $oilPriceTemp['price'] = $cv['paidUnitPrice'];
                        $oilPriceTemp['mac_price'] = $cv['listingPrice'];
                        $insertOilPriceTemp['sale_price'] = bcmul(100, $cv['paidUnitPrice']);
                        $insertOilPriceTemp['listing_price'] = bcmul(100, $cv['listingPrice']);
                        $oilTemp['price_list'][] = $oilPriceTemp;
                        $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                    }
                } else {
                    Log::handle(
                        "Oil's price data doesn't exist",
                        [
                            "data" => $v
                        ],
                        DockingPlatformInfoData::getFieldsByNameAbbreviation(
                            $this->nameAbbreviation,
                            "platform_name",
                            ""
                        )['platform_name'],
                        "oil_station_data",
                        "error"
                    );
                }
                if (empty($oilTemp['price_list'])) {
                    $stopStations[] = $v['id'];
                    continue;
                }
                $oilTemp['clearGunCache'] = true;
                $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                BasicJob::pushStationToStationHub($oilTemp);
                StationPriceData::create($insertStationPriceData);
            } catch (Throwable $exception) {
                Log::handle(
                    "Pull station failed",
                    [
                        'stationData' => $v,
                        'exception'   => json_decode(
                                             Log::getMessage([
                                                 'exception' => $exception,
                                             ]),
                                             true
                                         )['exception'],
                    ],
                    DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->nameAbbreviation,
                        "platform_name",
                        ""
                    )['platform_name'],
                    "oil_station_data",
                    "error"
                );
            }
        }
        $this->dealNotExistsStation($existsStationIds);
        if (!empty($stopStations)) {
            $this->stopStation($stopStations);
        }
    }
}
