<?php
// 鲸启

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Data\StationPrice as StationPriceData;
use Request\JQ as JQRequest;
use Throwable;


class PullOilForJQ extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:jq';
    protected $name      = 'pull oil for jq';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for jq';

    protected $nameAbbreviation = 'jq';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle(?array $stationId = [])
    {
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];

        do {
            try {
                if (!empty($stationId)) {
                    $existsStationIdsByDB = $this->getExistsStation();
                    if (!empty($existsStationIdsByDB)) {
                        array_push($existsStationIds, ...$existsStationIdsByDB);
                    }
                    $existsStationIds = array_diff($existsStationIds, $stationId);
                    $data = JQRequest::handle('fleet/services/queryStationByStationId', 'get', [
                        'stationId' => implode(',', $stationId)
                    ])['result'];
                    $totalPage = 1;
                } else {
                    $responseData = JQRequest::handle(
                        'fleet/services/queryStation',
                        'get',
                        [
                            'pageIndex' => $page,
                            'pageSize'  => 100,
                        ]
                    )['result'];
                    $data = $responseData['stationDtos'];
                    $totalPage = ceil($responseData['totalCount'] / 100);
                    unset($responseData);
                }

                $regionCodes = [];
                $waitCoordinates = [];
                foreach ($data as &$ov) {
                    $ov['provinceCode'] = "{$ov['provinceCode']}000000";
                    $ov['cityCode'] = "{$ov['cityCode']}000000";
                    $regionCodes[] = $ov['provinceCode'];
                    $regionCodes[] = $ov['cityCode'];
                }
                $existsRegionCodes = RegionalInfoData::filterCode(array_unique($regionCodes));
                foreach ($data as $v) {
                    if (!in_array($v['provinceCode'], $existsRegionCodes) or
                        !in_array($v['cityCode'], $existsRegionCodes)) {
                        $waitCoordinates[] = "{$v["longitude"]},{$v["latitude"]}";
                    }
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data as $v) {
                    try {
                        if ($v['payInputType'] != 1 or ($v['verificationType'] != 0 and $v['verificationType'] != 2)) {
                            continue;
                        }
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['price_list'] = [];
                        $oilTemp['id'] = $v['stationId'];
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $existsStationIds[] = $oilTemp['id'];
                        $oilTemp['station_name'] = $v['stationName'];
                        $oilTemp['station_type'] = 2;
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;

                        if (!in_array($v['provinceCode'], $existsRegionCodes) or
                            !in_array($v['cityCode'], $existsRegionCodes)) {
                            $oilTemp['province_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['provinceCode'] ?? '';
                            $oilTemp['city_code'] = $regionData["{$v["longitude"]},{$v["latitude"]}"]['cityCode'] ?? '';
                        } else {
                            $oilTemp['province_code'] = $v['provinceCode'];
                            $oilTemp['city_code'] = $v['cityCode'];
                        }
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['lng'] = $v['longitude'];
                        $oilTemp['lat'] = $v['latitude'];
                        if (strpos($oilTemp['lng'], '.') !== false) {
                            $dealLng = explode('.', $oilTemp['lng']);
                            $oilTemp['lng'] = $dealLng[0] . '.' . substr($dealLng[1], 0, 6);
                        }
                        if (strpos($oilTemp['lat'], '.') !== false) {
                            $dealLat = explode('.', $oilTemp['lat']);
                            $oilTemp['lat'] = $dealLat[0] . '.' . substr($dealLat[1], 0, 6);
                        }

                        $oilTemp['address'] = $v['stationAddress'];
                        $oilTemp['is_stop'] = 0;
                        $oilTemp['station_oil_unit'] = 1;
                        $oilTemp['trade_type'] = 3;
                        $oilTemp['station_brand'] = config('oil.brand.jq.' . $v['stationSourceId'], 8);
                        $oilTemp['is_highway'] = $v['isHighSpeed'];
                        $oilTemp['business_hours'] = "{$v['businessStartTime']}-{$v['businessEndTime']}";
                        $oilTemp['contact_phone'] = $v['phone'];
                        $insertStationPriceData = $oilTemp;
                        $insertStationPriceData['enabled_state'] = 1;

                        //油站价格数据
                        $oilGunMapping = [];
                        foreach ($v['oilGunList'] as $gv) {
                            if (!isset($oilGunMapping[$gv['oilCode']])) {
                                $oilGunMapping[$gv['oilCode']] = [];
                            }
                            $oilGunMapping[$gv['oilCode']][] = $gv['gunCode'];
                        }
                        foreach ($v['oilPriceList'] as $cv) {
                            $realOilInfo = explode("_", config("oil.oil_mapping.jq.mapping.{$cv['oilCode']}"));
                            $oilPriceTemp = [];
                            $insertOilPriceTemp = [];
                            $oilPriceTemp['oil_type'] = $realOilInfo[1] ?? "";
                            $oilPriceTemp['oil_level'] = $realOilInfo[2] ?? "";
                            $oilPriceTemp['oil_name'] = $realOilInfo[0] ?? "";
                            if (empty($oilPriceTemp['oil_name'])) {
                                Log::handle("Oil's attribute is invalid", [
                                    "data" => $v
                                ], "鲸启", "oil_station_data", "error");
                                continue;
                            }
                            $insertOilPriceTemp['oil_type'] = array_flip(
                                                                  config("oil.oil_type")
                                                              )[$oilPriceTemp['oil_name']] ?? '';
                            $insertOilPriceTemp['oil_no'] = count($realOilInfo) > 1 ? str_replace(
                                $insertOilPriceTemp['oil_type'],
                                '',
                                array_flip(config("oil.oil_no"))[$oilPriceTemp['oil_type']] ?? ''
                            ) : "";
                            $insertOilPriceTemp['oil_level'] = array_flip(
                                                                   config("oil.oil_level")
                                                               )[$oilPriceTemp['oil_level']] ?? '';

                            $oilPriceTemp['price'] = $cv['settlePrice'];
                            $oilPriceTemp['mac_price'] = $cv['stationPrice'];
                            $insertOilPriceTemp['gun_no'] = implode(',', $oilGunMapping[$cv['oilCode']]);
                            $insertOilPriceTemp['sale_price'] = bcmul($cv['settlePrice'], 100);
                            $insertOilPriceTemp['listing_price'] = bcmul($cv['stationPrice'], 100);
                            $insertOilPriceTemp['issue_price'] = bcmul($cv['countryPrice'], 100);
                            $oilTemp['price_list'][] = $oilPriceTemp;
                            $insertStationPriceData['oil_price_list'][] = $insertOilPriceTemp;
                        }

                        $insertStationPriceData['platform_code'] = $this->platformCode;
                        if (empty($oilTemp['price_list'])) {
                            continue;
                        }
                        $oilTemp['clearGunCache'] = true;
                        $oilTemp['gunCacheKeyPrefix'] = "gun_number_";
                        $insertStationPriceData['station_id'] = $v['stationId'];
                        StationPriceData::create($insertStationPriceData);
                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("The info of site is invalid", [
                            'exception' => json_decode(
                                               Log::getMessage([
                                                   'exception' => $throwable,
                                               ]),
                                               true
                                           )['exception'],
                        ], "鲸启", "oil_station_data", "error");
                    }
                }

                $page++;
            } catch (Throwable $exception) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => json_decode(
                                       Log::getMessage([
                                           'exception' => $exception,
                                       ]),
                                       true
                                   )['exception'],
                ], "鲸启", "oil_station_data", "error");
                if ($page == 1) {
                    throw $exception;
                }
            }
        } while ($page <= $totalPage);
        $this->dealNotExistsStation($existsStationIds);
    }
}
