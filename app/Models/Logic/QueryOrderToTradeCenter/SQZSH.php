<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Models\Data\AuthInfo;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Support\Facades\DB;
use Request\SQZSH as SQZSHRequest;
use Throwable;

class SQZSH extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        DB::beginTransaction();
        $orderAssocModel = OrderAssocData::getOneOrderByWhereAndLock([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->args['platform_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->args['name_abbreviation'],
            ],
            [
                'field'    => 'self_order_status',
                'operator' => '=',
                'value'    => 1,
            ],
            [
                'field'    => 'platform_order_status',
                'operator' => '=',
                'value'    => 2,
            ],
        ], ['*'], true);
        if (!$orderAssocModel) {
            DB::rollBack();
            return;
        }
        try {
            $extend = json_decode($orderAssocModel->extend, true);
            if (isset($extend['already_called_back_order_edit']) and $extend['already_called_back_order_edit']) {
                return;
            }
            $oilType = array_flip(config("oil.oil_type"))[$extend['owner']['oil_name_id']] ?? '';
            $oilNo = str_replace(
                $oilType,
                '',
                array_flip(
                    config("oil.oil_no")
                )[$extend['owner']['oil_type_id']] ?? ''
            );
            $oilLevel = array_flip(config("oil.oil_level"))[$extend['owner']['oil_level_id']] ?? '';
            $stationOilExtends = StationPriceData::getDataByWhere([
                [
                    'field'    => 'station_id',
                    'operator' => '=',
                    'value'    => $extend['owner']['app_station_id'],
                ],
                [
                    'field'    => 'oil_type',
                    'operator' => '=',
                    'value'    => $oilType,
                ],
                [
                    'field'    => 'oil_no',
                    'operator' => '=',
                    'value'    => $oilNo,
                ],
                [
                    'field'    => 'oil_level',
                    'operator' => '=',
                    'value'    => $oilLevel,
                ],
                [
                    'field'    => 'platform_code',
                    'operator' => '=',
                    'value'    => AuthInfo::getAuthInfoFieldByNameAbbreviation(
                        $this->args['name_abbreviation'],
                        ['role_code']
                    )['role_code'],
                ],
            ], ['extends']);
            $stationOilExtends = json_decode($stationOilExtends[0]['extends'] ?? '', true) ?? [];
            SQZSHRequest::handle('/order/revokeOrder', [
                'uniqueStr'   => $this->args['platform_order_id'],
                'orderAmount' => $extend['owner']['pushExtends']['amountGun'],
                'fuelName'    => $stationOilExtends[$oilNo . $oilType . $oilLevel]['fuelName'] ?? '',
            ]);
            DB::commit();
        } catch (Throwable $throwable) {
            DB::rollBack();
            throw $throwable;
        }
    }
}
