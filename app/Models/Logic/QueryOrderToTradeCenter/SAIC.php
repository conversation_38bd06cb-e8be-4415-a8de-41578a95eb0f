<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base as CommonBaseLogic;
use App\Models\Logic\Data\Push\VerificationResult;
use Illuminate\Support\Facades\DB;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\GAS as GASRequest;
use Request\SAIC as SAICRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class SAIC extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        $orderAssocModel = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_order_id',
                'operator' => '=',
                'value'    => $this->args['platform_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->args['name_abbreviation'],
            ],
        ], ['*'], true, true);
        if (!$orderAssocModel) {
            return;
        }

        $extend = json_decode($orderAssocModel->extend, true);
        $failedCall = function () use ($extend) {
            (new FeiShu())->writtenOffFailed([
                "title"        => DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        'saic',
                        'platform_name',
                        ''
                    )['platform_name'] . "站点支付失败",
                "reason"       => "上汽超过10s未返回扣款结果",
                "station_name" => $extend["owner"]["trade_place"],
                "oil"          => $extend["owner"]["oil_name"],
                "price"        => $extend["owner"]["supplier_price"],
                "oil_num"      => $extend["owner"]["trade_num"],
                "money"        => $extend["owner"]["supplier_money"],
                "plate_number" => $extend["owner"]["truck_no"],
                "driver_phone" => $extend["owner"]["drivertel"],
            ]);
        };

        try {
            $thirdAuthInfo = json_decode(
                                 AuthConfigData::getAuthConfigValByName(
                                     "SAIC_APP_AUTH_" .
                                     $extend['owner']['pcode']
                                 ),
                                 true
                             ) ?? [];
            $realNameAbbreviation = (json_decode(
                                         AuthConfigData::getAuthConfigValByName(
                                             'SAIC_APP_ID_TO_SUPPLIER_IDENTIFIER_MAPPING'
                                         ),
                                         true
                                     ) ??
                                     [])[$thirdAuthInfo['app_id'] ?? ''] ?? '';
            if (!$path = AuthConfigData::getAuthConfigValByName(
                "SAIC_" . strtoupper($realNameAbbreviation)
                . "_ORDER_QUERY_API_PATH"
            )) {
                $path = AuthConfigData::getAuthConfigValByName("SAIC_ORDER_QUERY_API_PATH");
            }
            $data = SAICRequest::handle($thirdAuthInfo, $path, [
                'outOrderId' => $this->args['platform_order_id'],
            ]);
        } catch (Throwable $exception) {
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_saic_{$this->args['platform_order_id']}",
                5,
                $failedCall
            );
            return;
        }
        if (!isset($data['data']) or !array_has($data['data'], [
                'tradeStatus',
                'tradeDesc',
                'outOrderId',
                'asn',
                'amount'
            ])) {
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                2,
                "order_query_cycle_saic_{$this->args['platform_order_id']}",
                5,
                $failedCall
            );
            return;
        }
        if ($data['data']['tradeStatus'] != '0000') {
            if ($data['data']['tradeStatus'] == '300') {
                $this->retryLaterQueue(
                    new QueryOrderToTradeCenter($this->args),
                    2,
                    "order_query_cycle_saic_{$this->args['platform_order_id']}",
                    5,
                    $failedCall
                );
                return;
            }
            CommonBaseLogic::refundToTradeCenterForPayCallbackFailed(
                $orderAssocModel,
                $data['data']['tradeDesc'],
                false
            );
            return;
        }

        DB::beginTransaction();

        try {
            $orderAssocModel->platform_order_status = 1;
            $orderAssocModel->save();
            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            Log::handle(
                "Pay order failed for saic",
                [
                    "exception" => $e,
                ],
                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                    "saic",
                    "platform_name",
                    ""
                )["platform_name"],
                "pay_order",
                "error"
            );
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                2,
                "order_query_cycle_saic_{$this->args['platform_order_id']}",
                5,
                $failedCall
            );
            return;
        }

        $authInfoData = AuthInfoData::getAuthInfoFieldByNameAbbreviation('saic');
        $source = $extend['owner']['pushExtends']['source'] ?? 'zeus';
        $sendMessageChannel = $source == 'zeus' ? GASRequest::class : FOSS_ORDERRequest::class;
        $messageData = $source == 'zeus' ? [
            'gas.adapter.sendWsMessage',
            [
                'user_id'  => $orderAssocModel->self_order_id,
                'msg_type' => 'order_pay_result',
                'content'  => json_encode([
                    'status'                     => 'success',
                    'reason'                     => '',
                    'platform_name_abbreviation' => 'saic',
                    'pay_card_no'                => $extend['either']['asn'],
                    'station_name'               => $extend['owner']['trade_place'],
                    'amount'                     => $extend['owner']['pushExtends']['amountGun'],
                ]),
            ],
            $authInfoData['access_key'],
            $authInfoData['secret'],
        ] : [
            '/api/services/v1/thirdOrder/pushMsg',
            [
                "msg_type"                   => 2,
                "pre_history_id"             => $orderAssocModel->self_order_id,
                "card_no"                    => $extend['owner']['vice_no'],
                "message"                    => '',
                'platform_name_abbreviation' => 'saic',
                'pay_card_no'                => $extend['either']['asn'],
                'station_name'               => $extend['owner']['trade_place'],
                'amount'                     => $extend['owner']['pushExtends']['amountGun'],
            ],
        ];
        if (OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderAssocModel->self_order_id,
                ],
            ], ['id'], false, true)->count() == 1) {
            (new $sendMessageChannel())->handle(...$messageData);
        } else {
            (new VerificationResult([
                'order_id'            => $orderAssocModel->self_order_id,
                'verification_status' => 1,
            ]))->handle();
        }
    }
}
