<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Trade\Pay\Main as PayMainLogic;
use Illuminate\Support\Facades\Queue;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Request\HBKJ as HBKJRequest;
use Throwable;
use Tool\Alarm\FeiShu;

class HBKJ extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        try {
            $orderAssocModel = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'platform_order_id',
                    'operator' => '=',
                    'value'    => $this->args['platform_order_id'],
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->args['name_abbreviation'],
                ],
            ], ['self_order_id', 'extend'], true, true);
            if (!$orderAssocModel) {
                return;
            }
            $extend = json_decode($orderAssocModel->extend, true);
            try {
                $data = HBKJRequest::handle('getCouponState', [
                    'businessId' => $orderAssocModel->self_order_id,
                    'voucher'    => $this->args['platform_order_id'],
                ]);
            } catch (Throwable $exception) {
                $this->retryLaterQueueSelf();
                return;
            }
            if (!in_array($data['state'], [40, 90])) {
                $this->retryLaterQueueSelf();
                return;
            }
            $orgOrderInfo = OrderAssocData::getOrderInfoByWhere([
                [
                    'field'    => 'self_order_id',
                    'operator' => '=',
                    'value'    => $orderAssocModel->self_order_id,
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '!=',
                    'value'    => 'hbkj',
                ],
            ], ['self_order_id', 'platform_order_id', 'platform_name'], true, true);
            if ($data['state'] == 90) {
                try {
                    $data = FOSS_STATIONRequest::handle(
                        'api/coupon/v1/getTripartiteCouponByTripartiteVoucherAndSupplierCode', [
                        'tripartite_voucher'    => $this->args['platform_order_id'],
                        'pcode'                 => $extend['owner']['pcode'],
                    ]);
                    if ($data['data'][0]['tripartite_status'] == 25) {
                        return;
                    }
                } catch (Throwable $exception) {
                    $this->retryLaterQueueSelf();
                    return;
                }
                $ownerOrderInfo = $extend['owner'] ?? $extend;
                (new FeiShu())->supplierRefundAlarm([
                    'supplier_order_id' => $this->args['platform_order_id'],
                    'self_order_id'     => $orderAssocModel->self_order_id,
                    'platform_order_id' => $orgOrderInfo->platform_order_id ?? '',
                    'supplier_name'     => DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                        'hbkj'
                    ),
                    'station_name'      => $ownerOrderInfo['trade_place'],
                    'org_name'          => $ownerOrderInfo['org_name'],
                    'price'             => $ownerOrderInfo['trade_price'],
                    'oil_num'           => $ownerOrderInfo['oil_num'],
                    'money'             => $ownerOrderInfo['trade_money'],
                ]);
                return;
            }
            $authInfoData = AuthInfoData::getAuthInfoByWhere([
                [
                    'field'    => 'name_abbreviation',
                    'operator' => '=',
                    'value'    => 'hbkj',
                ],
            ], ['*']);
            (new PayMainLogic([
                'auth_data' => $authInfoData,
                'realData'  => array_merge($data, [
                    'couponNo' => $this->args['platform_order_id'],
                    'useTime'  => $data['usedTime']
                ]),
            ]))->handle();
        } catch (Throwable $throwable) {
            $this->retryLaterQueueSelf();
            QueueLog::handle(
                'Pay order failed for hbkj',
                [
                    "exception" => $throwable,
                ],
                DockingPlatformInfoData::getFieldsByNameAbbreviation(
                    "hbkj",
                    "platform_name",
                    ""
                )["platform_name"],
                'pay_order',
                'error'
            );
        }
    }

    /**
     * @throws Throwable
     */
    public function retryLaterQueueSelf()
    {
        $orderQueryCycleCacheKey = "order_query_cycle_sqzl_{$this->args['platform_order_id']}";
        if (5 <= app('redis')->incr($orderQueryCycleCacheKey . '_1')) {
            if (12 <= app('redis')->incr($orderQueryCycleCacheKey . '_5')) {
                app('redis')->del([$orderQueryCycleCacheKey . '_1', $orderQueryCycleCacheKey . '_5']);
                return;
            }
            app('redis')->expire($orderQueryCycleCacheKey . '_1', 3660);
            Queue::later(300, new QueryOrderToTradeCenter($this->args));
            return;
        }
        app('redis')->expire($orderQueryCycleCacheKey . '_1', 360);
        Queue::later(60, new QueryOrderToTradeCenter($this->args));
    }
}
