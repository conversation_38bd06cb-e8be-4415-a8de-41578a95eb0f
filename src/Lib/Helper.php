<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-5-27
 * Time: 下午3:02
 */

namespace GosSDK\Lib;


class Helper
{
    //  生成UUID，并去掉分割符
    static public function uuid()
    {
        if (function_exists('com_create_guid')) {
            $uuid = com_create_guid();
        } else {
            mt_srand((double)microtime() * 10000);//optional for php 4.2.0 and up.
            $charid = \strtolower(md5(uniqid(rand(), TRUE)));
            $hyphen = chr(45);// "-"
            $uuid   = chr(123)// "{"
                . substr($charid, 0, 8) . $hyphen
                . substr($charid, 8, 4) . $hyphen
                . substr($charid, 12, 4) . $hyphen
                . substr($charid, 16, 4) . $hyphen
                . substr($charid, 20, 12)
                . chr(125);// "}"
        }
        $uuid = str_replace(['{', '}'], '', $uuid);

        return $uuid;
    }

    /*
     * XSS获取参数过滤
     */
    static function filterUnSafeCharacter(&$data = null)
    {
        if (is_array($data)) {
            foreach ($data as $key => &$val) {
                self::filterUnSafeCharacter($val);
            }
        } else {
            // Xss过滤
            $data = htmlspecialchars(strip_tags($data), ENT_NOQUOTES);
            // Sql注入过滤
            $preg = '/[\x00-\x08\x0b-\x0c\x0e-\x19]|[\!]|union|select|update|insert|delete|into|load_file|outfile|eval|script|document/i';
            $data = preg_replace($preg, '', $data);
            $data = addslashes($data);
        }
        return $data;
    }
}