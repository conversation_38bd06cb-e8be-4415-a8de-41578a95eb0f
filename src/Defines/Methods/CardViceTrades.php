<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class CardViceTrades {

    /**
     * 单条新增
     */
    const CREATE = 'v1/cardViceTrades/store';

    /**
     * 删除(支持单条和批量)
     */
    const DELETE = 'v1/cardViceTrades/delete';

    /**
     * 根据api_id删除数据(支持单条和批量)
     */
    const DELETEAPIID = 'v1/cardViceTrades/destroyByApiIdAndSysId';

    /**
     * 单条编辑
     */
    const UPDATE = 'v1/cardViceTrades/updateByThirdIdAndSysId';

    /**
     * 获取卡片列表
     */
    const GET_LIST = 'v1/cardViceTrades/get_list';

    /**
     * 获取机构消费分析列表
     */
    const GET_TRADE_AMALYSIS = 'v1/cardViceTrades/getOrgTradeAnalysis';

    /**
     * 获取车辆消费分析列表
     */
    const GET_TRUCK_AMALYSIS = 'v1/cardViceTrades/getTruckTradeAnalysis';

    /**
     * 获取油卡消费分析列表
     */
    const GET_CARD_AMALYSIS = 'v1/cardViceTrades/getCardTradeAnalysis';

    /**
     * 批量新增卡片
     */
    const CREATE_BATCH = 'v1/cardViceTrades/batchStore';


    /**
     * 批量修改卡片
     */
    const UPDATE_BATCH = 'v1/cardViceTrades/batchUpdateByThirdIdAndSysId';

    /**
     * 小票签名查询
     */
    const GET_TICKET = 'v1/cardViceTrades/getTicket';

    /**
     * 扫描验证车辆信息
     */
    const CHECK_TRUCK_INFO = 'v1/truck/checkTruckInfo';

}