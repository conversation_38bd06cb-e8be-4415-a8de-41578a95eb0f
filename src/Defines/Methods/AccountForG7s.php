<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class AccountForG7s {
    /**
     * 获取账户流水
     */
    const GET_ACCOUNT_RECORDS = 'v2/accountRecords/getList';

    /**
     * 获取账户流水
     */
    const ACCOUNT_RECORDS_DETAIL = 'v2/accountRecords/show';

    /**
     * 根据机构获取储值账户列表信息
     */
    const GET_MONEY_ACCOUNT_LIST = 'v2/account/getMoneyAccountList';

    /**
     * 根据机构获取授信账户列表信息
     */
    const GET_CREDIT_ACCOUNT_LIST = 'v2/account/getCreditAccountList';

    /**
     * 获取转账单明细
     */
    const GET_ACCOUNT_TRANSFER_DETAIL = 'v2/account/getAccountMoneyTransferDetail';

    /**
     * 获取汇款银行信息
     */
    const GET_OPERATORS_BY_ACCOUNT_NO = 'v2/account/getOperatorsByAccountNo';

    /**
     * 按机构获取汇款银行信息
     */
    const GET_OPERATORS_BY_ORGCODE = 'v1/operators/getByOrgCode';

}