<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class Station {


    /**
     * 获取列表
     */
    const GET_LIST = 'v1/station/get_list';

    /**
     * 批量新增
     */
    const CREATE_BATCH = 'v1/station/batchStore';

    /**
     * 批量修改
     */
    const UPDATE_BATCH = 'v1/station/batchUpdateByThirdIdAndSysId';

    /**
     * 删除(支持单条和批量)
     */
    const DELETE = 'v1/station/destroyByThirdIdAndSysId';

    /**
     * 根据油站id获取openid(批量)
     */
    const GETOPENID = "v1/station/getOpenIds";

    /**
     * 油站聚合
     */
    const GETFORMAP = "v1/station/getForMap";

    /**
     * 按地图区域获取
     */
    const GETSTATIONBYAREA = "v1/station/getStationByArea";

    /**
     * 油站详情
     */
    const GETSTATIONDETAIL = "v1/station/getStationDetail";

    /**
     * 线路规划后端数据查询
     */
    const GETSTATIONFORPOLYLINE = "v1/station/getStationForPolyLine";
}