<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class CardViceForG7s {


    /**
     * 卡片设置
     */
    const CARD_SET = 'v1/cardVice/cardSet';

    /**
     * 卡片设置(wechat专用)
     */
    const WECHAT_CARD_SET = 'v2/card/cardSetForWechat';

    /**
     * 卡片设置(GasApp专用)
     */
    const GAS_CARD_SET = 'gasApp/card/cardSetForGasApp';

    /**
     * 获取卡流水
     */
    const GET_CARD_FLOW = 'gasApp/card/getCardAccountFlow';

    /**
     * 卡列表探测
     */
    const DETECT_CARD_LIST = 'gasApp/card/detectCardList';

    /**
     * 卡列表探测(wechat专用)
     */
    const DETECT_FOR_WECHAT = 'v2/card/detectCardListForWechat';

    /**
     * 卡详情
     */
    const SHOW = 'v2/card/show';

    /**
     * 通过card_url获取卡详情
     */
    const GET_BY_CARD_URL = 'gasApp/card/getByCardUrl';

    /**
     * 获取卡片列表
     */
    const GET_LIST = 'v1/cardVice/get_list';

    /**
     * 获取卡片明细
     */
    const GET_RECORD = 'v2/card/card_record';

    /**
     * 获取卡片明细详情
     */
    const GET_RECORD_DETAIL = 'v2/card/cardRecordDetail';

    /**
     * 获取账户卡片明细
     */
    const GET_LIST_BY_ACCOUNT_ID = 'v2/card/getListByAccountId';

    /**
     * 获取选择扣款账号
     */
    const GET_ACCOUNT_USE_MONEY = 'v2/card/getAccountUseMoney';

    /**
     * 生成h5页，付款二维码
     */
    const CREATE_PAY_QRCODE = 'v2/card/payQrCode';

    /**
     * 生成转油二维码
     */
    const CREATE_OIL_TRANSFER_QRCODE = 'v2/card/generateOilTransferQrcode';

}