<?php

return [
    'info'      =>
        [
            'name'    => 'AccountAssign',
            'comment' => '分配申请单',
        ],
    'type'      => 'mongo',
    'fields'    =>
        [
            'id'               => 'id',
            'sys_id'           => 'sys_id',
            'third_id'         => '业务系统ID',
            'no'               => '单号',
            'no_type'          => '单号类型（单号前两位）',
            'assign_type'      => '10自动分配，20非自动分配，30撬装分配，40积分分配',
            'org_id'           => '组织ID',
            'org_name'         => '机构名称',
            'account_type'     => '10 资金账户 20 授信账户,30 撬装账户 40 托管卡账户',
            'account_no'       => '扣款账户',
            'money_total'      => '分配总金额',
            'use_cash_fanli'   => '使用现金返利',
            'jifen_total'      => '分配总积分',
            'assign_num'       => '分配卡数',
            'charge_id'        => '充值id',
            'status'           => '状态（-1已驳回，0待审核，1已审核）',
            'check_assign'     => '分配校验(0:未校验,-10:校验失败,5:分配校验中,10:校验成功)',
            'data_from'        => '数据来源（1.gsp，2.web，3.app 4,webchat）',
            'provider_flag'    => '1，中石油中石化 2，撬装 0 其他',
            'creator_id'       => '创建者ID（操作员）',
            'remark'           => '备注',
            'remark_work'      => '备注/外',
            'apply_time'       => '申请时间',
            'last_operator_id' => '最后修改人id',
            'last_operator'    => '最后修改者姓名',
            'other_creator_id' => '接口添加人id',
            'other_creator'    => '接口添加人名称',
            'complete_person'  => '完成人的名称',
            'complete_time'    => '完成时间',
            'createtime'       => '业务系统创建时间',
            'updatetime'       => '业务系统更新时间',
            'deletetime'       => '业务系统删除时间',
            'created_at'       => '创建时间',
            'updated_at'       => '更新时间',
            'deleted_at'       => '删除时间',
        ],
    'casts'     =>
        [
            'id'               => 'string',
            'sys_id'           => 'string',
            'third_id'         => 'string',
            'no'               => 'string',
            'no_type'          => 'string',
            'assign_type'      => 'string',
            'org_id'           => 'string',
            'org_name'         => 'string',
            'account_type'     => 'string',
            'account_no'       => 'string',
            'money_total'      => 'string',
            'use_cash_fanli'   => 'string',
            'jifen_total'      => 'string',
            'assign_num'       => 'string',
            'charge_id'        => 'string',
            'status'           => 'string',
            '_status'          => 'string',
            'check_assign'     => 'string',
            'data_from'        => 'string',
            'provider_flag'    => 'string',
            'creator_id'       => 'string',
            'remark'           => 'string',
            'remark_work'      => 'string',
            'apply_time'       => 'string',
            'last_operator_id' => 'string',
            'last_operator'    => 'string',
            'other_creator_id' => 'string',
            'other_creator'    => 'string',
            'complete_person'  => 'string',
            'complete_time'    => 'string',
            'createtime'       => 'string',
            'updatetime'       => 'string',
            'deletetime'       => 'string',
            'created_at'       => 'string',
            'updated_at'       => 'string',
            'deleted_at'       => 'string',
        ],
    'validator' =>
        [
            'CREATE' =>
                [
                    'id'       => 'required',
                    'no'       => 'required',
                    'org_id'   => 'required',
                    'org_name' => 'required',
                    'status'   => 'required',
                ],
            'UPDATE' =>
                [
                    'id'       => 'required',
                    'no'       => 'required',
                    'org_id'   => 'required',
                    'org_name' => 'required',
                    'status'   => 'required',
                ],
        ],
];
