<?php

return [
    'info'      =>
        [
            'name'    => 'CardViceApp',
            'comment' => '副卡开卡申请单',
        ],
    'type'      => 'mongo',
    'fields'    =>
        [
            'id'                 => '主键id',
            'sys_id'             => '系统ID',
            'third_id'           => '业务系统pk',
            'no'                 => '单号',
            'no_type'            => '单号类型（单号前两位）',
            'org_id'             => '机构ID',
            'org_id_big'         => '创建机构ID',
            'org_id_fanli'       => '返利机构id',
            'org_contact_id'     => '油品机构负责人ID',
            'org_contact_name'   => '机构负责人',
            'org_contact_mobile' => '机构负责人电话',
            'org_addr_id'        => '油品机构收卡人地址ID',
            'org_addr_name'      => '机构收卡联系人姓名',
            'org_addr_mobile'    => '机构收卡地址联系人电话',
            'org_addr_address'   => '机构收卡地址联系人地址',
            'org_addr_zip'       => '机构收卡地址联系人邮编',
            'oil_com'            => '油卡商（油卡类型）（1、中石化，2、中石油）',
            '_oil_com'           => '油卡商（油卡类型）（1、中石化，2、中石油）',
            'card_from'          => '10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡',
            '_card_from'         => '10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡',
            'gas_money_id'       => '公司卡账户',
            'fanli_region'       => '积分可用地区（关联省份表）',
            '_fanli_region'      => '积分可用地区（关联省份表）',
            'fanli_rate'         => '返利利率（协议返利）',
            'num'                => '开卡数量',
            'cardnum'            => '开卡张数json格式',
            'gsporg_id'          => '所属组织ID',
            'user_id'            => '业务负责人id',
            'data_from'          => '单号（数据）来源（1.gsp，2.web，3.app）',
            'is_done'            => '是否接受接口数据(0:否1:是)',
            'status'             => '状态（-1驳回，0待审核，1已审核，2完成）',
            'card_main_id'       => '主卡号ID',
            'file_url'           => '附件url',
            'operator_name'      => '操作员',
            'remark'             => '备注',
            'apply_time'         => '申请时间',
            'express_com'        => '快递公司',
            'express_no'         => '快递单号',
            'creator_id'         => '创建人ID（业务负责人）',
            'last_operator_id'   => '',
            'last_operator'      => '最后修改者姓名',
            'createtime'         => '创建时间（申请时间）',
            'updatetime'         => '更新时间',
            'other_creator_id'   => '接口添加人id',
            'other_creator'      => '接口添加人名称',
            'complete_person'    => '完成人的名称',
            'complete_time'      => '完成时间',
            'created_at'         => '创建时间',
            'updated_at'         => '更新时间',
            'deleted_at'         => '删除时间',
        ],
    'casts'     =>
        [
            'id'                 => 'string',
            'third_id'           => 'string',
            'sys_id'             => 'string',
            'no'                 => 'string',
            'no_type'            => 'string',
            'org_id'             => 'string',
            'org_id_big'         => 'string',
            'org_id_fanli'       => 'string',
            'org_contact_id'     => 'string',
            'org_contact_name'   => 'string',
            'org_contact_mobile' => 'string',
            'org_addr_id'        => 'string',
            'org_addr_name'      => 'string',
            'org_addr_mobile'    => 'string',
            'org_addr_address'   => 'string',
            'org_addr_zip'       => 'string',
            'oil_com'            => 'string',
            '_oil_com'           => 'string',
            'card_from'          => 'string',
            '_card_from'         => 'string',
            'gas_money_id'       => 'string',
            'fanli_region'       => 'string',
            '_fanli_region'      => 'string',
            'fanli_rate'         => 'string',
            'num'                => 'string',
            'cardnum'            => 'string',
            'gsporg_id'          => 'string',
            'user_id'            => 'string',
            'data_from'          => 'string',
            'is_done'            => 'string',
            'status'             => 'string',
            'card_main_id'       => 'string',
            'file_url'           => 'string',
            'operator_name'      => 'string',
            'remark'             => 'string',
            'apply_time'         => 'string',
            'express_com'        => 'string',
            'express_no'         => 'string',
            'creator_id'         => 'string',
            'last_operator_id'   => 'string',
            'last_operator'      => 'string',
            'createtime'         => 'string',
            'updatetime'         => 'string',
            'other_creator_id'   => 'string',
            'other_creator'      => 'string',
            'complete_person'    => 'string',
            'complete_time'      => 'string',
            'created_at'         => 'string',
            'updated_at'         => 'string',
            'deleted_at'         => 'string',
        ],
    'validator' =>
        [
            'CREATE'      =>
                [
                    'org_id_big'   => 'required',
                    'org_id_fanli' => 'required',
                ],
            'UPDATE'      =>
                [
                    'org_id_big'   => 'required',
                    'org_id_fanli' => 'required',
                ],
            'DESTROY'     =>
                [
                ],
            'CREATEBYG7S' =>
                [
                    'fanli_orgcode'      => 'required',
                    'org_contact_name'   => 'required',
                    'org_contact_mobile' => 'required',
                    'org_addr_name'      => 'required',
                    'org_addr_mobile'    => 'required',
                    'org_addr_address'   => 'required',
                    'create_orgcode'     => 'required',
                    'num'                => 'required',
                    'oil_com'            => 'required',
                ],
            'GETDETAIL'   =>
                [
                    'no'      => 'required',
                    'orgcode' => 'required',
                ],
        ],
];
