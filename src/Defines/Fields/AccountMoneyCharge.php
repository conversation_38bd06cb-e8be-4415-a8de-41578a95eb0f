<?php

return [
    'info'      =>
        [
            'name'    => 'AccountMoneyCharge',
            'comment' => '充值资金申请表',
        ],
    'type'      => 'mongo',
    'fields'    =>
        [
            'id'               => '主键ID',
            'sys_id'           => '系统ID',
            'third_id'         => '业务系统pk',
            'gas_id'           => '撬装ID',
            'no'               => '单号',
            'no_type'          => '单号类型（单号前两位）',
            'org_id'           => '机构id',
            'org_name'         => '机构名称',
            'charge_type'      => '1 常规 2 返利',
            'from_orgcode'     => '当前操作机构号',
            'from_orgname'     => '当前操作机构名称',
            'account_no'       => '支付基础信息账号',
            'account_type'     => '账户类型(10:现金账户,20:撬装账户)',
            'pay_info_id'      => '付款信息ID',
            'pay_channel'      => '渠道',
            'pay_type'         => '付款方式',
            'operator_id'      => '运营商ID',
            'operators_name'   => '运营商名称',
            'app_time'         => '申请时间',
            'arrival_money'    => '到账金额',
            'payer_fee_ratio'  => '付款方手续费',
            'payee_fee_ratio'  => '收款方手续费',
            'money'            => '充值金额',
            'fanli_charge'     => '返利充值',
            'real_money'       => '实体货币',
            'fanli_money'      => '使用的返利金额',
            'coupon_money'     => '优惠券',
            'status'           => '状态（-1已驳回，0待审核，1已审核）',
            'pay_company_id'   => '付款公司id',
            'pay_name'         => '付款公司',
            'pay_no'           => '付款单号',
            'data_from'        => '数据来源（1gsp，2web，3app）',
            'remark_work'      => '备注/外',
            'remark'           => '备注',
            'merchantsn'       => '商户单流水号',
            'pay_status'       => '-1：未支付， 0：成功，1：失败，2：处理中',
            'prepaysn'         => '预付单流水号',
            'pay_timeout'      => '支付超时时间',
            'is_settlement'    => '结算状态 -1：未处理，0：成功，1：失败，2：处理中',
            'last_operator_id' => '最后操作人ID',
            'last_operator'    => '最后操作人名称',
            'audit_time'       => '审核时间',
            'other_creator_id' => '接口添加人id',
            'other_creator'    => '接口添加人名称',
            'creator_id'       => '创建人ID',
            'creator_name'     => '创建人名称',
            'updater_id'       => '最后修改者姓名',
            'updater_name'     => '最后修改者姓名',
            'createtime'       => '业务系统创建时间',
            'updatetime'       => '业务系统更新时间',
            'deletetime'       => '业务系统删除时间',
            'created_at'       => '创建时间',
            'updated_at'       => '更新时间',
            'deleted_at'       => '删除时间',
        ],
    'casts'     =>
        [
            'id'               => 'string',
            'sys_id'           => 'string',
            'third_id'         => 'string',
            'gas_id'           => 'string',
            'no'               => 'string',
            'no_type'          => 'string',
            'org_id'           => 'string',
            'org_name'         => 'string',
            'charge_type'      => 'int',
            'from_orgcode'     => 'string',
            'from_orgname'     => 'string',
            'account_no'       => 'string',
            'account_type'     => 'int',
            'pay_info_id'      => 'string',
            'pay_channel'      => 'string',
            'pay_type'         => 'string',
            'operator_id'      => 'string',
            'operators_name'   => 'string',
            'app_time'         => 'string',
            'arrival_money'    => 'double',
            'payer_fee_ratio'  => 'double',
            'payee_fee_ratio'  => 'double',
            'money'            => 'double',
            'fanli_charge'     => 'double',
            'real_money'       => 'double',
            'fanli_money'      => 'double',
            'coupon_money'     => 'double',
            'status'           => 'int',
            'pay_company_id'   => 'string',
            'pay_name'         => 'string',
            'pay_no'           => 'string',
            'data_from'        => 'int',
            'remark_work'      => 'string',
            'remark'           => 'string',
            'merchantsn'       => 'string',
            'pay_status'       => 'int',
            'prepaysn'         => 'string',
            'pay_timeout'      => 'string',
            'is_settlement'    => 'int',
            'last_operator_id' => 'string',
            'last_operator'    => 'string',
            'audit_time'       => 'string',
            'other_creator_id' => 'string',
            'other_creator'    => 'string',
            'creator_id'       => 'string',
            'creator_name'     => 'string',
            'updater_id'       => 'string',
            'updater_name'     => 'string',
            'createtime'       => 'string',
            'updatetime'       => 'string',
            'deletetime'       => 'string',
            'created_at'       => 'string',
            'updated_at'       => 'string',
            'deleted_at'       => 'string',
        ],
    'validator' =>
        [
        ],
];
