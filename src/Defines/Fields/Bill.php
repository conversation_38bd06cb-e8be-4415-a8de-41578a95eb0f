<?php

return [
    'info'      =>
        [
            'name'    => 'Bill',
            'comment' => '账单表',
        ],
    'type'      => 'mysql',
    'fields'    =>
        [
            'id'                   => '主键id',
            'no'                   => '账单编号',
            'starttime'            => '账单起始时间',
            'endtime'              => '账单结束时间',
            'repayment_lastdate'   => '最后还款日',
            'money'                => '账单总额(本金)',
            'repay_money'          => '应还总额（含利息）',
            'repay_interest'       => '应还利息',
            'latefee'              => '滞纳金(逾期产生的费用)',
            'repaid_principal'     => '已还金额(本金+利息)',
            'not_repaid_principal' => '未还金额(本金+利息)',
            'status'               => '还款状态：0未确认,1已确认,2部分还款,3已还款(待定)',
            'org_id'               => '机构id',
            'account_id'           => '账号id',
            'account_no'           => '账号',
            'third_id'             => '第三方id',
            'sys_id'               => '系统id',
            'createtime'           => '账单产生日期',
            'creator_name'         => '创建人',
            'created_at'           => '创建时间',
            'updated_at'           => '更新时间',
            'deleted_at'           => '删除时间'
        ],
    'casts'     =>
        [
            'id'                   => 'string',
            'no'                   => 'string',
            'starttime'            => 'string',
            'endtime'              => 'string',
            'repayment_lastdate'   => 'string',
            'money'                => 'string',
            'repay_money'          => 'string',
            'repay_interest'       => 'string',
            'latefee'              => 'string',
            'repaid_principal'     => 'string',
            'not_repaid_principal' => 'string',
            'status'               => 'string',
            'org_id'               => 'string',
            'account_id'           => 'string',
            'account_no'           => 'string',
            'third_id'             => 'string',
            'sys_id'               => 'string',
            'createtime'           => 'string',
            'creator_name'         => 'string',
            'created_at'           => 'string',
            'updated_at'           => 'string',
            'deleted_at'           => 'string'
        ],
    'validator' =>
        [
        ],
];
