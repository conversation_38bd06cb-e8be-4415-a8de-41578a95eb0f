<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-23
 * Time: 下午5:51
 */

namespace GosSDK;

use GosSDK\Config\Config;
use GosSDK\Lib\Helper;
use GosSDK\Lib\Middleware;
use GuzzleHttp;

class Http
{
    static public function post(Gos $gos)
    {
        $data = null;
        $encrypt = new Crypt();
        $apiParams = [
            'request_id'   => Helper::uuid(),
            'app_id'       => $encrypt->getAppId(),
            'channel'      => isset($gos->channel) && $gos->channel ? trim($gos->channel) : null,
            'action'       => $gos->action,
            'method'       => $gos->method,
            'dataType'     => $gos->dataType,
            'attach'       => $gos->attach,
            'data'         => $gos->apiParams,
            'callback_url' => $encrypt->getCallbakUrl(),
        ];
        $_apiParams = $apiParams;
        $apiParams['data'] = $encrypt->encode($gos->apiParams);
        $apiUrl            = $encrypt->getAppUrl() . '/' . $gos->method;
//        echo $apiUrl;die;
        $middleWare = new Middleware();
        $middleWare->beforeAction($_apiParams);


        $config = Config::getConfig();
        try {
            $client = new GuzzleHttp\Client();

            $response = $client->request($gos->requestType, $apiUrl, [
                'json' => $apiParams,
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \RuntimeException($response->getBody(), $response->getStatusCode());
            }

            $jsonData = $response->getBody()->getContents();

            if (class_exists("\Framework\Log")) {

                \Framework\Log::info('data---'.var_export($jsonData,true),[],'interface');
            }

            $data = json_decode($jsonData);


            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::dataLog($jsonData);
                throw new \RuntimeException('JSON_DECODE_ERROR', 2);
            }

            if (!isset($data->code) || $data->code !== 0) {
                $message = isset($data->message) ? $data->message : $jsonData;
                throw new \RuntimeException($message, isset($data->code) && $data->code == 1001 ? $data->code : 2);
            }
        } catch (GuzzleHttp\Exception\ClientException $e) {
            Log::dataLog(['apiParams' => $_apiParams, 'Exception' => strval($e)]);
            $message = isset($config['debug']) && $config['debug'] ? strval($e) : $e->getMessage();
            throw new \RuntimeException($message, $e->getCode());
        } catch (\Exception $e) {
            Log::dataLog(['apiParams' => $_apiParams, 'Exception' => strval($e)]);
            $message = isset($config['debug']) && $config['debug'] ? strval($e) : $e->getMessage();
            throw new \RuntimeException($message, $e->getCode());
        }

        $middleWare->afterAction(
            [
                'params' => $_apiParams,
                'result' => (array)$data,
            ]
        );

        return $data->data;
    }
}
