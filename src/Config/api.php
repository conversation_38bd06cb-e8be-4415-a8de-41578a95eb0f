<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 15:56
 */


return [
    /**
     * 是否开启调试
     */
    'debug' =>  TRUE,

    /**
     * 是否开启日志
     */
    'log'         => TRUE,

    /**
     * 日志保存目录
     */
    'log_dir'     => '',

    /**
     * 接口请求地址
     */
    'url'         => 'http://dev.gos.chinawayltd.com',

    /**
     * 接口接入账户
     */
    'app_id'      => 'c5f57330-ed04-445c-8e3f-79342173ced5',

    /**
     * 参数加密公钥
     */
    'public_key'  => '9864DD8C9784CC161AB4D5BA586BC91B', //'afieE0mPTjLH+mmdB6G9qo0MoQhnz4m3',

    /**
     * 回调地址
     */
    'callbak_url' => 'http://dev.zqx.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',

    /**
     * 翻页需要样式
     * eg: ['gos名'=>'业务系统名','per_page'=>'pageSize','pageNo'=>'current_page']
     */
    'page_style'  => [
        'total'        => 'total',
        'per_page'     => 'per_page',
        'current_page' => 'current_page',
        'data'         => 'data'
    ],

    /**
     * sdk中间件
     * 如果希望在接口请求前或请求后注入自定义逻辑
     * 可以分别配置以下两个middleware
     * sdk将会执行指定类的handle()方法
     * BeforeMiddleware $params为即将请求的参数
     * AfterMiddleware   $params为请求后结果
     *
     * 如：
     * 'BeforeMiddleware'  =>  Fuel\Service\GosTask::class,
     */
    'middleware'  => [
        //接口请求前中间件
        'BeforeMiddleware' => Fuel\SdkMiddleware\Gos\BeforeMiddleware::class,

        //接口请求后中间件
        'AfterMiddleware'  => Fuel\SdkMiddleware\Gos\AfterMiddleware::class
    ]
];