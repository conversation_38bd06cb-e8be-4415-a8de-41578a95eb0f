<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-24
 * Time: 下午4:59
 */

namespace GosSDK\Config;


class Config
{
    static public $config;

    static public function getConfig()
    {
        if(!self::$config){
            if(defined('GOS_SDK_CONFIG')){
                if(!file_exists(GOS_SDK_CONFIG)){
                    throw new \RuntimeException('GOS_SDK_CONFIG 文件不存在', 2);
                }
                self::$config = require_once GOS_SDK_CONFIG;
            }else{
                self::$config = require_once __DIR__ . DIRECTORY_SEPARATOR . 'api.php';
            }
        }

        return self::$config;
    }
}