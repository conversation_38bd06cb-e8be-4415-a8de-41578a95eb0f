<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-24
 * Time: 下午4:40
 */

namespace GosSDK;


use GosSDK\Config\Config;

class Log
{
    static protected $config = NULL;

    static public function dataLog($content, $type = 'log_', $params = FALSE)
    {
        global $sdkTimeStart;
        if (!self::$config) {
            self::$config = Config::getConfig();
        }

        if(!isset(self::$config['log']) || !self::$config['log']){
            return;
        }

        $logDir = isset(self::$config['log_dir']) && self::$config['log_dir'] ? self::$config['log_dir'] : __DIR__ . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR;

        //文件名称 1--7
        $basePath = $logDir . date('Y-m-d') . DIRECTORY_SEPARATOR;

        if (!is_dir($basePath)) {
            @mkdir($basePath, 0777);
            @chmod($basePath, 0777);
        }

        $logfile = $basePath . $type . date("N", time()) . '.php';
        if (is_file($logfile)) {
            if (@filesize($logfile) >= 2048000) {
                //2M 分文件
                $logFileBak = $logfile . '_' . date('His') . '_bak.php';
                @rename($logfile, $logFileBak);
            }
        }
        $logStr    = var_export($content, TRUE) . "\r\n";
        $mTime     = explode(' ', microtime());
        $totalTime = @number_format(($mTime[1] + $mTime[0] - $sdkTimeStart), 4);
        $url       = '';
        $newLog    = date('Y-m-d H:i:s', time()) . ' ' . $totalTime . ' ' . $logStr . ' ' . $url;

        if ($fp = @fopen($logfile, 'a')) {
            @flock($fp, 2);
            fwrite($fp, str_replace(['<?', '?>'], '', $newLog) . "\n\n");
            fclose($fp);
        }
        unset($content, $logStr, $newLog);

    }

    /**
     * 获取系统时间，微秒为单位
     *
     * @return float
     */
    static public function getMicroTime()
    {
        list($uSec, $sec) = explode(" ", microtime());

        return ((float)$uSec + (float)$sec);
    }

}

