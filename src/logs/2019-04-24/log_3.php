2019-04-24 11:36:43 0.1564 array (
  'apiParams' => 
  array (
    'request_id' => '7818ab3c-9fdd-7cf1-a43b-58eaefeb6a1f',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: Parse error: syntax error, unexpected \';\', expecting \']\' in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:37:17 0.1762 array (
  'apiParams' => 
  array (
    'request_id' => '9536652c-6691-6e04-acbc-1ffb5a9025ff',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: Parse error: syntax error, unexpected \';\', expecting \']\' in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:37:40 0.1298 array (
  'apiParams' => 
  array (
    'request_id' => 'd62379b5-e60e-cbd4-1f53-34d65ed16056',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: Parse error: syntax error, unexpected \';\', expecting \']\' in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:38:45 0.6484 array (
  'apiParams' => 
  array (
    'request_id' => '14cdb47d-5245-8513-fa32-e9be6ef8f5c1',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:39:05 0.2002 array (
  'apiParams' => 
  array (
    'request_id' => '09e15c38-3191-2f99-5161-ed46460c58bf',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:39:49 0.2858 array (
  'apiParams' => 
  array (
    'request_id' => '282f5360-ff01-324b-3784-d7f4863b89f5',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:39:50 0.1801 array (
  'apiParams' => 
  array (
    'request_id' => 'dda720c1-5259-3bda-9f79-6e20eb723d1c',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:40:05 0.2152 array (
  'apiParams' => 
  array (
    'request_id' => '5d0968c0-61ac-51f5-47e3-0cff7397d9a9',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: 333 in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:40:23 0.2300 array (
  'apiParams' => 
  array (
    'request_id' => 'b1e267a2-5545-6ad0-ade7-9670ed034aec',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:41:03 0.3078 array (
  'apiParams' => 
  array (
    'request_id' => 'be87a6f6-892a-b502-58d3-bddb8addb160',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:43:13 0.3153 array (
  'apiParams' => 
  array (
    'request_id' => '0dbeddc3-73d6-d9e6-3950-94f7c033408b',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:43:41 0.1911 array (
  'apiParams' => 
  array (
    'request_id' => '425f458d-143e-d617-89d2-d3ef00bfa16d',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:43:42 0.1812 array (
  'apiParams' => 
  array (
    'request_id' => 'a18c42f3-aec2-5ff0-738a-e75520a67fee',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

2019-04-24 11:43:43 0.1794 array (
  'apiParams' => 
  array (
    'request_id' => 'e2e02ac1-c423-de46-55c2-63ad60c0bd9c',
    'app_id' => '94a24035-915c-4d78-9361-4ac6b2227424',
    'channel' => NULL,
    'action' => NULL,
    'method' => 'v1/card/getCardByOpenid',
    'dataType' => NULL,
    'attach' => NULL,
    'data' => 
    array (
      'openid' => '123123123123123',
    ),
    'callback_url' => 'http://dev.fuel.g7s.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify',
  ),
  'Exception' => 'RuntimeException: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve \'dds-2ze0f8edb573fc341.mongodb.rds.aliyuncs.com\'] in /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Http.php:67
Stack trace:
#0 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/SDK/GosSDK/src/Gos.php(142): GosSDK\\Http::post(Object(GosSDK\\Gos))
#1 /home/<USER>/workspaces/cat_framework/app/cat_fuel/module/card/service.php(1276): GosSDK\\Gos->sync()
#2 /home/<USER>/workspaces/cat_framework/lib/ServiceProxy/ServiceProxy.class.php(164): cardService->getCardByOpenid(Array)
#3 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(116): ServiceProxy->__call(\'getCardByOpenid\', Array)
#4 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(93): Fuel\\MiddleWare\\wxAuth->checkOpenid()
#5 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Fuel/MiddleWare/wxAuth.php(71): Fuel\\MiddleWare\\wxAuth->handle()
#6 [internal function]: Fuel\\MiddleWare\\wxAuth->__construct()
#7 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(150): ReflectionClass->newInstance()
#8 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(79): Framework\\Route\\CatRouter->getModuleMethod()
#9 /home/<USER>/workspaces/cat_framework/app/cat_fuel/library/Framework/Route/CatRouter.php(92): Framework\\Route\\CatRouter->checkParams()
#10 /home/<USER>/workspaces/cat_framework/app/cat_fuel/route-api.php(384): Framework\\Route\\CatRouter->dispatcher()
#11 /home/<USER>/workspaces/cat_framework/app/cat_fuel/www/api.php(40): require_once(\'/home/<USER>/works...\')
#12 {main}',
)
 

