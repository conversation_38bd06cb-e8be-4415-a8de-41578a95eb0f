<?php

namespace Unit\Downstream;

class TestStationTest extends \TestCase
{
    public function testStationQuery()
    {
        $data = [
            'station_id' => '119e6bfc7f7311eeae13fa163e2a9678'
        ];
        $signData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => json_encode($data,JSON_UNESCAPED_UNICODE),
            'app_key' => 'mb_api',
        ];
        $secret = '2b4402c276b6e47401af2560dd2122ec';
        $sign = $this->getSign($signData, $secret);
        $requestData = array_merge($signData, ['sign' => $sign]);
        $response = $this->post('/station/query' , $requestData);
        $this->assertJson(json_encode(['code' => 0]), $response->response->content());
        dd(json_decode($response->response->content(), true));
    }


    public function getSign($params, $secret)
    {
        if (array_has($params, ['timestamp', 'data'])) {
            $checkSignData = [
                'timestamp' => $params['timestamp'],
                'data' => $params['data'],
                'app_key' => $params['app_key'],
            ];
            return createSign($checkSignData, $secret);
        }
    }
}
