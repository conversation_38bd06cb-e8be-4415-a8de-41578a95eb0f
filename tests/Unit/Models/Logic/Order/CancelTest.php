<?php

namespace Unit\Models\Logic\Order;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\Cancel;
use App\Models\Logic\Order\Pay\Main as PayMainLogic;
use Exception;
use Illuminate\Http\Response;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use TypeError;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Cancel
 */
class CancelTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with downstream customer role
     */
    public function testHandleWithDownstreamCustomerRole()
    {
        $data = [
            'auth_data' => [
                'role' => 6
            ],
            'order_id' => 'test-order'
        ];

        $expectedResponse = new Response('success', 200);
        
        $mockPayLogic = Mockery::mock('overload:' . PayMainLogic::class);
        $mockPayLogic->shouldReceive('handle')->once()->andReturn($expectedResponse);

        $logic = new Cancel($data);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with regular customer role and valid order
     */
    public function testHandleWithRegularCustomerRoleAndValidOrder()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ],
            'order_id' => 'test-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-order-123'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_order_id',
                    'operator' => '=',
                    'value' => 'test-order',
                ],
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'test',
                ],
            ], ['self_order_id'])
            ->andReturn($orderAssocData);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/cancelOrder', [
                'order_id' => 'internal-order-123',
            ])
            ->andReturn(new Response('success', 200));

        $logic = new Cancel($data);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with regular customer role and invalid order
     */
    public function testHandleWithRegularCustomerRoleAndInvalidOrder()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ],
            'order_id' => 'invalid-order'
        ];

        // Mock OrderAssocData to return empty result
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_order_id',
                    'operator' => '=',
                    'value' => 'invalid-order',
                ],
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'test',
                ],
            ], ['self_order_id'])
            ->andReturn([]);

        // Mock responseFormat function
        $mockResponse = new Response('Order not found', 404);
        $this->mockFunction('responseFormat', $mockResponse);

        $logic = new Cancel($data);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * Test constructor sets data correctly
     */
    public function testConstructorSetsDataCorrectly()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ],
            'order_id' => 'test-order'
        ];

        $logic = new Cancel($data);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $actualData = $property->getValue($logic);

        $this->assertEquals($data, $actualData);
    }

    /**
     * Test handle method sets pay_status for downstream customer
     */
    public function testHandleSetsPayStatusForDownstreamCustomer()
    {
        $data = [
            'auth_data' => [
                'role' => 6
            ],
            'order_id' => 'test-order'
        ];

        $mockPayLogic = Mockery::mock('overload:' . PayMainLogic::class);
        $mockPayLogic->shouldReceive('__construct')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['pay_status'] === 2;
            }))
            ->andReturnSelf();
        $mockPayLogic->shouldReceive('handle')->once()->andReturn(new Response('success', 200));

        $logic = new Cancel($data);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * Test handle method with exception from FOSS_ORDER request
     */
    public function testHandleWithExceptionFromFossOrderRequest()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ],
            'order_id' => 'test-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-order-123'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS request failed', 5000001));

        $logic = new Cancel($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS request failed');
        $this->expectExceptionCode(5000001);

        $logic->handle();
    }

    /**
     * Test handle method with null order data
     */
    public function testHandleWithNullOrderData()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ],
            'order_id' => 'test-order'
        ];

        // Mock OrderAssocData to return null
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn(null);

        $mockResponse = new Response('Order not found', 404);
        $this->mockFunction('responseFormat', $mockResponse);

        $logic = new Cancel($data);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * Test handle method with missing auth data
     */
    public function testHandleWithMissingAuthData()
    {
        $data = [
            'order_id' => 'test-order'
        ];

        $logic = new Cancel($data);

        // Should handle gracefully when auth_data is missing
        $this->expectException(TypeError::class);
        $logic->handle();
    }

    /**
     * Test handle method with missing order_id
     */
    public function testHandleWithMissingOrderId()
    {
        $data = [
            'auth_data' => [
                'role' => 1,
                'name_abbreviation' => 'test'
            ]
        ];

        $logic = new Cancel($data);

        // Should handle gracefully when order_id is missing
        $this->expectException(TypeError::class);
        $logic->handle();
    }

    /**
     * Test handle method with different platform names
     */
    public function testHandleWithDifferentPlatformNames()
    {
        $platforms = ['zy', 'jd', 'xy', 'sp'];

        foreach ($platforms as $platform) {
            $data = [
                'auth_data' => [
                    'role' => 1,
                    'name_abbreviation' => $platform
                ],
                'order_id' => 'test-order-' . $platform
            ];

            $orderAssocData = [
                'self_order_id' => 'internal-order-' . $platform
            ];

            // Mock OrderAssocData
            $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
            $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
                ->once()
                ->with([
                    [
                        'field' => 'platform_order_id',
                        'operator' => '=',
                        'value' => 'test-order-' . $platform,
                    ],
                    [
                        'field' => 'platform_name',
                        'operator' => '=',
                        'value' => $platform,
                    ],
                ], ['self_order_id'])
                ->andReturn($orderAssocData);

            // Mock FOSS_ORDERRequest
            $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
            $mockFossRequest->shouldReceive('handle')
                ->once()
                ->andReturn(new Response('success', 200));

            $logic = new Cancel($data);
            $result = $logic->handle();

            $this->assertInstanceOf(Response::class, $result);
        }
    }
}
