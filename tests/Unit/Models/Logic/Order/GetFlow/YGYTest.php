<?php

namespace Tests\Unit\Models\Logic\Order\GetFlow;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\CompanyMapping as CompanyMappingData;
use App\Models\Logic\Order\GetFlow\ThirdParty;
use App\Models\Logic\Order\GetFlow\YGY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS as FOSSRequest;
use stdClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\GetFlow\YGY
 */
class YGYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test handle method with valid data
     */
    public function testHandleWithValidData()
    {
        $data = [
            'companyId' => 'ygy-company-123',
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP001';

        $authInfo = [
            'card_no' => 'CARD123456'
        ];

        $fossData = [
            'data' => [
                [
                    'money' => '100.50',
                    'after_money' => '1500.75',
                    'no' => 'BILL001',
                    'bill_type' => 1,
                    'createtime' => '2023-01-15 10:30:00'
                ],
                [
                    'money' => '-50.25',
                    'after_money' => '1450.50',
                    'no' => 'BILL002',
                    'bill_type' => 2,
                    'createtime' => '2023-01-20 14:45:00'
                ]
            ]
        ];

        // Mock CompanyMappingData
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_company_no',
                    'operator' => '=',
                    'value' => 'ygy-company-123',
                ],
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'ygy',
                ],
            ], ['company_no'], true, true)
            ->andReturn($companyInfo);

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->once()
            ->with('COMP001', true)
            ->andReturn($authInfo);

        // Mock FOSSRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('gas.org.accountBill', [
                'org_code' => 'COMP001',
                'createtimeGe' => '2023-01-01 00:00:00',
                'createtimeLe' => '2023-01-31 23:59:59',
                'vice_no' => 'CARD123456',
            ])
            ->andReturn($fossData);

        // Mock responseFormatForYgy function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => [
                [
                    'cardNo' => 'COMP001',
                    'money' => 10050.0,
                    'changeMoney' => '+100.50',
                    'accountMoney' => 150075.0,
                    'malNumber' => 'BILL001',
                    'type' => 1,
                    'companyId' => 'ygy-company-123',
                    'payTime' => '2023-01-15 10:30:00'
                ],
                [
                    'cardNo' => 'COMP001',
                    'money' => -5025.0,
                    'changeMoney' => '-50.25',
                    'accountMoney' => 145050.0,
                    'malNumber' => 'BILL002',
                    'type' => 2,
                    'companyId' => 'ygy-company-123',
                    'payTime' => '2023-01-20 14:45:00'
                ]
            ]
        ]);
        $this->mockFunction('responseFormatForYgy', $expectedResponse);

        // Create YGY instance
        $logic = new YGY($data);

        // Set name_abbreviation using reflection
        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with empty company info
     */
    public function testHandleWithEmptyCompanyInfo()
    {
        $data = [
            'companyId' => 'invalid-company-id'
        ];

        // Mock CompanyMappingData to return empty result
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')
            ->once()
            ->andReturn([]);

        // Mock responseFormatForYgy function for error case
        $expectedResponse = new JsonResponse([
            'code' => 5000119,
            'msg' => 'Company not found'
        ]);
        $this->mockFunction('responseFormatForYgy', $expectedResponse);

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with empty auth info
     */
    public function testHandleWithEmptyAuthInfo()
    {
        $data = [
            'companyId' => 'ygy-company-no-auth'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP_NO_AUTH';

        // Mock CompanyMappingData
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')
            ->once()
            ->andReturn($companyInfo);

        // Mock AuthInfoData to return empty result
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->once()
            ->with('COMP_NO_AUTH', true)
            ->andReturn([]);

        $expectedResponse = new JsonResponse([
            'code' => 5000119,
            'msg' => 'Auth info not found'
        ]);
        $this->mockFunction('responseFormatForYgy', $expectedResponse);

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS request exception
     */
    public function testHandleWithFossRequestException()
    {
        $data = [
            'companyId' => 'ygy-company-error',
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP_ERROR';

        $authInfo = [
            'card_no' => 'CARD_ERROR'
        ];

        // Mock CompanyMappingData
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')
            ->once()
            ->andReturn($companyInfo);

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->once()
            ->andReturn($authInfo);

        // Mock FOSSRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS service unavailable', 5000025));

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS service unavailable');
        $this->expectExceptionCode(5000025);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with negative money and bill type 1
     */
    public function testHandleWithNegativeMoneyAndBillType1()
    {
        $data = [
            'companyId' => 'ygy-company-negative',
            'startTime' => '2023-02-01 00:00:00',
            'endTime' => '2023-02-28 23:59:59'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP_NEG';

        $authInfo = [
            'card_no' => 'CARD_NEG'
        ];

        $fossData = [
            'data' => [
                [
                    'money' => '-75.00',
                    'after_money' => '925.00',
                    'no' => 'BILL_NEG',
                    'bill_type' => 1, // bill_type 1 with negative money should result in type 5
                    'createtime' => '2023-02-10 12:00:00'
                ]
            ]
        ];

        // Mock dependencies
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')->once()->andReturn($companyInfo);

        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')->once()->andReturn($authInfo);

        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')->once()->andReturn($fossData);

        // Mock responseFormatForYgy and verify type is 5 for negative money with bill_type 1
        $this->mockFunction('responseFormatForYgy', function ($code, $data) {
            if ($code === 0 && isset($data[0]['type']) && $data[0]['type'] === 5) {
                return new JsonResponse(['code' => 0, 'data' => $data]);
            }
            return new JsonResponse(['code' => $code, 'data' => $data]);
        });

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with empty FOSS data
     */
    public function testHandleWithEmptyFossData()
    {
        $data = [
            'companyId' => 'ygy-company-empty',
            'startTime' => '2023-03-01 00:00:00',
            'endTime' => '2023-03-31 23:59:59'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP_EMPTY';

        $authInfo = [
            'card_no' => 'CARD_EMPTY'
        ];

        $fossData = [
            'data' => [] // empty data array
        ];

        // Mock dependencies
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')->once()->andReturn($companyInfo);

        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')->once()->andReturn($authInfo);

        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')->once()->andReturn($fossData);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => [] // should return empty array
        ]);
        $this->mockFunction('responseFormatForYgy', $expectedResponse);

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test inheritance from ThirdParty class
     */
    public function testInheritanceFromThirdParty()
    {
        $logic = new YGY([]);
        $this->assertInstanceOf(ThirdParty::class, $logic);
    }

    /**
     * Test that data property exists and is accessible
     */
    public function testDataPropertyExists()
    {
        $testData = ['companyId' => 'test'];
        $logic = new YGY($testData);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('data'));

        $property = $reflection->getProperty('data');
        $property->setAccessible(true);

        // Test getting property
        $this->assertEquals($testData, $property->getValue($logic));
    }

    /**
     * Test handle method return type annotation
     */
    public function testHandleMethodReturnTypeAnnotation()
    {
        $reflection = new ReflectionClass(YGY::class);
        $method = $reflection->getMethod('handle');
        
        $this->assertTrue($method->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $method->getReturnType()->getName());
    }

    /**
     * Test money calculation with bcmul
     */
    public function testMoneyCalculationWithBcmul()
    {
        $data = [
            'companyId' => 'ygy-company-calc',
            'startTime' => '2023-04-01 00:00:00',
            'endTime' => '2023-04-30 23:59:59'
        ];

        $companyInfo = new stdClass();
        $companyInfo->company_no = 'COMP_CALC';

        $authInfo = [
            'card_no' => 'CARD_CALC'
        ];

        $fossData = [
            'data' => [
                [
                    'money' => '123.456', // should be multiplied by 100
                    'after_money' => '987.654',
                    'no' => 'BILL_CALC',
                    'bill_type' => 3,
                    'createtime' => '2023-04-15 16:30:00'
                ]
            ]
        ];

        // Mock dependencies
        $mockCompanyMappingData = Mockery::mock('alias:' . CompanyMappingData::class);
        $mockCompanyMappingData->shouldReceive('getCompanyByWhere')->once()->andReturn($companyInfo);

        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')->once()->andReturn($authInfo);

        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')->once()->andReturn($fossData);

        // Mock responseFormatForYgy and verify money calculations
        $this->mockFunction('responseFormatForYgy', function ($code, $data) {
            if ($code === 0 && isset($data[0])) {
                $item = $data[0];
                // Verify money is multiplied by 100: 123.456 * 100 = 12345.6
                if ($item['money'] === 12345.6 && $item['accountMoney'] === 98765.4) {
                    return new JsonResponse(['code' => 0, 'data' => $data]);
                }
            }
            return new JsonResponse(['code' => $code, 'data' => $data]);
        });

        $logic = new YGY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'ygy');

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
