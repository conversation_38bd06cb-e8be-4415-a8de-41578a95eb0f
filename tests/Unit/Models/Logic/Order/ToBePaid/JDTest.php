<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\JD;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\JD
 */
class JDTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with complete data
     */
    public function testHandleWithCompleteData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-123',
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI',
            'refueling_time' => '2023-01-01 12:00:00',
            'telephone' => '13800138000',
            'station_id' => 'station-001',
            'oil_num' => 50.5,
            'price' => 6.8,
            'money' => 343.4
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['platform_order_id'] === 'jd-order-123' &&
                       $arg['platform_name'] === 'jd' &&
                       $arg['refueling_time'] === '2023-01-01 12:00:00';
            }))
            ->andReturn(['order_id' => 'internal-123']);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::type('array'))
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-order-123'],
                'msg' => 'success'
            ]);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse(['code' => 0, 'data' => ['order_id' => 'foss-order-123']]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new JD($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple.92#' => 'type_92',
            'oil.oil_no_simple.0#' => 'type_0',
            'oil.oil_level.国VI' => 'level_6',
            'oil.oil_level.国V' => 'level_5',
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with minimal data
     */
    public function testHandleWithMinimalData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-456',
            'oil_name' => '95#汽油',
            'refueling_time' => '2023-01-02 14:30:00',
            'telephone' => '13900139000'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->andReturn(['order_id' => 'internal-456']);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-order-456'],
                'msg' => 'success'
            ]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new JD($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with oil type mapping
     */
    public function testHandleWithOilTypeMapping()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-789',
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国V',
            'refueling_time' => '2023-01-03 16:45:00',
            'telephone' => '13700137000'
        ];

        // Mock config function with specific mappings
        $this->mockConfigWithMappings();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilNameId'] === 'diesel_id' &&
                       $taskData['oilTypeId'] === 'type_0' &&
                       $taskData['oilLevelId'] === 'level_5';
            }))
            ->andReturn(['order_id' => 'internal-789']);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-order-789'],
                'msg' => 'success'
            ]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new JD($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['92#' => 'type_92', '0#' => 'type_0'],
            'oil.oil_level.国V' => 'level_5',
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-error',
            'oil_name' => '92#汽油',
            'refueling_time' => '2023-01-04 10:00:00',
            'telephone' => '13600136000'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->andReturn(['order_id' => 'internal-error']);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order creation failed', 5000001));

        $logic = new JD($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS order creation failed');
        $this->expectExceptionCode(5000001);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with OrderAssoc exception
     */
    public function testHandleWithOrderAssocException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-db-error',
            'oil_name' => '92#汽油',
            'refueling_time' => '2023-01-05 11:30:00',
            'telephone' => '13500135000'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData to throw exception
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->andThrow(new Exception('Database error', 5000002));

        $logic = new JD($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');
        $this->expectExceptionCode(5000002);

        $logic->handle();
    }

    /**
     * Test data transformation for oil_time field
     */
    public function testDataTransformationForOilTime()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-time',
            'oil_name' => '92#汽油',
            'refueling_time' => '2023-01-06 09:15:00',
            'telephone' => '13400134000'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oil_time'] === '2023-01-06 09:15:00' &&
                       $taskData['driver_phone'] === '13400134000';
            }))
            ->andReturn(['order_id' => 'internal-time']);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-order-time'],
                'msg' => 'success'
            ]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new JD($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handle method with empty oil type and level
     */
    public function testHandleWithEmptyOilTypeAndLevel()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'jd'
            ],
            'order_id' => 'jd-order-empty',
            'oil_name' => '92#汽油',
            'oil_type' => '',
            'oil_level' => '',
            'refueling_time' => '2023-01-07 13:20:00',
            'telephone' => '13300133000'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilTypeId'] === '' &&
                       $taskData['oilLevelId'] === '';
            }))
            ->andReturn(['order_id' => 'internal-empty']);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-order-empty'],
                'msg' => 'success'
            ]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new JD($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
