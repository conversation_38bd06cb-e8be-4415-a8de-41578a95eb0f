<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\SP;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\SP
 */
class SPTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid card data and self card
     */
    public function testHandleWithValidCardDataAndSelfCard()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP001'
            ],
            'card_no' => 'card-123',
            'station_id' => 'station-001',
            'order_id' => 'sp-order-123',
            'oil_num' => 50.5,
            'price' => 680, // in cents
            'money' => 34340, // in cents
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI'
        ];

        $cardData = [
            'is_self' => true,
            'card_no' => '1234567890',
            'order_no' => 'card-order-123',
            'qr_code_source' => 'mobile',
            'driver_phone' => '13800138000',
            'driver_name' => 'Test Driver',
            'truck_no' => '京A12345'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-order-123'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('card-123')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, '', 'sp-order-123', 'sp', '', '')
            ->andReturn(true);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('sp-order-123', [
                'self_order_id' => 'foss-order-123',
                'reason' => 'success',
            ], false)
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['trade_mode'] === 25 && // self card
                       $arg['card_no'] === '1234567890' &&
                       $arg['app_station_id'] === 'station-001' &&
                       $arg['oil_num'] === 50.5 &&
                       $arg['oil_price'] === '6.80' && // converted from cents
                       $arg['oil_money'] === '343.40' && // converted from cents
                       $arg['pay_money'] === '343.40' &&
                       $arg['third_order_id'] === 'sp-order-123' &&
                       $arg['order_no'] === 'card-order-123' &&
                       $arg['pcode'] === 'SP001' &&
                       $arg['driver_source'] === 1 && // self card
                       $arg['client_type'] === 'mobile' &&
                       $arg['driver_phone'] === '13800138000' &&
                       $arg['driver_name'] === 'Test Driver' &&
                       $arg['truck_no'] === '京A12345';
            }))
            ->andReturn($orderData);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-order-123']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SP($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock app function
     */
    private function mockApp($redisInstance)
    {
        if (!function_exists('app')) {
            eval('function app($service = null) { return $GLOBALS["mock_app"][$service] ?? $GLOBALS["mock_app"]; }');
        }
        $GLOBALS['mock_app'] = ['redis' => $redisInstance];
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_no.92#汽油92#' => 'gasoline_92_type',
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_level.国VI' => 'level_6',
            'oil.oil_no_simple' => ['92#' => 'type_92'],
            'oil.oil_level' => ['国VI' => 'level_6']
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with non-self card
     */
    public function testHandleWithNonSelfCard()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP002'
            ],
            'card_no' => 'card-456',
            'station_id' => 'station-002',
            'order_id' => 'sp-order-456',
            'oil_num' => 30.0,
            'price' => 720, // in cents
            'money' => 21600, // in cents
            'oil_name' => '95#汽油'
        ];

        $cardData = [
            'is_self' => false, // non-self card
            'card_no' => '9876543210',
            'order_no' => 'card-order-456',
            'qr_code_source' => 'web',
            'driver_phone' => '13900139000',
            'driver_name' => 'Test Driver 2',
            'truck_no' => '沪B67890'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-order-456'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('card-456')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['trade_mode'] === 40 && // non-self card
                       $arg['driver_source'] === 2; // non-self card
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SP($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid card data
     */
    public function testHandleWithInvalidCardData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp'
            ],
            'card_no' => 'invalid-card',
            'order_id' => 'sp-order-error'
        ];

        // Mock Redis to return null
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('invalid-card')
            ->andReturn(null);

        $this->mockApp($mockRedis);

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['data']) && isset($arg['msg']) &&
                       $arg['msg'] === '二维码解析失败';
            }));

        $logic = new SP($data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000001);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with oil type and level mapping
     */
    public function testHandleWithOilTypeAndLevelMapping()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP003'
            ],
            'card_no' => 'card-mapping',
            'station_id' => 'station-003',
            'order_id' => 'sp-order-mapping',
            'oil_num' => 40.0,
            'price' => 750,
            'money' => 30000,
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国V'
        ];

        $cardData = [
            'is_self' => true,
            'card_no' => '1111111111',
            'order_no' => 'card-mapping-order',
            'qr_code_source' => 'app',
            'driver_phone' => '13700137000',
            'driver_name' => 'Mapping Driver',
            'truck_no' => '粤C11111'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-mapping-order'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('card-mapping')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfigWithMappings();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return isset($arg['oil_type']) && $arg['oil_type'] === 'diesel_type_0' &&
                       isset($arg['oil_name']) && $arg['oil_name'] === 'diesel_id' &&
                       isset($arg['oil_level']) && $arg['oil_level'] === 'level_5';
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SP($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_no.柴油0#' => 'diesel_type_0',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_level.国V' => 'level_5',
            'oil.oil_no_simple' => ['0#' => 'diesel_simple'],
            'oil.oil_level' => ['国V' => 'level_5']
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP004'
            ],
            'card_no' => 'card-error',
            'order_id' => 'sp-order-foss-error'
        ];

        $cardData = [
            'is_self' => true,
            'card_no' => '2222222222',
            'order_no' => 'error-order'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('card-error')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order failed', 5000006));

        $logic = new SP($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS order failed');
        $this->expectExceptionCode(5000006);

        $logic->handle();
    }

    /**
     * Test price conversion from cents to yuan
     */
    public function testPriceConversionFromCentsToYuan()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP005'
            ],
            'card_no' => 'card-price',
            'order_id' => 'sp-price-test',
            'price' => 1234, // 12.34 yuan in cents
            'money' => 56789 // 567.89 yuan in cents
        ];

        $cardData = [
            'is_self' => true,
            'card_no' => '3333333333',
            'order_no' => 'price-order'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify price conversion
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_price'] === '12.34' && // converted from 1234 cents
                       $arg['oil_money'] === '567.89' && // converted from 56789 cents
                       $arg['pay_money'] === '567.89';
            }))
            ->andReturn(['code' => 0, 'data' => ['order_id' => 'price-test'], 'msg' => 'success']);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SP($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handling empty oil type and level
     */
    public function testHandlingEmptyOilTypeAndLevel()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sp',
                'role_code' => 'SP006'
            ],
            'card_no' => 'card-empty',
            'order_id' => 'sp-empty-test',
            'oil_name' => '92#汽油',
            'oil_type' => '', // empty
            'oil_level' => '' // empty
        ];

        $cardData = [
            'is_self' => false,
            'card_no' => '4444444444',
            'order_no' => 'empty-order'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify empty values are handled
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_type'] === '' && // should remain empty
                       $arg['oil_level'] === ''; // should remain empty
            }))
            ->andReturn(['code' => 0, 'data' => ['order_id' => 'empty-test'], 'msg' => 'success']);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SP($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
