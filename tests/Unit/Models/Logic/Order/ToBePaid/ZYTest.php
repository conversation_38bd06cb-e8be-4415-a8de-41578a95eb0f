<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\ToBePaid;
use App\Models\Logic\Order\ToBePaid\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\ZY
 */
class ZYTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid card data
     */
    public function testHandleWithValidCardData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'app-station-123',
            'tradeId' => 'trade-123',
            'oilNum' => 50.5,
            'price' => 6.8,
            'money' => 343.4,
            'oilName' => '92#汽油',
            'oilType' => '92#',
            'oilLevel' => '国VI',
            'cardNo' => '1234567890'
        ];

        $cardData = [
            'card_no' => '1234567890',
            'is_self' => true,
            'extends' => 'test-extends',
            'access_key' => 'test-key',
            'secret' => 'test-secret'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('trade-123')
            ->andReturn(json_encode($cardData));

        // Mock app function
        $this->mockApp($mockRedis);

        // Mock config function
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('app-station-123')
            ->andReturn('gas-station-456');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::type('array'))
            ->andReturn([
                'code' => 0,
                'data' => [
                    'order_id' => 'foss-order-123',
                    'orgcode' => 'ORG123',
                    'price' => 6.8,
                    'money' => 343.4
                ],
                'msg' => 'success'
            ]);

        // Mock ToBePaid logic (for non-self cards)
        $mockToBePaidLogic = Mockery::mock('overload:' . ToBePaid::class);
        $mockToBePaidLogic->shouldReceive('handle')->never(); // is_self is true

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => [
                'tradeId' => 'foss-order-123',
                'thirdCode' => 'G7',
                'thirdName' => 'G7',
                'thirdSubCode' => 'ORG123',
                'price' => 6.8,
                'money' => 343.4
            ]
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockZY, 'zy');

        $result = $mockZY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock app function
     */
    private function mockApp($redisInstance)
    {
        if (!function_exists('app')) {
            eval('function app($service = null) { return $GLOBALS["mock_app"][$service] ?? $GLOBALS["mock_app"]; }');
        }
        $GLOBALS['mock_app'] = ['redis' => $redisInstance];
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_no_simple.92#' => 'type_92',
            'oil.oil_no_simple.95#' => 'type_95',
            'oil.oil_type' => [
                'gasoline_92' => '92#汽油',
                'gasoline_95' => '95#汽油'
            ],
            'oil.oil_level' => [
                'level_6' => '国VI',
                'level_5' => '国V'
            ]
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with invalid card data
     */
    public function testHandleWithInvalidCardData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'app-station-123',
            'tradeId' => 'invalid-trade-id'
        ];

        // Mock Redis to return null
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('invalid-trade-id')
            ->andReturn(null);

        $this->mockApp($mockRedis);

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('app-station-123')
            ->andReturn('gas-station-456');

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['data']) && isset($arg['msg']) &&
                       $arg['msg'] === '二维码解析失败';
            }));

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000001);

        $mockZY->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with station ID exception
     */
    public function testHandleWithStationIdException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'invalid-station'
        ];

        // Mock the getGasStationIdByAppStationIdAndPCode method to throw exception
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('invalid-station')
            ->andThrow(new Exception('Station not found', 4040001));

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['data']) && isset($arg['exception']);
            }));

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Station not found');
        $this->expectExceptionCode(4040001);

        $mockZY->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with non-self card
     */
    public function testHandleWithNonSelfCard()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'app-station-123',
            'tradeId' => 'trade-456',
            'oilNum' => 30.0,
            'price' => 7.2,
            'money' => 216.0,
            'oilName' => '95#汽油',
            'cardNo' => '9876543210'
        ];

        $cardData = [
            'card_no' => '9876543210',
            'is_self' => false, // Non-self card
            'extends' => 'test-extends-2',
            'access_key' => 'test-key-2',
            'secret' => 'test-secret-2'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('trade-456')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('app-station-123')
            ->andReturn('gas-station-789');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => [
                    'order_id' => 'foss-order-456',
                    'orgcode' => 'ORG456',
                    'price' => 7.2,
                    'money' => 216.0
                ],
                'msg' => 'success'
            ]);

        // Mock ToBePaid logic (should be called for non-self cards)
        $mockToBePaidLogic = Mockery::mock('overload:' . ToBePaid::class);
        $mockToBePaidLogic->shouldReceive('handle')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockZY, 'zy');

        $result = $mockZY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'app-station-123',
            'tradeId' => 'trade-error',
            'cardNo' => '1111111111'
        ];

        $cardData = [
            'card_no' => '1111111111',
            'is_self' => true,
            'extends' => 'test-extends'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('trade-error')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('app-station-123')
            ->andReturn('gas-station-error');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();

        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order failed', 5000003));

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockZY, 'zy');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS order failed');
        $this->expectExceptionCode(5000003);

        $mockZY->handle();
    }

    /**
     * Test data transformation and mapping
     */
    public function testDataTransformationAndMapping()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'stationId' => 'app-station-mapping',
            'tradeId' => 'trade-mapping',
            'oilNum' => 40.0,
            'oilName' => '柴油',
            'oilType' => '0#',
            'oilLevel' => '国V'
        ];

        $cardData = [
            'card_no' => '2222222222',
            'is_self' => true,
            'extends' => 'mapping-extends'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('trade-mapping')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);

        // Mock config with specific mappings
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_no_simple' => ['0#' => 'diesel_type'],
            'oil.oil_type' => ['diesel_id' => '柴油'],
            'oil.oil_level' => ['level_5' => '国V']
        ];

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockZY = Mockery::mock(ZY::class)->makePartial();
        $mockZY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->andReturn('gas-station-mapping');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')->andReturn([
            'code' => 0,
            'data' => ['order_id' => 'foss-mapping', 'orgcode' => 'ORG', 'price' => 7.0, 'money' => 280.0],
            'msg' => 'success'
        ]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $reflection = new ReflectionClass($mockZY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockZY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockZY, 'zy');

        $result = $mockZY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
