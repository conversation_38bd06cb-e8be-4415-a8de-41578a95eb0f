<?php

namespace Unit\Models\Logic\Order\ToBePaid\Downstream;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Common as CommonData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\AutonomousOrder;
use App\Models\Logic\Order\ToBePaid\Downstream\Simple;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS as FOSSRequest;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\Downstream\Simple
 */
class SimpleTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid data and self-operated station
     */
    public function testHandleWithValidDataAndSelfOperatedStation()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890',
                'role_code' => 'SIMPLE001'
            ],
            'order_id' => 'simple-order-123',
            'station_id' => 'station-001',
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI',
            'oil_num' => 50.0,
            'price' => 6.8,
            'money' => 340.0,
            'deduction_mode' => 1,
            'driver_name' => 'Test Driver',
            'driver_phone' => '13800138000',
            'truck_no' => '京A12345'
        ];

        $stationInfo = [
            'data' => [
                'stationInfo' => [
                    'trade_type' => 1,
                    'pcode' => 'SELF001'
                ]
            ]
        ];

        $selfSuppliers = ['SELF001', 'SELF002'];

        $fossOrderResponse = [
            'code' => 0,
            'data' => [
                'order_id' => 'foss-order-123'
            ],
            'msg' => 'success'
        ];

        // Mock config function
        $this->mockConfig();

        // Mock FOSS_STATIONRequest
        $mockStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockStationRequest->shouldReceive('handle')
            ->once()
            ->with("v1/station/getStationById", ['id' => 'station-001'])
            ->andReturn($stationInfo);

        // Mock CommonData
        $mockCommonData = Mockery::mock('alias:' . CommonData::class);
        $mockCommonData->shouldReceive('RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM')
            ->andReturn([1, 2, 3]);

        // Mock FOSSRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('g7s.station.getCanPcode', ['is_self_operated' => 1])
            ->andReturn(['data' => array_flip($selfSuppliers)]);

        // Mock AutonomousOrder
        $mockAutonomousOrder = Mockery::mock('overload:' . AutonomousOrder::class);
        $mockAutonomousOrder->shouldReceive('checkWorkerExists')->never(); // self-operated

        // Mock FOSS_ORDERRequest
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['card_no'] === '1234567890' &&
                       $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === 'type_92' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['trade_mode'] === 10 && // self-operated
                       $arg['driver_source'] === 2;
            }))
            ->andReturn($fossOrderResponse);

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, 'foss-order-123', 'simple-order-123', Mockery::any(), "", "", Mockery::type('string'))
            ->andReturn(true);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-order-123']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no.92#汽油92#' => 'type_92',
            'oil.oil_no.柴油0#' => 'type_0',
            'oil.oil_level.国VI' => 'level_6',
            'oil.oil_level.国V' => 'level_5',
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with org_code override
     */
    public function testHandleWithOrgCodeOverride()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890',
                'role_code' => 'SIMPLE002'
            ],
            'org_code' => 'ORG123',
            'order_id' => 'simple-order-456',
            'station_id' => 'station-002',
            'oil_name' => '95#汽油',
            'oil_num' => 30.0,
            'price' => 7.2,
            'money' => 216.0,
            'deduction_mode' => 2
        ];

        $realAuthInfo = [
            'card_no' => '9876543210'
        ];

        $stationInfo = [
            'data' => [
                'stationInfo' => [
                    'trade_type' => 2,
                    'pcode' => 'THIRD001'
                ]
            ]
        ];

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->once()
            ->with('ORG123', true)
            ->andReturn($realAuthInfo);

        $this->mockConfig();

        // Mock FOSS_STATIONRequest
        $mockStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockStationRequest->shouldReceive('handle')->once()->andReturn($stationInfo);

        // Mock CommonData
        $mockCommonData = Mockery::mock('alias:' . CommonData::class);
        $mockCommonData->shouldReceive('RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM')
            ->andReturn([1, 2, 3]);

        // Mock FOSSRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn(['data' => ['SELF001' => 'SELF001']]);

        // Mock AutonomousOrder
        $mockAutonomousOrder = Mockery::mock('overload:' . AutonomousOrder::class);
        $mockAutonomousOrder->shouldReceive('checkWorkerExists')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['card_no'] === '9876543210' && // should use org_code card
                       $arg['trade_mode'] === 32; // autonomous order
            }))
            ->andReturn(['code' => 0, 'data' => ['order_id' => 'foss-order-456'], 'msg' => 'success']);

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid oil name
     */
    public function testHandleWithInvalidOilName()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890'
            ],
            'order_id' => 'simple-order-error',
            'oil_name' => 'invalid_oil'
        ];

        $this->mockConfig();

        // Mock responseFormat function for error case
        $expectedResponse = new JsonResponse([
            'code' => 4120014,
            'msg' => 'Invalid oil name'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid org_code
     */
    public function testHandleWithInvalidOrgCode()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890'
            ],
            'org_code' => 'INVALID_ORG',
            'order_id' => 'simple-order-invalid-org'
        ];

        // Mock AuthInfoData to return empty result
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->once()
            ->with('INVALID_ORG', true)
            ->andReturn([]);

        // Mock responseFormat function for error case
        $expectedResponse = new JsonResponse([
            'code' => 4120402,
            'msg' => 'Invalid organization code'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid station trade type
     */
    public function testHandleWithInvalidStationTradeType()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890'
            ],
            'order_id' => 'simple-order-invalid-station',
            'station_id' => 'invalid-station',
            'oil_name' => '92#汽油'
        ];

        $stationInfo = [
            'data' => [
                'stationInfo' => [
                    'trade_type' => 99 // invalid trade type
                ]
            ]
        ];

        $this->mockConfig();

        // Mock FOSS_STATIONRequest
        $mockStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockStationRequest->shouldReceive('handle')->once()->andReturn($stationInfo);

        // Mock CommonData
        $mockCommonData = Mockery::mock('alias:' . CommonData::class);
        $mockCommonData->shouldReceive('RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM')
            ->andReturn([1, 2, 3]); // 99 is not in this list

        $logic = new Simple($data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000117);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with third-party station and no autonomous order
     */
    public function testHandleWithThirdPartyStationAndNoAutonomousOrder()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890',
                'role_code' => 'SIMPLE003'
            ],
            'order_id' => 'simple-order-third-party',
            'station_id' => 'station-third',
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国V',
            'oil_num' => 60.0,
            'price' => 6.5,
            'money' => 390.0,
            'deduction_mode' => 1
        ];

        $stationInfo = [
            'data' => [
                'stationInfo' => [
                    'trade_type' => 2,
                    'pcode' => 'THIRD002'
                ]
            ]
        ];

        $this->mockConfig();

        // Mock FOSS_STATIONRequest
        $mockStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockStationRequest->shouldReceive('handle')->once()->andReturn($stationInfo);

        // Mock CommonData
        $mockCommonData = Mockery::mock('alias:' . CommonData::class);
        $mockCommonData->shouldReceive('RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM')
            ->andReturn([1, 2, 3]);

        // Mock FOSSRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn(['data' => ['SELF001' => 'SELF001']]); // THIRD002 not in self-operated

        // Mock AutonomousOrder
        $mockAutonomousOrder = Mockery::mock('overload:' . AutonomousOrder::class);
        $mockAutonomousOrder->shouldReceive('checkWorkerExists')->once()->andReturn(false);

        // Mock FOSS_ORDERRequest
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === 'diesel_id' &&
                       $arg['oil_type'] === 'type_0' &&
                       $arg['oil_level'] === 'level_5' &&
                       $arg['trade_mode'] === 30; // third-party without autonomous
            }))
            ->andReturn(['code' => 0, 'data' => ['order_id' => 'foss-third-party'], 'msg' => 'success']);

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test oil type and level mapping with empty values
     */
    public function testOilTypeAndLevelMappingWithEmptyValues()
    {
        $data = [
            'auth_data' => [
                'card_no' => '1234567890'
            ],
            'order_id' => 'simple-order-empty-mapping',
            'station_id' => 'station-empty',
            'oil_name' => '92#汽油',
            'oil_type' => '', // empty
            'oil_level' => '', // empty
            'oil_num' => 25.0,
            'price' => 6.9,
            'money' => 172.5,
            'deduction_mode' => 1
        ];

        $stationInfo = [
            'data' => [
                'stationInfo' => [
                    'trade_type' => 1,
                    'pcode' => 'SELF003'
                ]
            ]
        ];

        $this->mockConfig();

        // Mock FOSS_STATIONRequest
        $mockStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockStationRequest->shouldReceive('handle')->once()->andReturn($stationInfo);

        // Mock CommonData
        $mockCommonData = Mockery::mock('alias:' . CommonData::class);
        $mockCommonData->shouldReceive('RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM')
            ->andReturn([1, 2, 3]);

        // Mock FOSSRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSSRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn(['data' => ['SELF003' => 'SELF003']]);

        // Mock FOSS_ORDERRequest and verify empty values are handled
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === '' && // should remain empty
                       $arg['oil_level'] === ''; // should remain empty
            }))
            ->andReturn(['code' => 0, 'data' => ['order_id' => 'foss-empty'], 'msg' => 'success']);

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Simple($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
