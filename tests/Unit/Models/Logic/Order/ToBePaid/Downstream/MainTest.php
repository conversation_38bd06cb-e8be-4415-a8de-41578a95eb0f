<?php

namespace Unit\Models\Logic\Order\ToBePaid\Downstream;

use App\Models\Logic\Order\ToBePaid\Downstream\AD;
use App\Models\Logic\Order\ToBePaid\Downstream\DESP2A6LA9;
use App\Models\Logic\Order\ToBePaid\Downstream\DESP2C637M;
use App\Models\Logic\Order\ToBePaid\Downstream\DESP2H8BBD;
use App\Models\Logic\Order\ToBePaid\Downstream\FY;
use App\Models\Logic\Order\ToBePaid\Downstream\GBDW;
use App\Models\Logic\Order\ToBePaid\Downstream\HLL;
use App\Models\Logic\Order\ToBePaid\Downstream\HR;
use App\Models\Logic\Order\ToBePaid\Downstream\HTX;
use App\Models\Logic\Order\ToBePaid\Downstream\KY;
use App\Models\Logic\Order\ToBePaid\Downstream\Main;
use App\Models\Logic\Order\ToBePaid\Downstream\Rq;
use App\Models\Logic\Order\ToBePaid\Downstream\SFSX;
use App\Models\Logic\Order\ToBePaid\Downstream\Simple;
use App\Models\Logic\Order\ToBePaid\Downstream\Sp;
use App\Models\Logic\Order\ToBePaid\Downstream\YGY;
use App\Models\Logic\Order\ToBePaid\Downstream\ZEY;
use App\Models\Logic\Order\ToBePaid\Downstream\Zj;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use TypeError;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\Downstream\Main
 */
class MainTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::__construct
     * Test constructor with default Simple worker
     */
    public function testConstructorWithDefaultSimpleWorker()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => '' // empty should use Simple
            ]
        ];

        $mockWorker = Mockery::mock(Simple::class);
        Mockery::mock('overload:' . Simple::class, $mockWorker);

        $logic = new Main($parameter);
        $this->assertInstanceOf(Main::class, $logic);
    }

    /**
     * @covers ::__construct
     * Test constructor with SP platform worker
     */
    public function testConstructorWithSpPlatformWorker()
    {
        $spCodes = ['SPTXXTZ', 'SPT5CWE', 'SPT3SB7', 'SPTLHLX', 'SPTCDNF', 'SPT76YH'];

        foreach ($spCodes as $spCode) {
            $parameter = [
                'auth_data' => [
                    'real_name_abbreviation' => $spCode
                ]
            ];

            $mockWorker = Mockery::mock(Sp::class);
            Mockery::mock('overload:' . Sp::class, $mockWorker);

            $logic = new Main($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf(Sp::class, $worker);
        }
    }

    /**
     * @covers ::__construct
     * Test constructor with various platform workers
     */
    public function testConstructorWithVariousPlatformWorkers()
    {
        $platformMappings = [
            'rq' => Rq::class,
            'htx' => HTX::class,
            'ygy' => YGY::class,
            'fy' => FY::class,
            'gbdw' => GBDW::class,
            'zj' => Zj::class,
            'hr' => HR::class,
            'hll' => HLL::class,
            'ad' => AD::class,
            'zey' => ZEY::class,
            'ky' => KY::class,
            'sfsx' => SFSX::class,
        ];

        foreach ($platformMappings as $platform => $expectedClass) {
            $parameter = [
                'auth_data' => [
                    'real_name_abbreviation' => $platform
                ]
            ];

            $mockWorker = Mockery::mock($expectedClass);
            Mockery::mock('overload:' . $expectedClass, $mockWorker);

            $logic = new Main($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf($expectedClass, $worker);
        }
    }

    /**
     * @covers ::__construct
     * Test constructor with DESP platform workers
     */
    public function testConstructorWithDespPlatformWorkers()
    {
        $despMappings = [
            'desp|2H8BBD' => DESP2H8BBD::class,
            'desp|2A6LA9' => DESP2A6LA9::class,
            'desp|2C637M' => DESP2C637M::class,
        ];

        foreach ($despMappings as $platform => $expectedClass) {
            $parameter = [
                'auth_data' => [
                    'real_name_abbreviation' => $platform
                ]
            ];

            $mockWorker = Mockery::mock($expectedClass);
            Mockery::mock('overload:' . $expectedClass, $mockWorker);

            $logic = new Main($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf($expectedClass, $worker);
        }
    }

    /**
     * @covers ::__construct
     * Test constructor with unknown platform defaults to Simple
     */
    public function testConstructorWithUnknownPlatformDefaultsToSimple()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'unknown_platform'
            ]
        ];

        $mockWorker = Mockery::mock(Simple::class);
        Mockery::mock('overload:' . Simple::class, $mockWorker);

        $logic = new Main($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('worker');
        $property->setAccessible(true);
        $worker = $property->getValue($logic);

        $this->assertInstanceOf(Simple::class, $worker);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'downstream-order-123',
            'station_id' => 'station-001'
        ];

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'downstream-order-123-456']
        ]);

        $mockWorker = Mockery::mock(HTX::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);

        Mockery::mock('overload:' . HTX::class, $mockWorker);

        $logic = new Main($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with Simple worker
     */
    public function testHandleWithSimpleWorker()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => ''
            ],
            'order_id' => 'simple-order-456',
            'oil_name' => '92#汽油',
            'oil_num' => 50.0,
            'price' => 6.8,
            'money' => 340.0
        ];

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-order-456']
        ]);

        $mockWorker = Mockery::mock(Simple::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);

        Mockery::mock('overload:' . Simple::class, $mockWorker);

        $logic = new Main($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with exception from worker
     */
    public function testHandleWithExceptionFromWorker()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'ad'
            ],
            'order_id' => 'error-order'
        ];

        $mockWorker = Mockery::mock(AD::class);
        $mockWorker->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Worker error', 5000008));

        Mockery::mock('overload:' . AD::class, $mockWorker);

        $logic = new Main($parameter);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Worker error');
        $this->expectExceptionCode(5000008);

        $logic->handle();
    }

    /**
     * Test worker mapping completeness
     */
    public function testWorkerMappingCompleteness()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'rq'
            ]
        ];

        $mockWorker = Mockery::mock(Rq::class);
        Mockery::mock('overload:' . Rq::class, $mockWorker);

        $logic = new Main($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('workerMapping');
        $property->setAccessible(true);
        $mapping = $property->getValue($logic);

        // Verify all expected platforms are mapped
        $expectedPlatforms = [
            '', 'SPTXXTZ', 'SPT5CWE', 'SPT3SB7', 'SPTLHLX', 'SPTCDNF',
            'SPT76YH', 'SPSL8KY', 'SPT5GZG', 'SPTMKCF', 'SPTHZTD',
            'SPTG7PR', 'SPTRR3N', 'SPTNGHH', 'SPTYW4A', 'SPTHSQW',
            'SPT5RLT', 'rq', 'htx', 'ygy', 'fy', 'desp|2H8BBD',
            'desp|2A6LA9', 'gbdw', 'desp|2C637M', 'zj', 'hr', 'hll',
            'ad', 'zey', 'ky', 'sfsx'
        ];

        foreach ($expectedPlatforms as $platform) {
            $this->assertArrayHasKey($platform, $mapping);
        }

        // Verify SP platforms map to Sp class
        $spPlatforms = [
            'SPTXXTZ', 'SPT5CWE', 'SPT3SB7', 'SPTLHLX', 'SPTCDNF',
            'SPT76YH', 'SPSL8KY', 'SPT5GZG', 'SPTMKCF', 'SPTHZTD',
            'SPTG7PR', 'SPTRR3N', 'SPTNGHH', 'SPTYW4A', 'SPTHSQW', 'SPT5RLT'
        ];

        foreach ($spPlatforms as $platform) {
            $this->assertEquals(Sp::class, $mapping[$platform]);
        }

        // Verify empty string maps to Simple class
        $this->assertEquals(Simple::class, $mapping['']);
    }

    /**
     * Test constructor with missing auth_data
     */
    public function testConstructorWithMissingAuthData()
    {
        $parameter = [];

        $this->expectException(TypeError::class);

        new Main($parameter);
    }

    /**
     * Test constructor with missing real_name_abbreviation
     */
    public function testConstructorWithMissingRealNameAbbreviation()
    {
        $parameter = [
            'auth_data' => []
        ];

        $mockWorker = Mockery::mock(Simple::class);
        Mockery::mock('overload:' . Simple::class, $mockWorker);

        $logic = new Main($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('worker');
        $property->setAccessible(true);
        $worker = $property->getValue($logic);

        // Should default to Simple when real_name_abbreviation is missing
        $this->assertInstanceOf(Simple::class, $worker);
    }

    /**
     * Test data is properly passed to worker
     */
    public function testDataIsProperlyPassedToWorker()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'role_code' => 'YGY001',
                'card_no' => '1234567890'
            ],
            'order_id' => 'ygy-order-789',
            'station_id' => 'station-ygy-001',
            'oil_name' => '95#汽油',
            'oil_num' => 40.0,
            'price' => 7.2,
            'money' => 288.0,
            'companyId' => 'company-123'
        ];

        // Verify that the worker is constructed with the correct data
        $mockWorker = Mockery::mock(YGY::class);
        Mockery::mock('overload:' . YGY::class)
            ->shouldReceive('__construct')
            ->once()
            ->with($parameter)
            ->andReturn($mockWorker);

        $mockWorker->shouldReceive('handle')
            ->once()
            ->andReturn(new JsonResponse(['code' => 0]));

        $logic = new Main($parameter);
        $logic->handle();
    }

    /**
     * Test all SP platform codes are properly mapped
     */
    public function testAllSpPlatformCodesAreMapped()
    {
        $allSpCodes = [
            'SPTXXTZ', 'SPT5CWE', 'SPT3SB7', 'SPTLHLX', 'SPTCDNF',
            'SPT76YH', 'SPSL8KY', 'SPT5GZG', 'SPTMKCF', 'SPTHZTD',
            'SPTG7PR', 'SPTRR3N', 'SPTNGHH', 'SPTYW4A', 'SPTHSQW', 'SPT5RLT'
        ];

        foreach ($allSpCodes as $spCode) {
            $parameter = [
                'auth_data' => [
                    'real_name_abbreviation' => $spCode
                ]
            ];

            $mockWorker = Mockery::mock(Sp::class);
            $mockWorker->shouldReceive('handle')
                ->once()
                ->andReturn(new JsonResponse(['code' => 0, 'data' => ['trade_id' => 'sp-test']]));

            Mockery::mock('overload:' . Sp::class, $mockWorker);

            $logic = new Main($parameter);
            $result = $logic->handle();

            $this->assertInstanceOf(JsonResponse::class, $result);
        }
    }

    /**
     * Test handle method returns correct response type
     */
    public function testHandleReturnsCorrectResponseType()
    {
        $parameter = [
            'auth_data' => [
                'real_name_abbreviation' => 'gbdw'
            ]
        ];

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => [
                'trade_id' => 'gbdw-trade-123',
                'order_status' => 'pending'
            ]
        ]);

        $mockWorker = Mockery::mock(GBDW::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);

        Mockery::mock('overload:' . GBDW::class, $mockWorker);

        $logic = new Main($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse->getContent(), $result->getContent());
        $this->assertEquals($expectedResponse->getStatusCode(), $result->getStatusCode());
    }
}
