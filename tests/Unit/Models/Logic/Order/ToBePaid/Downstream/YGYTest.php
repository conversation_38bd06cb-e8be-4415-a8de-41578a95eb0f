<?php

namespace Unit\Models\Logic\Order\ToBePaid\Downstream;

use App\Jobs\QueryOrderToThirdParty;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\Downstream\YGY;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\Downstream\YGY
 */
class YGYTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid data and successful order creation
     */
    public function testHandleWithValidDataAndSuccessfulOrderCreation()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '1234567890'
            ],
            'orderSn' => 'ygy-order-123',
            'companyId' => 'company-001',
            'stationId' => 'station-001',
            'oilName' => '92#汽油',
            'oilType' => '92#',
            'oilLevel' => '国VI',
            'oilNum' => 50.0,
            'price' => 6.8,
            'money' => 340.0,
            'driverPhone' => '13800138000',
            'driverName' => 'YGY Driver',
            'truckNo' => '京A12345'
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-order-123'],
            'msg' => 'success'
        ];

        $tradePayResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'trade-ygy-123']
        ]);

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, '', 'ygy-order-123', 'ygy', '', '', Mockery::type('string'))
            ->andReturn(true);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('ygy-order-123', [
                'self_order_id' => 'foss-ygy-order-123',
                'reason' => 'success',
            ], false, 'ygy')
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['card_no'] === '1234567890' &&
                       $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === 'type_92' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['oil_num'] === 50.0 &&
                       $arg['oil_price'] === 6.8 &&
                       $arg['oil_money'] === 340.0 &&
                       $arg['pay_money'] === 340.0 &&
                       $arg['third_order_id'] === 'ygy-order-123' &&
                       $arg['driver_phone'] === '13800138000' &&
                       $arg['driver_name'] === 'YGY Driver' &&
                       $arg['truck_no'] === '京A12345' &&
                       $arg['trade_mode'] === 32 &&
                       $arg['driver_source'] === 2;
            }))
            ->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('handle')
            ->once()
            ->with(true)
            ->andReturn($tradePayResponse);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')
            ->once()
            ->with(
                Mockery::type(Carbon::class),
                Mockery::type(QueryOrderToThirdParty::class),
                '',
                'adapter_deal_trade'
            );

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-ygy-order-123']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['92#' => 'type_92', '95#' => 'type_95'],
            'oil.oil_level' => ['国VI' => 'level_6', '国V' => 'level_5']
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '1234567890'
            ],
            'orderSn' => 'ygy-order-error',
            'companyId' => 'company-error',
            'oilName' => '92#汽油'
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order failed', 5000014));

        $logic = new YGY($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS order failed');
        $this->expectExceptionCode(5000014);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with empty oil type and level
     */
    public function testHandleWithEmptyOilTypeAndLevel()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '9876543210'
            ],
            'orderSn' => 'ygy-order-empty',
            'companyId' => 'company-empty',
            'oilName' => '95#汽油',
            'oilType' => '', // empty
            'oilLevel' => '', // empty
            'oilNum' => 30.0,
            'price' => 7.2,
            'money' => 216.0
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-empty'],
            'msg' => 'success'
        ];

        $tradePayResponse = new JsonResponse(['code' => 0]);

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify empty values are handled
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === 'gasoline_95' &&
                       $arg['oil_type'] === '' && // should remain empty
                       $arg['oil_level'] === ''; // should remain empty
            }))
            ->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('handle')->once()->andReturn($tradePayResponse);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with oil type and level mapping
     */
    public function testHandleWithOilTypeAndLevelMapping()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '1111111111'
            ],
            'orderSn' => 'ygy-order-mapping',
            'companyId' => 'company-mapping',
            'oilName' => '柴油',
            'oilType' => '0#',
            'oilLevel' => '国V',
            'oilNum' => 40.0,
            'price' => 6.5,
            'money' => 260.0
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-mapping'],
            'msg' => 'success'
        ];

        $tradePayResponse = new JsonResponse(['code' => 0]);

        // Mock config with specific mappings
        $this->mockConfigWithMappings();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify mappings
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === 'diesel_id' &&
                       $arg['oil_type'] === 'type_0' &&
                       $arg['oil_level'] === 'level_5';
            }))
            ->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('handle')->once()->andReturn($tradePayResponse);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['0#' => 'type_0'],
            'oil.oil_level' => ['国V' => 'level_5']
        ];
    }

    /**
     * Test data structure passed to FOSS_ORDER
     */
    public function testDataStructurePassedToFossOrder()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '2222222222'
            ],
            'orderSn' => 'ygy-order-structure',
            'companyId' => 'company-structure',
            'stationId' => 'station-structure',
            'oilName' => '92#汽油',
            'oilType' => '92#',
            'oilLevel' => '国VI',
            'oilNum' => 45.5,
            'price' => 6.9,
            'money' => 313.95,
            'driverPhone' => '13700137000',
            'driverName' => 'Structure Driver',
            'truckNo' => '粤C11111'
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-structure'],
            'msg' => 'success'
        ];

        $tradePayResponse = new JsonResponse(['code' => 0]);

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify complete data structure
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                $requiredFields = [
                    'card_no', 'oil_num', 'oil_price', 'oil_money', 'pay_money',
                    'oil_type', 'oil_name', 'oil_level', 'third_order_id',
                    'trade_mode', 'driver_source', 'driver_phone', 'driver_name', 'truck_no'
                ];

                foreach ($requiredFields as $field) {
                    if (!array_key_exists($field, $arg)) {
                        return false;
                    }
                }

                return $arg['card_no'] === '2222222222' &&
                       $arg['oil_num'] === 45.5 &&
                       $arg['oil_price'] === 6.9 &&
                       $arg['oil_money'] === 313.95 &&
                       $arg['pay_money'] === 313.95 &&
                       $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === 'type_92' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['third_order_id'] === 'ygy-order-structure' &&
                       $arg['trade_mode'] === 32 &&
                       $arg['driver_source'] === 2 &&
                       $arg['driver_phone'] === '13700137000' &&
                       $arg['driver_name'] === 'Structure Driver' &&
                       $arg['truck_no'] === '粤C11111';
            }))
            ->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('handle')->once()->andReturn($tradePayResponse);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test TradePaySimpleLogic integration
     */
    public function testTradePaySimpleLogicIntegration()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '3333333333'
            ],
            'orderSn' => 'ygy-order-trade-pay',
            'companyId' => 'company-trade-pay'
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-trade-pay'],
            'msg' => 'success'
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')->once()->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic and verify parameters
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('__construct')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['auth_data']['real_name_abbreviation'] === 'ygy' &&
                       $arg['trade_id'] === 'foss-ygy-trade-pay' &&
                       $arg['order_id'] === 'ygy-order-trade-pay' &&
                       $arg['pay_status'] === 1 &&
                       $arg['pay_reason'] === '' &&
                       $arg['updatePlatformReason'] === false;
            }))
            ->andReturnSelf();

        $mockTradePayLogic->shouldReceive('handle')
            ->once()
            ->with(true)
            ->andReturn(new JsonResponse(['code' => 0]));

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')
            ->once()
            ->with(
                Mockery::on(function ($carbon) {
                    return $carbon instanceof Carbon;
                }),
                Mockery::on(function ($job) {
                    return $job instanceof QueryOrderToThirdParty;
                }),
                '',
                'adapter_deal_trade'
            );

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test queue job creation with correct parameters
     */
    public function testQueueJobCreationWithCorrectParameters()
    {
        $data = [
            'auth_data' => [
                'real_name_abbreviation' => 'ygy',
                'card_no' => '4444444444'
            ],
            'orderSn' => 'ygy-order-queue',
            'companyId' => 'company-queue'
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'foss-ygy-queue'],
            'msg' => 'success'
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')->once()->andReturn($fossOrderResponse);

        // Mock TradePaySimpleLogic
        $mockTradePayLogic = Mockery::mock('overload:' . TradePaySimpleLogic::class);
        $mockTradePayLogic->shouldReceive('handle')->once()->andReturn(new JsonResponse(['code' => 0]));

        // Mock Queue facade and verify job parameters
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('later')
            ->once()
            ->with(
                Mockery::on(function ($carbon) {
                    // Verify delay is approximately 1 second
                    return $carbon instanceof Carbon;
                }),
                Mockery::on(function ($job) {
                    // Verify job type and data
                    return $job instanceof QueryOrderToThirdParty;
                }),
                '',
                'adapter_deal_trade'
            );

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new YGY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
