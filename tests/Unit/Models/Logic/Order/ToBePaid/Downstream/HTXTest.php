<?php

namespace Unit\Models\Logic\Order\ToBePaid\Downstream;

use App\Jobs\PushTradeToGas;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\Downstream\HTX;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Mockery;
use PHPUnit\Framework\TestCase;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\Downstream\HTX
 */
class HTXTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with complete data and task push
     */
    public function testHandleWithCompleteDataAndTaskPush()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-123',
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI',
            'refueling_time' => '2023-01-01 12:00:00',
            'oil_num' => 50.0,
            'price' => 6.8,
            'money' => 340.0,
            'station_id' => 'station-001',
            'driver_phone' => '13800138000',
            'driver_name' => 'HTX Driver',
            'truck_no' => '京A12345'
        ];

        $orderAssocResult = [
            'id' => 456,
            'allowPushTask' => true
        ];

        // Mock config function
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['self_order_status'] === 1 &&
                       $arg['platform_order_id'] === 'htx-order-123' &&
                       $arg['platform_name'] === 'htx' &&
                       $arg['platform_order_status'] === 1 &&
                       $arg['refueling_time'] === '2023-01-01 12:00:00' &&
                       isset($arg['extend']);
            }))
            ->andReturn($orderAssocResult);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('push')
            ->once()
            ->with(
                Mockery::type(PushTradeToGas::class),
                '',
                'adapter_deal_trade'
            );

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'htx-order-123-456']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['92#' => 'type_92', '0#' => 'type_0'],
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with oil type mapping
     */
    public function testHandleWithOilTypeMapping()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-456',
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国V',
            'refueling_time' => '2023-01-02 14:30:00',
            'oil_num' => 40.0,
            'price' => 6.5,
            'money' => 260.0
        ];

        $orderAssocResult = [
            'id' => 789,
            'allowPushTask' => false // no task push
        ];

        // Mock config function with specific mappings
        $this->mockConfigWithMappings();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilNameId'] === 'diesel_id' &&
                       $taskData['oilTypeId'] === 'type_0' &&
                       $taskData['oilLevelId'] === '' && // empty for HTX
                       $taskData['oil_time'] === '2023-01-02 14:30:00';
            }))
            ->andReturn($orderAssocResult);

        // Mock Queue facade - should not be called
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('push')->never();

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'htx-order-456-789']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['0#' => 'type_0'],
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with empty oil type
     */
    public function testHandleWithEmptyOilType()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-empty-type',
            'oil_name' => '95#汽油',
            'oil_type' => '', // empty
            'oil_level' => '国VI',
            'refueling_time' => '2023-01-03 16:45:00',
            'oil_num' => 30.0,
            'price' => 7.2,
            'money' => 216.0
        ];

        $orderAssocResult = [
            'id' => 101,
            'allowPushTask' => true
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilNameId'] === 'gasoline_95' &&
                       $taskData['oilTypeId'] === '' && // should remain empty
                       $taskData['oilLevelId'] === '';
            }))
            ->andReturn($orderAssocResult);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('push')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with missing oil type config
     */
    public function testHandleWithMissingOilTypeConfig()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-missing-config',
            'oil_name' => '98#汽油', // not in config
            'oil_type' => '98#',
            'refueling_time' => '2023-01-04 10:00:00'
        ];

        $orderAssocResult = [
            'id' => 202,
            'allowPushTask' => false
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilNameId'] === '' && // should be empty due to missing config
                       $taskData['oilTypeId'] === '';
            }))
            ->andReturn($orderAssocResult);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test data transformation for extend field
     */
    public function testDataTransformationForExtendField()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-extend',
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI',
            'refueling_time' => '2023-01-05 11:30:00',
            'oil_num' => 45.0,
            'price' => 6.9,
            'money' => 310.5,
            'station_id' => 'station-extend',
            'driver_phone' => '13900139000',
            'custom_field' => 'custom_value'
        ];

        $orderAssocResult = [
            'id' => 303,
            'allowPushTask' => true
        ];

        $this->mockConfig();

        // Mock OrderAssocData and verify extend field structure
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $extend = json_decode($arg['extend'], true);
                return isset($extend['taskData']) &&
                       isset($extend['name_abbreviation']) &&
                       $extend['name_abbreviation'] === 'htx' &&
                       $extend['taskData']['order_id'] === 'htx-order-extend' &&
                       $extend['taskData']['oil_time'] === '2023-01-05 11:30:00' &&
                       $extend['taskData']['custom_field'] === 'custom_value';
            }))
            ->andReturn($orderAssocResult);

        // Mock Queue facade
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('push')
            ->once()
            ->with(
                Mockery::on(function ($job) {
                    return $job instanceof PushTradeToGas;
                }),
                '',
                'adapter_deal_trade'
            );

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test oil level handling (always empty for HTX)
     */
    public function testOilLevelHandling()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-order-level',
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国VI', // should be set to empty
            'refueling_time' => '2023-01-06 09:15:00'
        ];

        $orderAssocResult = [
            'id' => 404,
            'allowPushTask' => false
        ];

        $this->mockConfig();

        // Mock OrderAssocData and verify oil level is empty
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->with(Mockery::on(function ($arg) {
                $taskData = json_decode($arg['extend'], true)['taskData'];
                return $taskData['oilLevelId'] === ''; // should always be empty for HTX
            }))
            ->andReturn($orderAssocResult);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test queue job data structure
     */
    public function testQueueJobDataStructure()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx',
                'role_code' => 'HTX001'
            ],
            'order_id' => 'htx-order-queue',
            'oil_name' => '92#汽油',
            'refueling_time' => '2023-01-07 13:20:00'
        ];

        $orderAssocResult = [
            'id' => 505,
            'allowPushTask' => true
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->andReturn($orderAssocResult);

        // Mock Queue facade and verify job parameters
        $mockQueue = Mockery::mock('alias:' . Queue::class);
        $mockQueue->shouldReceive('push')
            ->once()
            ->with(
                Mockery::on(function ($job) use ($data, $orderAssocResult) {
                    // Verify that the job is created with correct data
                    return $job instanceof PushTradeToGas;
                }),
                '',
                'adapter_deal_trade'
            );

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test response format with virtual order ID
     */
    public function testResponseFormatWithVirtualOrderId()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'htx',
                'real_name_abbreviation' => 'htx'
            ],
            'order_id' => 'htx-virtual-order',
            'oil_name' => '95#汽油',
            'refueling_time' => '2023-01-08 15:45:00'
        ];

        $orderAssocResult = [
            'id' => 606,
            'allowPushTask' => false
        ];

        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insertBySearchForPlatform')
            ->once()
            ->andReturn($orderAssocResult);

        // Mock responseFormat and verify virtual order ID format
        $this->mockFunction('responseFormat', function ($code, $data) {
            if ($code === 0 && isset($data['trade_id']) && $data['trade_id'] === 'htx-virtual-order-606') {
                return new JsonResponse(['code' => 0, 'data' => $data]);
            }
            return new JsonResponse(['code' => $code, 'data' => $data]);
        });

        $logic = new HTX($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
