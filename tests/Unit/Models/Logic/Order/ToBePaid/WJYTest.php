<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\ToBePaid;
use App\Models\Logic\Order\ToBePaid\WJY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\WJY
 */
class WJYTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid data and self card
     */
    public function testHandleWithValidDataAndSelfCard()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY001'
            ],
            'oilsStationId' => 'wjy-app-station-123',
            'orderSn' => 'wjy-order-123',
            'foreign_driver_id' => 'wjy-driver-123',
            'oilNum' => 50.0,
            'amountPrice' => 340.0,
            'oilName' => '92#汽油',
            'oilType' => '92#',
            'oilLevel' => '国VI'
        ];

        $cardData = [
            'card_no' => '1234567890',
            'order_no' => 'wjy-card-order-123',
            'is_self' => true,
            'qr_code_source' => 'mobile',
            'driver_phone' => '13800138000',
            'driver_name' => 'WJY Driver',
            'truck_no' => '京A12345',
            'name_abbreviation' => 'wjy',
            'secret' => 'wjy-secret'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-wjy-order-123'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('wjy-driver-123')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('wjy-app-station-123')
            ->andReturn('gas-station-456');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, '', 'wjy-order-123', Mockery::any(), '', '', Mockery::type('string'))
            ->andReturn($mockOrderAssocModel);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('wjy-order-123', [
                'self_order_id' => 'foss-wjy-order-123',
                'reason' => 'success',
            ], false, Mockery::any())
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['card_no'] === '1234567890' &&
                       $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === 'type_92' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['trade_mode'] === 25 && // self card
                       $arg['driver_source'] === 1;
            }))
            ->andReturn($orderData);

        // Mock ToBePaid logic (should not be called for self cards)
        $mockToBePaidLogic = Mockery::mock('overload:' . ToBePaid::class);
        $mockToBePaidLogic->shouldReceive('handle')->never();

        // Mock responseFormatForWjy function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['foreign_unique_id' => 'foss-wjy-order-123']
        ]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock app function
     */
    private function mockApp($redisInstance)
    {
        if (!function_exists('app')) {
            eval('function app($service = null) { return $GLOBALS["mock_app"][$service] ?? $GLOBALS["mock_app"]; }');
        }
        $GLOBALS['mock_app'] = ['redis' => $redisInstance];
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['92#' => 'type_92', '95#' => 'type_95'],
            'oil.oil_level' => ['国VI' => 'level_6', '国V' => 'level_5']
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with non-self card
     */
    public function testHandleWithNonSelfCard()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY002'
            ],
            'oilsStationId' => 'wjy-app-station-456',
            'orderSn' => 'wjy-order-456',
            'foreign_driver_id' => 'wjy-driver-456',
            'oilNum' => 30.0,
            'amountPrice' => 216.0,
            'oilName' => '95#汽油'
        ];

        $cardData = [
            'card_no' => '9876543210',
            'order_no' => 'wjy-card-order-456',
            'is_self' => false, // non-self card
            'qr_code_source' => 'web',
            'driver_phone' => '13900139000',
            'driver_name' => 'WJY Driver 2',
            'truck_no' => '沪B67890',
            'name_abbreviation' => 'wjy',
            'secret' => 'wjy-secret-2'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-wjy-order-456'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('wjy-driver-456')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('wjy-app-station-456')
            ->andReturn('gas-station-789');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn($mockOrderAssocModel);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['trade_mode'] === 40 && // non-self card
                       $arg['driver_source'] === 2;
            }))
            ->andReturn($orderData);

        // Mock ToBePaid logic (should be called for non-self cards)
        $mockToBePaidLogic = Mockery::mock('overload:' . ToBePaid::class);
        $mockToBePaidLogic->shouldReceive('handle')->once();

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid station ID
     */
    public function testHandleWithInvalidStationId()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY003'
            ],
            'oilsStationId' => 'invalid-station',
            'orderSn' => 'wjy-order-error'
        ];

        // Mock the getGasStationIdByAppStationIdAndPCode method to throw exception
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('invalid-station')
            ->andThrow(new Exception('Station not found', 4040001));

        // Mock responseFormatForWjy function for error case
        $expectedResponse = new JsonResponse([
            'code' => 5000999,
            'msg' => 'Station not found'
        ]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid card data
     */
    public function testHandleWithInvalidCardData()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY004'
            ],
            'oilsStationId' => 'wjy-app-station-error',
            'orderSn' => 'wjy-order-card-error',
            'foreign_driver_id' => 'invalid-driver-id'
        ];

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('wjy-app-station-error')
            ->andReturn('gas-station-error');

        // Mock Redis to return null
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('invalid-driver-id')
            ->andReturn(null);

        $this->mockApp($mockRedis);

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['data']) && isset($arg['msg']) &&
                       $arg['msg'] === '二维码解析失败';
            }));

        // Mock responseFormatForWjy function for error case
        $expectedResponse = new JsonResponse([
            'code' => 5000001,
            'msg' => 'Card data parsing failed'
        ]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY005'
            ],
            'oilsStationId' => 'wjy-app-station-foss-error',
            'orderSn' => 'wjy-order-foss-error',
            'foreign_driver_id' => 'wjy-driver-foss-error',
            'oilName' => '92#汽油'
        ];

        $cardData = [
            'card_no' => '1111111111',
            'order_no' => 'wjy-foss-error-order',
            'is_self' => true
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('wjy-driver-foss-error')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')
            ->with('wjy-app-station-foss-error')
            ->andReturn('gas-station-foss-error');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();

        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn($mockOrderAssocModel);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('wjy-order-foss-error', [
                'reason' => 'FOSS order failed',
            ], false, Mockery::any())
            ->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order failed', 5000010));

        // Mock responseFormatForWjy function for error case
        $expectedResponse = new JsonResponse([
            'code' => 5000999,
            'msg' => 'FOSS order failed'
        ]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test data transformation for WJY specific fields
     */
    public function testDataTransformationForWjySpecificFields()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY006'
            ],
            'oilsStationId' => 'wjy-app-station-transform',
            'orderSn' => 'wjy-order-transform',
            'foreign_driver_id' => 'wjy-driver-transform',
            'oilNum' => 40.0,
            'amountPrice' => 280.0,
            'oilName' => '柴油',
            'oilType' => '0#',
            'oilLevel' => '国V'
        ];

        $cardData = [
            'card_no' => '2222222222',
            'order_no' => 'wjy-transform-order',
            'is_self' => true,
            'name_abbreviation' => 'wjy',
            'secret' => 'transform-secret'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-wjy-transform'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfigWithMappings();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')->andReturn('gas-station-transform');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')->andReturn($mockOrderAssocModel);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->andReturn(true);

        // Mock FOSS_ORDERRequest and verify data transformation
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) use ($data) {
                return $arg['oil_name'] === 'diesel_id' &&
                       $arg['oil_type'] === 'type_0' &&
                       $arg['oil_level'] === 'level_5' &&
                       $arg['oil_num'] === 40.0 &&
                       $arg['oil_price'] === 280.0 &&
                       $arg['oil_money'] === 280.0 &&
                       $arg['pay_money'] === 280.0 &&
                       $arg['third_order_id'] === 'wjy-order-transform';
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['0#' => 'type_0'],
            'oil.oil_level' => ['国V' => 'level_5']
        ];
    }

    /**
     * Test ToBePaid logic exception handling for non-self cards
     */
    public function testToBePaidLogicExceptionHandlingForNonSelfCards()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'WJY007'
            ],
            'oilsStationId' => 'wjy-app-station-push-error',
            'orderSn' => 'wjy-order-push-error',
            'foreign_driver_id' => 'wjy-driver-push-error',
            'amountPrice' => 200.0
        ];

        $cardData = [
            'card_no' => '3333333333',
            'order_no' => 'wjy-push-error-order',
            'is_self' => false, // non-self card
            'name_abbreviation' => 'wjy',
            'secret' => 'push-error-secret'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-wjy-push-error'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock the getGasStationIdByAppStationIdAndPCode method
        $mockWJY = Mockery::mock(WJY::class)->makePartial();
        $mockWJY->shouldReceive('getGasStationIdByAppStationIdAndPCode')->andReturn('gas-station-push-error');

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->times(2);
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocModel->self_order_id = '';

        $mockOrderAssocData->shouldReceive('insert')->andReturn($mockOrderAssocModel);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')->andReturn($orderData);

        // Mock ToBePaid logic to throw exception
        $mockToBePaidLogic = Mockery::mock('overload:' . ToBePaid::class);
        $mockToBePaidLogic->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Push failed', 5000011));

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['exception']);
            }));

        // Mock responseFormatForWjy function for error case
        $expectedResponse = new JsonResponse([
            'code' => 5000001,
            'msg' => 'Push failed'
        ]);
        $this->mockFunction('responseFormatForWjy', $expectedResponse);

        $reflection = new ReflectionClass($mockWJY);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $property->setValue($mockWJY, $data);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($mockWJY, 'wjy');

        $result = $mockWJY->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
