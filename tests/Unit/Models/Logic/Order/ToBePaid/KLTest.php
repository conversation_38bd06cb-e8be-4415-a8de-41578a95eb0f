<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\KL;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\FOSS_USER as FOSS_USERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\KL
 */
class KLTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid token and successful order creation
     */
    public function testHandleWithValidTokenAndSuccessfulOrder()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'valid-token-123',
            'order_id' => 'kl-order-123',
            'money' => 500.00
        ];

        $userInfo = [
            'code' => 0,
            'data' => [
                'mobile' => '13800138000',
                'name' => 'Test User'
            ]
        ];

        $orderData = [
            'code' => 0,
            'data' => [
                'order_id' => 'foss-order-123'
            ],
            'msg' => 'Order created successfully'
        ];

        // Mock FOSS_USERRequest
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')
            ->once()
            ->with('api/wxApp/user/userInfoByToken', [
                'X-G7-Api-Token:valid-token-123',
                'X-G7-Api-Client:adapter',
            ])
            ->andReturn($userInfo);

        // Mock AuthConfigData
        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')
            ->once()
            ->with('KL_STATION_ID')
            ->andReturn('kl-station-001');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->once();
        $mockDB->shouldReceive('rollBack')->never();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();
        $mockOrderAssocModel->self_order_id = '';
        $mockOrderAssocModel->reason = '';

        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, '', 'kl-order-123', 'kl', '', '', json_encode($data))
            ->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === config('oil.oil_type.润滑油类') &&
                       $arg['oil_num'] === '500.00' &&
                       $arg['oil_price'] === 1 &&
                       $arg['oil_money'] === 500.00 &&
                       $arg['pay_money'] === 500.00 &&
                       $arg['third_order_id'] === 'kl-order-123' &&
                       $arg['driver_phone'] === '13800138000' &&
                       $arg['station_id'] === 'kl-station-001' &&
                       $arg['trade_mode'] === 10 &&
                       $arg['driver_source'] === 2;
            }))
            ->andReturn($orderData);

        // Mock config function
        $this->mockConfig();

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-order-123']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new KL($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.润滑油类' => 'lubricant_oil'
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_USER exception
     */
    public function testHandleWithFossUserException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'invalid-token',
            'order_id' => 'kl-order-error',
            'money' => 300.00
        ];

        // Mock FOSS_USERRequest to throw exception
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Invalid token', 4010001));

        $logic = new KL($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid token');
        $this->expectExceptionCode(4010001);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception and rollback
     */
    public function testHandleWithFossOrderExceptionAndRollback()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'valid-token-456',
            'order_id' => 'kl-order-456',
            'money' => 200.00
        ];

        $userInfo = [
            'code' => 0,
            'data' => [
                'mobile' => '13900139000',
                'name' => 'Test User 2'
            ]
        ];

        // Mock FOSS_USERRequest
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')
            ->once()
            ->andReturn($userInfo);

        // Mock AuthConfigData
        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')
            ->once()
            ->andReturn('kl-station-002');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->never();
        $mockDB->shouldReceive('rollBack')->once();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest to throw exception
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Order creation failed', 5000004));

        $this->mockConfig();

        $logic = new KL($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Order creation failed');
        $this->expectExceptionCode(5000004);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with OrderAssoc exception and rollback
     */
    public function testHandleWithOrderAssocExceptionAndRollback()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'valid-token-789',
            'order_id' => 'kl-order-789',
            'money' => 150.00
        ];

        $userInfo = [
            'code' => 0,
            'data' => [
                'mobile' => '13700137000'
            ]
        ];

        // Mock FOSS_USERRequest
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')
            ->once()
            ->andReturn($userInfo);

        // Mock AuthConfigData
        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')
            ->once()
            ->andReturn('kl-station-003');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->never();
        $mockDB->shouldReceive('rollBack')->once();

        // Mock OrderAssocData to throw exception
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->andThrow(new Exception('Database error', 5000005));

        $this->mockConfig();

        $logic = new KL($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');
        $this->expectExceptionCode(5000005);

        $logic->handle();
    }

    /**
     * Test money calculation with bcdiv
     */
    public function testMoneyCalculationWithBcdiv()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'calc-token',
            'order_id' => 'kl-calc-order',
            'money' => 123.45
        ];

        $userInfo = [
            'code' => 0,
            'data' => ['mobile' => '13600136000']
        ];

        // Mock FOSS_USERRequest
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')->once()->andReturn($userInfo);

        // Mock AuthConfigData
        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')->once()->andReturn('kl-station-calc');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->once();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();
        $mockOrderAssocModel->self_order_id = '';
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest and verify oil_num calculation
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                // Verify that oil_num is calculated as money/1 with 2 decimal places
                return $arg['oil_num'] === '123.45';
            }))
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-calc-order'],
                'msg' => 'success'
            ]);

        $this->mockConfig();
        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new KL($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test order data structure
     */
    public function testOrderDataStructure()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'structure-token',
            'order_id' => 'kl-structure-order',
            'money' => 999.99
        ];

        $userInfo = [
            'code' => 0,
            'data' => ['mobile' => '13500135000']
        ];

        // Mock FOSS_USERRequest
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')->once()->andReturn($userInfo);

        // Mock AuthConfigData
        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')->once()->andReturn('kl-station-structure');

        // Mock DB facade
        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->once();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();
        $mockOrderAssocModel->self_order_id = '';
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn($mockOrderAssocModel);

        // Mock FOSS_ORDERRequest and verify complete order data structure
        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                $requiredFields = [
                    'oil_name', 'oil_level', 'oil_type', 'oil_num', 'oil_price',
                    'oil_money', 'pay_money', 'third_order_id', 'driver_phone',
                    'station_id', 'trade_mode', 'driver_source'
                ];

                foreach ($requiredFields as $field) {
                    if (!array_key_exists($field, $arg)) {
                        return false;
                    }
                }

                return $arg['oil_level'] === '' &&
                       $arg['oil_type'] === '' &&
                       $arg['oil_price'] === 1 &&
                       $arg['trade_mode'] === 10 &&
                       $arg['driver_source'] === 2;
            }))
            ->andReturn([
                'code' => 0,
                'data' => ['order_id' => 'foss-structure-order'],
                'msg' => 'success'
            ]);

        $this->mockConfig();
        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new KL($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test response format with trade_id
     */
    public function testResponseFormatWithTradeId()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'response-token',
            'order_id' => 'kl-response-order',
            'money' => 88.88
        ];

        $userInfo = [
            'code' => 0,
            'data' => ['mobile' => '13400134000']
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'final-trade-id-123'],
            'msg' => 'Order created'
        ];

        // Mock all dependencies
        $mockUserRequest = Mockery::mock('alias:' . FOSS_USERRequest::class);
        $mockUserRequest->shouldReceive('handle')->once()->andReturn($userInfo);

        $mockAuthConfig = Mockery::mock('alias:' . AuthConfigData::class);
        $mockAuthConfig->shouldReceive('getAuthConfigValByName')->once()->andReturn('kl-station-response');

        $mockDB = Mockery::mock('alias:' . DB::class);
        $mockDB->shouldReceive('beginTransaction')->once();
        $mockDB->shouldReceive('commit')->once();

        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocModel = Mockery::mock();
        $mockOrderAssocModel->shouldReceive('save')->once();
        $mockOrderAssocModel->self_order_id = '';
        $mockOrderAssocModel->reason = '';
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn($mockOrderAssocModel);

        $mockOrderRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockOrderRequest->shouldReceive('handle')->once()->andReturn($orderData);

        $this->mockConfig();

        // Mock responseFormat and verify it's called with correct parameters
        $this->mockFunction('responseFormat', function ($code, $data) {
            if ($code === 0 && isset($data['trade_id']) && $data['trade_id'] === 'final-trade-id-123') {
                return new JsonResponse(['code' => 0, 'data' => $data]);
            }
            return new JsonResponse(['code' => $code, 'data' => $data]);
        });

        $logic = new KL($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
