<?php

namespace Unit\Models\Logic\Order\ToBePaid;

use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ToBePaid\XY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid\XY
 */
class XYTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid card data and self card
     */
    public function testHandleWithValidCardDataAndSelfCard()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY001'
            ],
            'card_no' => 'xy-card-123',
            'station_id' => 'xy-station-001',
            'order_id' => 'xy-order-123',
            'oil_num' => 50.0,
            'price' => 6.8,
            'money' => 340.0,
            'oil_name' => '92#汽油',
            'oil_type' => '92#',
            'oil_level' => '国VI'
        ];

        $cardData = [
            'card_no' => '1234567890',
            'order_no' => 'xy-card-order-123',
            'is_self' => true,
            'qr_code_source' => 'mobile',
            'driver_phone' => '13800138000',
            'driver_name' => 'XY Driver',
            'truck_no' => '京A12345'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-xy-order-123'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-123')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')
            ->once()
            ->with(2, 2, '', 'xy-order-123', Mockery::any(), '', '')
            ->andReturn(true);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('xy-order-123', [
                'self_order_id' => 'foss-xy-order-123',
                'reason' => 'success',
            ], false)
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['card_no'] === '1234567890' &&
                       $arg['app_station_id'] === 'xy-station-001' &&
                       $arg['oil_num'] === 50.0 &&
                       $arg['oil_price'] === 6.8 &&
                       $arg['oil_money'] === 340.0 &&
                       $arg['pay_money'] === 340.0 &&
                       $arg['oil_name'] === 'gasoline_92' &&
                       $arg['oil_type'] === 'type_92' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['third_order_id'] === 'xy-order-123' &&
                       $arg['order_no'] === 'xy-card-order-123' &&
                       $arg['pcode'] === 'XY001' &&
                       $arg['driver_source'] === 1 && // self card
                       $arg['client_type'] === 'mobile' &&
                       $arg['driver_phone'] === '13800138000' &&
                       $arg['driver_name'] === 'XY Driver' &&
                       $arg['truck_no'] === '京A12345' &&
                       $arg['trade_mode'] === 25; // self card
            }))
            ->andReturn($orderData);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'foss-xy-order-123']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock app function
     */
    private function mockApp($redisInstance)
    {
        if (!function_exists('app')) {
            eval('function app($service = null) { return $GLOBALS["mock_app"][$service] ?? $GLOBALS["mock_app"]; }');
        }
        $GLOBALS['mock_app'] = ['redis' => $redisInstance];
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.92#汽油' => 'gasoline_92',
            'oil.oil_type.95#汽油' => 'gasoline_95',
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['92#' => 'type_92', '95#' => 'type_95'],
            'oil.oil_level' => ['国VI' => 'level_6', '国V' => 'level_5']
        ];
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with non-self card
     */
    public function testHandleWithNonSelfCard()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY002'
            ],
            'card_no' => 'xy-card-456',
            'station_id' => 'xy-station-002',
            'order_id' => 'xy-order-456',
            'oil_num' => 30.0,
            'price' => 7.2,
            'money' => 216.0,
            'oil_name' => '95#汽油'
        ];

        $cardData = [
            'card_no' => '9876543210',
            'order_no' => 'xy-card-order-456',
            'is_self' => false, // non-self card
            'qr_code_source' => 'web',
            'driver_phone' => '13900139000',
            'driver_name' => 'XY Driver 2',
            'truck_no' => '沪B67890'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-xy-order-456'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-456')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['driver_source'] === 2 && // non-self card
                       $arg['trade_mode'] === 40; // non-self card
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with invalid card data
     */
    public function testHandleWithInvalidCardData()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY003'
            ],
            'card_no' => 'invalid-xy-card',
            'order_id' => 'xy-order-error'
        ];

        // Mock Redis to return null
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('invalid-xy-card')
            ->andReturn(null);

        $this->mockApp($mockRedis);

        // Mock ResponseLog
        $mockResponseLog = Mockery::mock('alias:' . ResponseLog::class);
        $mockResponseLog->shouldReceive('handle')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return isset($arg['data']) && isset($arg['msg']) &&
                       $arg['msg'] === '二维码解析失败';
            }));

        $logic = new XY($data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000001);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with invalid oil name
     */
    public function testHandleWithInvalidOilName()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY004'
            ],
            'card_no' => 'xy-card-invalid-oil',
            'order_id' => 'xy-order-invalid-oil',
            'oil_name' => 'invalid_oil_name'
        ];

        $cardData = [
            'card_no' => '1111111111',
            'order_no' => 'xy-invalid-oil-order',
            'is_self' => true
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-invalid-oil')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock responseFormat function for error case
        $expectedResponse = new JsonResponse([
            'code' => 4120014,
            'msg' => 'Invalid oil name'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with empty oil type and level
     */
    public function testHandleWithEmptyOilTypeAndLevel()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY005'
            ],
            'card_no' => 'xy-card-empty',
            'order_id' => 'xy-order-empty',
            'oil_name' => '92#汽油',
            'oil_type' => '', // empty
            'oil_level' => '' // empty
        ];

        $cardData = [
            'card_no' => '2222222222',
            'order_no' => 'xy-empty-order',
            'is_self' => true
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-xy-empty'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-empty')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify empty values are handled
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_type'] === '' && // should remain empty
                       $arg['oil_level'] === ''; // should remain empty
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with oil type and level mapping
     */
    public function testHandleWithOilTypeAndLevelMapping()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY006'
            ],
            'card_no' => 'xy-card-mapping',
            'order_id' => 'xy-order-mapping',
            'oil_name' => '柴油',
            'oil_type' => '0#',
            'oil_level' => '国V'
        ];

        $cardData = [
            'card_no' => '3333333333',
            'order_no' => 'xy-mapping-order',
            'is_self' => false
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-xy-mapping'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-mapping')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfigWithMappings();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify mappings
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                return $arg['oil_name'] === 'diesel_id' &&
                       $arg['oil_type'] === 'type_0' &&
                       $arg['oil_level'] === 'level_5';
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type.柴油' => 'diesel_id',
            'oil.oil_no_simple' => ['0#' => 'type_0'],
            'oil.oil_level' => ['国V' => 'level_5']
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY007'
            ],
            'card_no' => 'xy-card-error',
            'order_id' => 'xy-order-foss-error',
            'oil_name' => '92#汽油'
        ];

        $cardData = [
            'card_no' => '4444444444',
            'order_no' => 'xy-error-order',
            'is_self' => true
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')
            ->with('xy-card-error')
            ->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS order failed', 5000009));

        $logic = new XY($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS order failed');
        $this->expectExceptionCode(5000009);

        $logic->handle();
    }

    /**
     * Test data structure passed to FOSS_ORDER
     */
    public function testDataStructurePassedToFossOrder()
    {
        $data = [
            'auth_data' => [
                'role_code' => 'XY008'
            ],
            'card_no' => 'xy-card-structure',
            'station_id' => 'xy-station-structure',
            'order_id' => 'xy-order-structure',
            'oil_num' => 45.5,
            'price' => 6.9,
            'money' => 313.95,
            'oil_name' => '95#汽油',
            'oil_type' => '95#',
            'oil_level' => '国VI'
        ];

        $cardData = [
            'card_no' => '5555555555',
            'order_no' => 'xy-structure-order',
            'is_self' => true,
            'qr_code_source' => 'app',
            'driver_phone' => '13700137000',
            'driver_name' => 'Structure Driver',
            'truck_no' => '粤C11111'
        ];

        $orderData = [
            'code' => 0,
            'data' => ['order_id' => 'foss-xy-structure'],
            'msg' => 'success'
        ];

        // Mock Redis
        $mockRedis = Mockery::mock();
        $mockRedis->shouldReceive('get')->andReturn(json_encode($cardData));

        $this->mockApp($mockRedis);
        $this->mockConfig();

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('insert')->once()->andReturn(true);
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')->once()->andReturn(true);

        // Mock FOSS_ORDERRequest and verify complete data structure
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/makeOrder', Mockery::on(function ($arg) {
                $requiredFields = [
                    'card_no', 'app_station_id', 'oil_num', 'oil_price', 'oil_money',
                    'pay_money', 'oil_type', 'oil_name', 'oil_level', 'third_order_id',
                    'order_no', 'pcode', 'driver_source', 'client_type', 'driver_phone',
                    'driver_name', 'truck_no', 'trade_mode'
                ];

                foreach ($requiredFields as $field) {
                    if (!array_key_exists($field, $arg)) {
                        return false;
                    }
                }

                return $arg['card_no'] === '5555555555' &&
                       $arg['app_station_id'] === 'xy-station-structure' &&
                       $arg['oil_num'] === 45.5 &&
                       $arg['oil_price'] === 6.9 &&
                       $arg['oil_money'] === 313.95 &&
                       $arg['pay_money'] === 313.95 &&
                       $arg['oil_name'] === 'gasoline_95' &&
                       $arg['oil_type'] === 'type_95' &&
                       $arg['oil_level'] === 'level_6' &&
                       $arg['third_order_id'] === 'xy-order-structure' &&
                       $arg['order_no'] === 'xy-structure-order' &&
                       $arg['pcode'] === 'XY008' &&
                       $arg['driver_source'] === 1 &&
                       $arg['client_type'] === 'app' &&
                       $arg['driver_phone'] === '13700137000' &&
                       $arg['driver_name'] === 'Structure Driver' &&
                       $arg['truck_no'] === '粤C11111' &&
                       $arg['trade_mode'] === 25;
            }))
            ->andReturn($orderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new XY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
