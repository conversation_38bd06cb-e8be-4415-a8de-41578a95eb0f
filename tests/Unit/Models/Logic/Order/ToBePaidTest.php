<?php

namespace Unit\Models\Logic\Order;

use App\Models\Logic\Order\ToBePaid;
use App\Models\Logic\Order\ToBePaid\Downstream\Main as DownstreamMain;
use App\Models\Logic\Order\ToBePaid\JD;
use App\Models\Logic\Order\ToBePaid\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ToBePaid
 */
class ToBePaidTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::__construct
     * Test constructor with valid platform
     */
    public function testConstructorWithValidPlatform()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'jd',
                'role' => 1
            ]
        ];

        $mockWorker = Mockery::mock(JD::class);
        Mockery::mock('overload:' . JD::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $this->assertInstanceOf(ToBePaid::class, $logic);
    }

    /**
     * @covers ::__construct
     * Test constructor with downstream customer role
     */
    public function testConstructorWithDownstreamCustomer()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'custom',
                'real_name_abbreviation' => 'ds',
                'role' => 6
            ]
        ];

        $mockWorker = Mockery::mock(DownstreamMain::class);
        Mockery::mock('overload:' . DownstreamMain::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $this->assertInstanceOf(ToBePaid::class, $logic);
    }

    /**
     * @covers ::__construct
     * Test constructor with invalid platform throws exception
     */
    public function testConstructorWithInvalidPlatformThrowsException()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'invalid_platform',
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ToBePaid($parameter);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy',
                'role' => 1
            ]
        ];

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => ['order_id' => 'test-order']]);
        
        $mockWorker = Mockery::mock(ZY::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);
        
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * Test worker mapping contains expected platforms
     */
    public function testWorkerMappingContainsExpectedPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'jd',
                'role' => 1
            ]
        ];

        $mockWorker = Mockery::mock(JD::class);
        Mockery::mock('overload:' . JD::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('workerMapping');
        $property->setAccessible(true);
        $mapping = $property->getValue($logic);

        $expectedPlatforms = ['jd', 'zy', 'dd', 'sp', 'xy', 'kl', 'downstream', 'wjy', 'sffy'];
        
        foreach ($expectedPlatforms as $platform) {
            $this->assertArrayHasKey($platform, $mapping);
        }
    }

    /**
     * Test constructor sets correct worker for each platform
     */
    public function testConstructorSetsCorrectWorkerForEachPlatform()
    {
        $platforms = [
            'jd' => JD::class,
            'zy' => ZY::class,
        ];

        foreach ($platforms as $platform => $expectedClass) {
            $parameter = [
                'auth_data' => [
                    'name_abbreviation' => $platform,
                    'role' => 1
                ]
            ];

            $mockWorker = Mockery::mock($expectedClass);
            Mockery::mock('overload:' . $expectedClass, $mockWorker);

            $logic = new ToBePaid($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf($expectedClass, $worker);
        }
    }

    /**
     * Test downstream customer logic with unknown platform
     */
    public function testDownstreamCustomerLogicWithUnknownPlatform()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'unknown_platform',
                'real_name_abbreviation' => 'ds',
                'role' => 6
            ]
        ];

        $mockWorker = Mockery::mock(DownstreamMain::class);
        Mockery::mock('overload:' . DownstreamMain::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $this->assertInstanceOf(ToBePaid::class, $logic);
    }

    /**
     * Test handle method with exception from worker
     */
    public function testHandleWithExceptionFromWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'jd',
                'role' => 1
            ]
        ];

        $mockWorker = Mockery::mock(JD::class);
        $mockWorker->shouldReceive('handle')->once()->andThrow(new Exception('Worker error', 5000001));
        
        Mockery::mock('overload:' . JD::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Worker error');
        $this->expectExceptionCode(5000001);
        
        $logic->handle();
    }

    /**
     * Test constructor with empty auth data
     */
    public function testConstructorWithEmptyAuthData()
    {
        $parameter = [
            'auth_data' => []
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ToBePaid($parameter);
    }

    /**
     * Test constructor with missing name_abbreviation
     */
    public function testConstructorWithMissingNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ToBePaid($parameter);
    }

    /**
     * Test constructor with null name_abbreviation
     */
    public function testConstructorWithNullNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => null,
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ToBePaid($parameter);
    }

    /**
     * Test constructor with empty string name_abbreviation
     */
    public function testConstructorWithEmptyStringNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => '',
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ToBePaid($parameter);
    }

    /**
     * Test downstream customer with missing real_name_abbreviation
     */
    public function testDownstreamCustomerWithMissingRealNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'unknown',
                'role' => 6
            ]
        ];

        $mockWorker = Mockery::mock(DownstreamMain::class);
        Mockery::mock('overload:' . DownstreamMain::class, $mockWorker);

        $logic = new ToBePaid($parameter);
        $this->assertInstanceOf(ToBePaid::class, $logic);
    }

    /**
     * Test that data is properly passed to worker
     */
    public function testDataIsProperlyPassedToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'jd',
                'role' => 1
            ],
            'station_id' => 'test-station',
            'order_id' => 'test-order'
        ];

        $mockWorker = Mockery::mock(JD::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn(new JsonResponse(['code' => 0]));
        
        // Verify that the worker is constructed with the correct data
        Mockery::mock('overload:' . JD::class)
            ->shouldReceive('__construct')
            ->once()
            ->with($parameter)
            ->andReturn($mockWorker);

        $logic = new ToBePaid($parameter);
        $logic->handle();
    }
}
