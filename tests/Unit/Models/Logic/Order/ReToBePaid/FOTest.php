<?php

namespace Tests\Unit\Models\Logic\Order\ReToBePaid;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\DockingPlatformInfo;
use App\Models\Logic\Order\ReToBePaid\FO;
use App\Models\Logic\Order\ReToBePaid\ThirdParty;
use Exception;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use stdClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ReToBePaid\FO
 */
class FOTest extends TestCase
{
    /**
     * @covers ::handle
     * Test handle method with valid data
     */
    public function testHandleWithValidData()
    {
        $data = [
            'orgcode' => 'FO001',
            'station_id' => 'station-123',
            'oil_name' => 'gasoline_92',
            'oil_type' => 'type_92',
            'oil_level' => 'level_6'
        ];

        $realAuthInfo = [
            'role_code' => 'FO001',
            'name_abbreviation' => 'fo'
        ];

        $stationData = [
            'stationInfo' => [
                'pcode' => 'STATION001',
                'station_name' => 'Test Station'
            ]
        ];

        $authInfo = [
            'role_code' => 'STATION001',
            'name_abbreviation' => 'station'
        ];

        $reToBePaidResult = new stdClass();
        $reToBePaidResult->self_order_id = 'foss-fo-retry-123';
        $reToBePaidResult->platform_order_id = 'fo-platform-456';

        // Mock config function
        $this->mockConfig();

        // Mock DockingPlatformInfo
        $mockDockingPlatformInfo = Mockery::mock('alias:' . DockingPlatformInfo::class);
        $mockDockingPlatformInfo->shouldReceive('getPlatformInfoByName')
            ->with('fo')
            ->andReturn([
                'platform_name' => 'fo',
                'platform_id' => 1
            ]);
        $mockDockingPlatformInfo->shouldReceive('getFieldsByNameAbbreviation')
            ->with('fo', 'platform_name', '')
            ->andReturn([
                'platform_name' => 'fo'
            ]);

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('FO001', true)
            ->andReturn($realAuthInfo);

        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('STATION001', true)
            ->andReturn($authInfo);

        // Mock for ReToBePaidLogic internal call
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with(['FO001'])
            ->andReturn([$realAuthInfo]);

        // Mock FOSS_STATIONRequest
        $mockFossStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockFossStationRequest->shouldReceive('handle')
            ->once()
            ->with('v1/station/getStationById', [
                'id' => 'station-123',
            ])
            ->andReturn(['data' => $stationData]);

        // Create FO instance
        $logic = new FO($data);

        // Should throw exception because FO platform is not supported in ReToBePaid
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('非法请求');

        $logic->handle();
    }

    /**
     * Helper method to mock config function
     */
    private function mockConfig()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type' => [
                'gasoline_92' => '92#汽油',
                'gasoline_95' => '95#汽油',
                'diesel_id' => '柴油'
            ],
            'oil.oil_no_simple' => [
                'type_92' => '92',
                'type_95' => '95',
                'type_0' => '0'
            ],
            'oil.oil_level' => [
                'level_6' => '国VI',
                'level_5' => '国V'
            ]
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with empty real auth info
     */
    public function testHandleWithEmptyRealAuthInfo()
    {
        $data = [
            'orgcode' => 'INVALID_ORG',
            'station_id' => 'station-456'
        ];

        // Mock AuthInfoData to return empty result
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('INVALID_ORG', true)
            ->andReturn([]);

        $logic = new FO($data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000071);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with empty station data
     */
    public function testHandleWithEmptyStationData()
    {
        $data = [
            'orgcode' => 'FO002',
            'station_id' => 'invalid-station'
        ];

        $realAuthInfo = [
            'role_code' => 'FO002',
            'name_abbreviation' => 'fo'
        ];

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('FO002', true)
            ->andReturn($realAuthInfo);

        // Mock FOSS_STATIONRequest to return empty data
        $mockFossStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockFossStationRequest->shouldReceive('handle')
            ->once()
            ->with('v1/station/getStationById', [
                'id' => 'invalid-station',
            ])
            ->andReturn(['data' => []]);

        $logic = new FO($data);

        $this->expectException(Exception::class);
        $this->expectExceptionCode(5000027);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_STATION exception
     */
    public function testHandleWithFossStationException()
    {
        $data = [
            'orgcode' => 'FO003',
            'station_id' => 'station-error'
        ];

        $realAuthInfo = [
            'role_code' => 'FO003',
            'name_abbreviation' => 'fo'
        ];

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('FO003', true)
            ->andReturn($realAuthInfo);

        // Mock FOSS_STATIONRequest to throw exception
        $mockFossStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockFossStationRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Station service unavailable', 5000024));

        $logic = new FO($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Station service unavailable');
        $this->expectExceptionCode(5000024);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with oil configuration mapping - should fail with unsupported platform
     */
    public function testHandleWithOilConfigurationMapping()
    {
        $data = [
            'orgcode' => 'FO004',
            'station_id' => 'station-config',
            'oil_name' => 'diesel_id',
            'oil_type' => 'type_0',
            'oil_level' => 'level_5'
        ];

        $realAuthInfo = [
            'role_code' => 'FO004',
            'name_abbreviation' => 'fo'
        ];

        $stationData = [
            'stationInfo' => [
                'pcode' => 'STATION004'
            ]
        ];

        $authInfo = [
            'role_code' => 'STATION004',
            'name_abbreviation' => 'station'
        ];

        // Mock config with specific mappings
        $this->mockConfigWithMappings();

        // Mock DockingPlatformInfo
        $mockDockingPlatformInfo = Mockery::mock('alias:' . DockingPlatformInfo::class);
        $mockDockingPlatformInfo->shouldReceive('getPlatformInfoByName')
            ->with('fo')
            ->andReturn([
                'platform_name' => 'fo',
                'platform_id' => 1
            ]);
        $mockDockingPlatformInfo->shouldReceive('getFieldsByNameAbbreviation')
            ->with('fo', 'platform_name', '')
            ->andReturn([
                'platform_name' => 'fo'
            ]);

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('FO004', true)
            ->andReturn($realAuthInfo);

        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('STATION004', true)
            ->andReturn($authInfo);

        // Mock for ReToBePaidLogic internal call
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with(['FO004'])
            ->andReturn([$realAuthInfo]);

        // Mock FOSS_STATIONRequest
        $mockFossStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockFossStationRequest->shouldReceive('handle')
            ->once()
            ->andReturn(['data' => $stationData]);

        $logic = new FO($data);

        // Should throw exception because FO platform is not supported in ReToBePaid
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('非法请求');

        $logic->handle();
    }

    /**
     * Helper method to mock config function with specific mappings
     */
    private function mockConfigWithMappings()
    {
        if (!function_exists('config')) {
            eval('function config($key) { return $GLOBALS["mock_config"][$key] ?? ""; }');
        }
        $GLOBALS['mock_config'] = [
            'oil.oil_type' => [
                'diesel_id' => '柴油'
            ],
            'oil.oil_no_simple' => [
                'type_0' => '0'
            ],
            'oil.oil_level' => [
                'level_5' => '国V'
            ]
        ];
    }

    /**
     * @covers ::handle
     * Test handle method with missing oil configuration - should fail with unsupported platform
     */
    public function testHandleWithMissingOilConfiguration()
    {
        $data = [
            'orgcode' => 'FO005',
            'station_id' => 'station-missing-config',
            'oil_name' => 'unknown_oil',
            'oil_type' => 'unknown_type',
            'oil_level' => 'unknown_level'
        ];

        $realAuthInfo = [
            'role_code' => 'FO005',
            'name_abbreviation' => 'fo'
        ];

        $stationData = [
            'stationInfo' => [
                'pcode' => 'STATION005'
            ]
        ];

        $authInfo = [
            'role_code' => 'STATION005',
            'name_abbreviation' => 'station'
        ];

        $this->mockConfig();

        // Mock DockingPlatformInfo
        $mockDockingPlatformInfo = Mockery::mock('alias:' . DockingPlatformInfo::class);
        $mockDockingPlatformInfo->shouldReceive('getPlatformInfoByName')
            ->with('fo')
            ->andReturn([
                'platform_name' => 'fo',
                'platform_id' => 1
            ]);
        $mockDockingPlatformInfo->shouldReceive('getFieldsByNameAbbreviation')
            ->with('fo', 'platform_name', '')
            ->andReturn([
                'platform_name' => 'fo'
            ]);

        // Mock AuthInfoData
        $mockAuthInfoData = Mockery::mock('alias:' . AuthInfoData::class);
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('FO005', true)
            ->andReturn($realAuthInfo);

        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with('STATION005', true)
            ->andReturn($authInfo);

        // Mock for ReToBePaidLogic internal call
        $mockAuthInfoData->shouldReceive('getAuthInfoByRoleCode')
            ->with(['FO005'])
            ->andReturn([$realAuthInfo]);

        // Mock FOSS_STATIONRequest
        $mockFossStationRequest = Mockery::mock('alias:' . FOSS_STATIONRequest::class);
        $mockFossStationRequest->shouldReceive('handle')
            ->once()
            ->andReturn(['data' => $stationData]);

        $logic = new FO($data);

        // Should throw exception because FO platform is not supported in ReToBePaid
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('非法请求');

        $logic->handle();
    }

    /**
     * Test inheritance from ThirdParty class
     */
    public function testInheritanceFromThirdParty()
    {
        $logic = new FO([]);
        $this->assertInstanceOf(ThirdParty::class, $logic);
    }

    /**
     * Test that data property exists and is accessible
     */
    public function testDataPropertyExists()
    {
        $testData = ['orgcode' => 'test'];
        $logic = new FO($testData);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('data'));

        $property = $reflection->getProperty('data');
        $property->setAccessible(true);

        // Test getting property
        $this->assertEquals($testData, $property->getValue($logic));
    }

    /**
     * Test handle method return type annotation
     */
    public function testHandleMethodReturnTypeAnnotation()
    {
        $reflection = new ReflectionClass(FO::class);
        $method = $reflection->getMethod('handle');

        $this->assertTrue($method->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $method->getReturnType()->getName());
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }
}
