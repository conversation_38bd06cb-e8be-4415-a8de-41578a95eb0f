<?php

namespace Unit\Models\Logic\Order\ReToBePaid;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\ReToBePaid\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ReToBePaid\ZY
 */
class ZYTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with valid order data
     */
    public function testHandleWithValidOrderData()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-retry-order-123'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-retry-123',
            'platform_order_id' => 'zy-retry-order-123',
            'platform_name' => 'zy',
            'self_order_status' => 2, // failed status
            'platform_order_status' => 2
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'internal-zy-retry-123'],
            'msg' => 'Retry payment successful'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_order_id',
                    'operator' => '=',
                    'value' => 'zy-retry-order-123',
                ],
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'zy',
                ],
            ])
            ->andReturn($orderAssocData);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('zy-retry-order-123', [
                'self_order_status' => 1,
                'platform_order_status' => 1,
                'reason' => 'Retry payment successful',
            ], false, 'zy')
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/retryPayment', [
                'order_id' => 'internal-zy-retry-123'
            ])
            ->andReturn($fossOrderResponse);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['trade_id' => 'internal-zy-retry-123'],
            'msg' => 'Retry payment successful'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with order not found
     */
    public function testHandleWithOrderNotFound()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'non-existent-zy-order'
        ];

        // Mock OrderAssocData to return empty result
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn([]);

        // Mock responseFormat function for error case
        $expectedResponse = new JsonResponse([
            'code' => 4040001,
            'msg' => 'Order not found'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with order already paid
     */
    public function testHandleWithOrderAlreadyPaid()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-paid-order-456'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-paid-456',
            'platform_order_id' => 'zy-paid-order-456',
            'platform_name' => 'zy',
            'self_order_status' => 1, // already paid
            'platform_order_status' => 1
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock responseFormat function for already paid case
        $expectedResponse = new JsonResponse([
            'code' => 4090001,
            'msg' => 'Order already paid'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-retry-error-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-retry-error',
            'platform_order_id' => 'zy-retry-error-order',
            'platform_name' => 'zy',
            'self_order_status' => 2, // failed status
            'platform_order_status' => 2
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('zy-retry-error-order', [
                'reason' => 'Retry payment failed',
            ], false, 'zy')
            ->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Retry payment failed', 5000016));

        $logic = new ZY($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Retry payment failed');
        $this->expectExceptionCode(5000016);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with order in cancelled status
     */
    public function testHandleWithOrderInCancelledStatus()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-cancelled-order-789'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-cancelled-789',
            'platform_order_id' => 'zy-cancelled-order-789',
            'platform_name' => 'zy',
            'self_order_status' => 4, // cancelled status
            'platform_order_status' => 4
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock responseFormat function for cancelled order case
        $expectedResponse = new JsonResponse([
            'code' => 4090002,
            'msg' => 'Order has been cancelled'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with order in refunded status
     */
    public function testHandleWithOrderInRefundedStatus()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-refunded-order-101'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-refunded-101',
            'platform_order_id' => 'zy-refunded-order-101',
            'platform_name' => 'zy',
            'self_order_status' => 3, // refunded status
            'platform_order_status' => 3
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock responseFormat function for refunded order case
        $expectedResponse = new JsonResponse([
            'code' => 4090003,
            'msg' => 'Order has been refunded'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test order status validation logic
     */
    public function testOrderStatusValidationLogic()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-status-validation-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-status-validation',
            'platform_order_id' => 'zy-status-validation-order',
            'platform_name' => 'zy',
            'self_order_status' => 2, // failed status - valid for retry
            'platform_order_status' => 2
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'internal-zy-status-validation'],
            'msg' => 'Retry successful'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('zy-status-validation-order', [
                'self_order_status' => 1,
                'platform_order_status' => 1,
                'reason' => 'Retry successful',
            ], false, 'zy')
            ->andReturn(true);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andReturn($fossOrderResponse);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test retry payment with additional parameters
     */
    public function testRetryPaymentWithAdditionalParameters()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-retry-params-order',
            'retry_reason' => 'Network timeout',
            'force_retry' => true
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-retry-params',
            'platform_order_id' => 'zy-retry-params-order',
            'platform_name' => 'zy',
            'self_order_status' => 2,
            'platform_order_status' => 2
        ];

        $fossOrderResponse = [
            'code' => 0,
            'data' => ['order_id' => 'internal-zy-retry-params'],
            'msg' => 'Force retry successful'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->andReturn(true);

        // Mock FOSS_ORDERRequest and verify additional parameters
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/retryPayment', Mockery::on(function ($params) {
                return $params['order_id'] === 'internal-zy-retry-params' &&
                       isset($params['retry_reason']) &&
                       $params['retry_reason'] === 'Network timeout' &&
                       isset($params['force_retry']) &&
                       $params['force_retry'] === true;
            }))
            ->andReturn($fossOrderResponse);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test retry payment failure with order status update
     */
    public function testRetryPaymentFailureWithOrderStatusUpdate()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-retry-failure-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-zy-retry-failure',
            'platform_order_id' => 'zy-retry-failure-order',
            'platform_name' => 'zy',
            'self_order_status' => 2,
            'platform_order_status' => 2
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Should update order with failure reason
        $mockOrderAssocData->shouldReceive('updateOrderInfoByOrderId')
            ->once()
            ->with('zy-retry-failure-order', [
                'reason' => 'Payment gateway error',
            ], false, 'zy')
            ->andReturn(true);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Payment gateway error', 5000017));

        $logic = new ZY($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Payment gateway error');
        $this->expectExceptionCode(5000017);

        $logic->handle();
    }

    /**
     * Test constructor sets data correctly
     */
    public function testConstructorSetsDataCorrectly()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-constructor-test',
            'retry_reason' => 'Test retry'
        ];

        $logic = new ZY($data);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $actualData = $property->getValue($logic);

        $this->assertEquals($data, $actualData);
    }

    /**
     * Test handle method with empty self_order_id
     */
    public function testHandleWithEmptySelfOrderId()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'order_id' => 'zy-empty-self-order'
        ];

        $orderAssocData = [
            'self_order_id' => '', // empty
            'platform_order_id' => 'zy-empty-self-order',
            'platform_name' => 'zy',
            'self_order_status' => 2,
            'platform_order_status' => 2
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock responseFormat function for invalid order data
        $expectedResponse = new JsonResponse([
            'code' => 4040002,
            'msg' => 'Invalid order data'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new ZY($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
