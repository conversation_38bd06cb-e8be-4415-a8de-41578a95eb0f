<?php

namespace Tests\Unit\Models\Logic\Order\RefundApplication;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\RefundApplication\FY;
use App\Models\Logic\Order\RefundApplication\ThirdParty;
use ErrorException;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

/**
 * @coversDefaultClass \App\Models\Logic\Order\RefundApplication\FY
 */
class FYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test handle method with valid data including orderId
     */
    public function testHandleWithValidDataIncludingOrderId()
    {
        $data = [
            'orderId' => 'foss-fy-order-123',
            'tradeId' => 'fy-trade-456',
            'refund_reason' => 'Customer request'
        ];

        $fossResponse = [
            'code' => 0,
            'data' => ['refund_id' => 'refund-123'],
            'msg' => 'Refund application successful'
        ];

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/oaRefundApplication', [
                'order_id' => 'foss-fy-order-123',
                'refund_reason' => '',
                'idempotent' => true,
            ])
            ->andReturn($fossResponse);

        // Mock responseFormatForFy function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['refund_id' => 'refund-123'],
            'msg' => 'Refund application successful'
        ]);
        $this->mockFunction('responseFormatForFy', $expectedResponse);

        // Create FY instance
        $logic = new FY($data);

        // Set name_abbreviation using reflection
        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'fy');

        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method without orderId, should query from OrderAssocData     */
    public function testHandleWithoutOrderIdShouldQueryFromOrderAssocData()
    {
        // Test that the method can handle missing orderId
        $data = [
            'tradeId' => 'fy-trade-789'
        ];

        $logic = new FY($data);
        
        // Test that data property is set correctly
        $reflection = new \ReflectionClass($logic);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        
        $this->assertEquals($data, $dataProperty->getValue($logic));
    }

    /**
     * @covers ::handle
     * Test handle method with empty tradeId
     */
    public function testHandleWithEmptyTradeId()
    {
        $data = [
            'orderId' => 'foss-fy-order-empty',
            'tradeId' => '',
            'refund_reason' => 'Empty trade ID test'
        ];

        $fossResponse = [
            'code' => 0,
            'data' => ['refund_id' => 'refund-empty'],
            'msg' => 'Refund application successful'
        ];

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/oaRefundApplication', [
                'order_id' => 'foss-fy-order-empty',
                'refund_reason' => '',
                'idempotent' => true,
            ])
            ->andReturn($fossResponse);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormatForFy', $expectedResponse);

        $logic = new FY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'fy');

        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'orderId' => 'foss-fy-order-error',
            'tradeId' => 'fy-trade-error'
        ];

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS refund application failed', 5000022));

        $logic = new FY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'fy');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS refund application failed');
        $this->expectExceptionCode(5000022);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with OrderAssocData not found     */
    public function testHandleWithOrderAssocDataNotFound()
    {
        // Test that the method exists and accepts data parameter
        $this->assertTrue(method_exists(FY::class, 'handle'));
        
        $reflection = new \ReflectionMethod(FY::class, 'handle');
        $this->assertTrue($reflection->isPublic());
    }

    /**
     * @covers ::handle
     * Test handle method with null orderId and valid tradeId     */
    public function testHandleWithNullOrderIdAndValidTradeId()
    {
        $data = [
            'orderId' => null,
            'tradeId' => 'fy-trade-null-order'
        ];

        $logic = new FY($data);
        
        // Test that null orderId is handled properly in data
        $reflection = new \ReflectionClass($logic);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        
        $actualData = $dataProperty->getValue($logic);
        $this->assertNull($actualData['orderId']);
        $this->assertEquals('fy-trade-null-order', $actualData['tradeId']);
    }

    /**
     * Test inheritance from ThirdParty class
     */
    public function testInheritanceFromThirdParty()
    {
        $logic = new FY([]);
        $this->assertInstanceOf(ThirdParty::class, $logic);
    }

    /**
     * Test that data property exists and is accessible
     */
    public function testDataPropertyExists()
    {
        $testData = ['test' => 'value'];
        $logic = new FY($testData);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('data'));

        $property = $reflection->getProperty('data');
        $property->setAccessible(true);

        // Test getting property
        $this->assertEquals($testData, $property->getValue($logic));
    }

    /**
     * Test that name_abbreviation property exists and is accessible
     */
    public function testNameAbbreviationPropertyExists()
    {
        $logic = new FY([]);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('name_abbreviation'));

        $property = $reflection->getProperty('name_abbreviation');
        $property->setAccessible(true);

        // Test setting and getting property
        $property->setValue($logic, 'fy');
        $this->assertEquals('fy', $property->getValue($logic));
    }

    /**
     * Test handle method parameter validation for FOSS request
     */
    public function testHandleMethodParameterValidationForFossRequest()
    {
        $data = [
            'orderId' => 'test-order-validation',
            'tradeId' => 'test-trade-validation'
        ];

        // Mock FOSS_ORDERRequest and verify exact parameters
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with(
                Mockery::on(function ($endpoint) {
                    return $endpoint === '/api/oil_adapter/oaRefundApplication';
                }),
                Mockery::on(function ($params) {
                    return isset($params['order_id']) && 
                           isset($params['refund_reason']) &&
                           isset($params['idempotent']) &&
                           $params['order_id'] === 'test-order-validation' &&
                           $params['refund_reason'] === '' &&
                           $params['idempotent'] === true;
                })
            )
            ->andReturn(['code' => 0, 'data' => []]);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormatForFy', $expectedResponse);

        $logic = new FY($data);

        $reflection = new ReflectionClass($logic);

        $nameProperty = $reflection->getProperty('name_abbreviation');
        $nameProperty->setAccessible(true);
        $nameProperty->setValue($logic, 'fy');

        $result = $logic->handle();

        // Verify that the method was called with correct parameters
        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
