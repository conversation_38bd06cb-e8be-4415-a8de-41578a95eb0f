<?php

namespace Unit\Models\Logic\Order\Query;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\Query\SC;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FOSS_ORDER as FOSS_ORDERRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Query\SC
 */
class SCTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::handle
     * Test handle method with order_id query
     */
    public function testHandleWithOrderIdQuery()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'order_id' => 'sc-order-123'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-sc-order-123'
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [
                'order_id' => 'internal-sc-order-123',
                'status' => 'completed',
                'amount' => 340.0,
                'oil_name' => '92#汽油',
                'oil_num' => 50.0,
                'station_name' => 'SC Test Station',
                'driver_phone' => '13800138000',
                'truck_no' => '京A12345'
            ],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_order_id',
                    'operator' => '=',
                    'value' => 'sc-order-123',
                ],
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'sc',
                ],
            ], ['self_order_id'])
            ->andReturn($orderAssocData);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', [
                'order_id' => 'internal-sc-order-123'
            ])
            ->andReturn($fossOrderData);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $fossOrderData['data']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with date range query
     */
    public function testHandleWithDateRangeQuery()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-01-01 00:00:00',
            'end_time' => '2023-01-31 23:59:59',
            'pay_status' => '1',
            'station_id' => 'sc-station-001'
        ];

        $orderAssocDataList = [
            ['self_order_id' => 'internal-sc-order-001'],
            ['self_order_id' => 'internal-sc-order-002'],
            ['self_order_id' => 'internal-sc-order-003']
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [
                [
                    'order_id' => 'internal-sc-order-001',
                    'status' => 'completed',
                    'amount' => 300.0
                ],
                [
                    'order_id' => 'internal-sc-order-002',
                    'status' => 'pending',
                    'amount' => 450.0
                ],
                [
                    'order_id' => 'internal-sc-order-003',
                    'status' => 'completed',
                    'amount' => 600.0
                ]
            ],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'platform_name',
                    'operator' => '=',
                    'value' => 'sc',
                ],
                [
                    'field' => 'created_at',
                    'operator' => '>=',
                    'value' => '2023-01-01 00:00:00',
                ],
                [
                    'field' => 'created_at',
                    'operator' => '<=',
                    'value' => '2023-01-31 23:59:59',
                ],
            ], ['self_order_id'])
            ->andReturn($orderAssocDataList);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', [
                'order_ids' => ['internal-sc-order-001', 'internal-sc-order-002', 'internal-sc-order-003'],
                'pay_status' => '1',
                'station_id' => 'sc-station-001'
            ])
            ->andReturn($fossOrderData);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $fossOrderData['data']
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with order not found
     */
    public function testHandleWithOrderNotFound()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'order_id' => 'non-existent-sc-order'
        ];

        // Mock OrderAssocData to return empty result
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn([]);

        // Mock responseFormat function for error case
        $expectedResponse = new JsonResponse([
            'code' => 4040001,
            'msg' => 'Order not found'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with FOSS_ORDER exception
     */
    public function testHandleWithFossOrderException()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'order_id' => 'sc-error-order'
        ];

        $orderAssocData = [
            'self_order_id' => 'internal-sc-error-order'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        // Mock FOSS_ORDERRequest to throw exception
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('FOSS query failed', 5000015));

        $logic = new SC($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FOSS query failed');
        $this->expectExceptionCode(5000015);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with empty date range results
     */
    public function testHandleWithEmptyDateRangeResults()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-12-01 00:00:00',
            'end_time' => '2023-12-31 23:59:59'
        ];

        // Mock OrderAssocData to return empty result
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn([]);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => []
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test query parameter building for date range with additional filters
     */
    public function testQueryParameterBuildingForDateRangeWithAdditionalFilters()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-06-01 00:00:00',
            'end_time' => '2023-06-30 23:59:59',
            'pay_status' => '2',
            'station_id' => 'sc-station-123',
            'driver_phone' => '13800138000',
            'truck_no' => '京A12345',
            'order_status' => 'completed'
        ];

        $orderAssocDataList = [
            ['self_order_id' => 'internal-sc-order-june-001']
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [
                [
                    'order_id' => 'internal-sc-order-june-001',
                    'status' => 'completed'
                ]
            ],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with(Mockery::on(function ($conditions) {
                // Verify that all conditions are properly set
                $expectedConditions = [
                    ['field' => 'platform_name', 'operator' => '=', 'value' => 'sc'],
                    ['field' => 'created_at', 'operator' => '>=', 'value' => '2023-06-01 00:00:00'],
                    ['field' => 'created_at', 'operator' => '<=', 'value' => '2023-06-30 23:59:59'],
                ];

                foreach ($expectedConditions as $expected) {
                    if (!in_array($expected, $conditions)) {
                        return false;
                    }
                }
                return true;
            }), ['self_order_id'])
            ->andReturn($orderAssocDataList);

        // Mock FOSS_ORDERRequest
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', Mockery::on(function ($params) {
                return isset($params['order_ids']) &&
                       $params['order_ids'] === ['internal-sc-order-june-001'] &&
                       isset($params['pay_status']) &&
                       $params['pay_status'] === '2' &&
                       isset($params['station_id']) &&
                       $params['station_id'] === 'sc-station-123' &&
                       isset($params['driver_phone']) &&
                       $params['driver_phone'] === '13800138000' &&
                       isset($params['truck_no']) &&
                       $params['truck_no'] === '京A12345' &&
                       isset($params['order_status']) &&
                       $params['order_status'] === 'completed';
            }))
            ->andReturn($fossOrderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handle method with null self_order_id
     */
    public function testHandleWithNullSelfOrderId()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'order_id' => 'sc-null-order'
        ];

        $orderAssocData = [
            'self_order_id' => null
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocData);

        $expectedResponse = new JsonResponse([
            'code' => 4040001,
            'msg' => 'Invalid order data'
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handle method with multiple order IDs extraction and filtering
     */
    public function testHandleWithMultipleOrderIdsExtractionAndFiltering()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-03-01 00:00:00',
            'end_time' => '2023-03-31 23:59:59'
        ];

        $orderAssocDataList = [
            ['self_order_id' => 'sc-order-001'],
            ['self_order_id' => 'sc-order-002'],
            ['self_order_id' => null], // should be filtered out
            ['self_order_id' => ''],    // should be filtered out
            ['self_order_id' => 'sc-order-003']
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [
                ['order_id' => 'sc-order-001'],
                ['order_id' => 'sc-order-002'],
                ['order_id' => 'sc-order-003']
            ],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocDataList);

        // Mock FOSS_ORDERRequest and verify only valid order IDs are sent
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', Mockery::on(function ($params) {
                return isset($params['order_ids']) &&
                       count($params['order_ids']) === 3 &&
                       in_array('sc-order-001', $params['order_ids']) &&
                       in_array('sc-order-002', $params['order_ids']) &&
                       in_array('sc-order-003', $params['order_ids']) &&
                       !in_array(null, $params['order_ids']) &&
                       !in_array('', $params['order_ids']);
            }))
            ->andReturn($fossOrderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handle method with pagination parameters
     */
    public function testHandleWithPaginationParameters()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-04-01 00:00:00',
            'end_time' => '2023-04-30 23:59:59',
            'page' => 3,
            'page_size' => 50
        ];

        $orderAssocDataList = [
            ['self_order_id' => 'sc-order-page-001'],
            ['self_order_id' => 'sc-order-page-002']
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [
                ['order_id' => 'sc-order-page-001'],
                ['order_id' => 'sc-order-page-002']
            ],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocDataList);

        // Mock FOSS_ORDERRequest and verify pagination parameters are passed
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', Mockery::on(function ($params) {
                return isset($params['page']) &&
                       $params['page'] === 3 &&
                       isset($params['page_size']) &&
                       $params['page_size'] === 50;
            }))
            ->andReturn($fossOrderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test constructor sets data correctly
     */
    public function testConstructorSetsDataCorrectly()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'order_id' => 'sc-test-order',
            'start_time' => '2023-01-01 00:00:00'
        ];

        $logic = new SC($data);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $actualData = $property->getValue($logic);

        $this->assertEquals($data, $actualData);
    }

    /**
     * Test handle method with refund status filter
     */
    public function testHandleWithRefundStatusFilter()
    {
        $data = [
            'auth_data' => [
                'name_abbreviation' => 'sc'
            ],
            'start_time' => '2023-05-01 00:00:00',
            'end_time' => '2023-05-31 23:59:59',
            'refund_status' => 'refunded'
        ];

        $orderAssocDataList = [
            ['self_order_id' => 'sc-refund-order-001']
        ];

        $fossOrderData = [
            'code' => 0,
            'data' => [['order_id' => 'sc-refund-order-001', 'refund_status' => 'refunded']],
            'msg' => 'success'
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocDataList);

        // Mock FOSS_ORDERRequest and verify refund status parameter is passed
        $mockFossRequest = Mockery::mock('alias:' . FOSS_ORDERRequest::class);
        $mockFossRequest->shouldReceive('handle')
            ->once()
            ->with('/api/oil_adapter/queryOrder', Mockery::on(function ($params) {
                return isset($params['refund_status']) &&
                       $params['refund_status'] === 'refunded';
            }))
            ->andReturn($fossOrderData);

        $expectedResponse = new JsonResponse(['code' => 0]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new SC($data);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }
}
