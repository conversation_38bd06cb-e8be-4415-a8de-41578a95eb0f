<?php

namespace Tests\Unit\Models\Logic\Order\Query;

use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Order\Query\FO;
use App\Models\Logic\Order\Query\ThirdParty;
use ErrorException;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Query\FO
 */
class FOTest extends TestCase
{
    /**     */
    public function testHandleWithValidOrderId()
    {
        $data = [
            'order_id' => 'foss-fo-order-123'
        ];

        $orderAssocInfo = [
            'platform_order_status' => 1
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'self_order_id',
                    'operator' => '=',
                    'value' => 'foss-fo-order-123',
                ]
            ], ['platform_order_status'])
            ->andReturn($orderAssocInfo);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        // Create FO instance
        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with valid order_id     */

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::handle
     * Test handle method with empty order_id     */

    /**     */
    public function testHandleWithEmptyOrderId()
    {
        $data = [
            'order_id' => ''
        ];

        $orderAssocInfo = [];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'self_order_id',
                    'operator' => '=',
                    'value' => '',
                ]
            ], ['platform_order_status'])
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with null order_id     */
    /**     */
    public function testHandleWithNullOrderId()
    {
        $data = [
            'order_id' => null
        ];

        $orderAssocInfo = null;

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'self_order_id',
                    'operator' => '=',
                    'value' => null,
                ]
            ], ['platform_order_status'])
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with OrderAssocData exception     */
    /**     */
    public function testHandleWithOrderAssocDataException()
    {
        $data = [
            'order_id' => 'foss-fo-order-error'
        ];

        // Mock OrderAssocData to throw exception
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andThrow(new Exception('Database connection failed', 5000023));

        $logic = new FO($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database connection failed');
        $this->expectExceptionCode(5000023);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with multiple platform_order_status values
     */
    /**     */
    public function testHandleWithMultiplePlatformOrderStatusValues()
    {
        $data = [
            'order_id' => 'foss-fo-order-multiple'
        ];

        $orderAssocInfo = [
            'platform_order_status' => 2 // different status
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with very long order_id
     */
    /**     */
    public function testHandleWithVeryLongOrderId()
    {
        $longOrderId = str_repeat('a', 255);
        $data = [
            'order_id' => $longOrderId
        ];

        $orderAssocInfo = [
            'platform_order_status' => 1
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'self_order_id',
                    'operator' => '=',
                    'value' => $longOrderId,
                ]
            ], ['platform_order_status'])
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method with special characters in order_id
     */
    /**     */
    public function testHandleWithSpecialCharactersInOrderId()
    {
        $specialOrderId = 'foss-fo-order-特殊字符-123';
        $data = [
            'order_id' => $specialOrderId
        ];

        $orderAssocInfo = [
            'platform_order_status' => 3
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with([
                [
                    'field' => 'self_order_id',
                    'operator' => '=',
                    'value' => $specialOrderId,
                ]
            ], ['platform_order_status'])
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test inheritance from ThirdParty class
     */
    /**     */
    public function testInheritanceFromThirdParty()
    {
        $logic = new FO([]);
        $this->assertInstanceOf(ThirdParty::class, $logic);
    }

    /**
     * Test that data property exists and is accessible
     */
    /**     */
    public function testDataPropertyExists()
    {
        $testData = ['order_id' => 'test-order'];
        $logic = new FO($testData);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('data'));

        $property = $reflection->getProperty('data');
        $property->setAccessible(true);

        // Test getting property
        $this->assertEquals($testData, $property->getValue($logic));
    }

    /**
     * Test handle method parameter validation for OrderAssocData query
     */
    /**     */
    public function testHandleMethodParameterValidationForOrderAssocDataQuery()
    {
        $data = [
            'order_id' => 'test-order-validation'
        ];

        $orderAssocInfo = [
            'platform_order_status' => 1
        ];

        // Mock OrderAssocData and verify exact parameters
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->with(
                Mockery::on(function ($conditions) {
                    return is_array($conditions) &&
                           count($conditions) === 1 &&
                           isset($conditions[0]['field']) &&
                           $conditions[0]['field'] === 'self_order_id' &&
                           isset($conditions[0]['operator']) &&
                           $conditions[0]['operator'] === '=' &&
                           isset($conditions[0]['value']) &&
                           $conditions[0]['value'] === 'test-order-validation';
                }),
                Mockery::on(function ($fields) {
                    return is_array($fields) &&
                           count($fields) === 1 &&
                           $fields[0] === 'platform_order_status';
                })
            )
            ->andReturn($orderAssocInfo);

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => $orderAssocInfo
        ]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new FO($data);

        $result = $logic->handle();

        // Verify that the method was called with correct parameters
        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test handle method with missing order_id key
     */
    /**     */
    public function testHandleWithMissingOrderIdKey()
    {
        $data = []; // missing order_id key

        $logic = new FO($data);

        // Should throw exception when order_id key is missing
        $this->expectException(ErrorException::class);
        $this->expectExceptionMessage('Undefined index: order_id');

        $logic->handle();
    }

    /**
     * Test handle method with responseFormat function call validation
     */
    /**     */
    public function testHandleWithResponseFormatFunctionCallValidation()
    {
        $data = [
            'order_id' => 'test-response-format'
        ];

        $orderAssocInfo = [
            'platform_order_status' => 4
        ];

        // Mock OrderAssocData
        $mockOrderAssocData = Mockery::mock('alias:' . OrderAssocData::class);
        $mockOrderAssocData->shouldReceive('getOrderInfoByWhere')
            ->once()
            ->andReturn($orderAssocInfo);

        // Mock responseFormat function and verify parameters
        $this->mockFunction('responseFormat', function ($code, $data) use ($orderAssocInfo) {
            if ($code === 0 && $data === $orderAssocInfo) {
                return new JsonResponse(['code' => 0, 'data' => $data]);
            }
            return new JsonResponse(['code' => $code, 'data' => $data]);
        });

        $logic = new FO($data);

        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
