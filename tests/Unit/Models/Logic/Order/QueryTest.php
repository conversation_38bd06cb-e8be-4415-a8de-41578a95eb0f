<?php

namespace Unit\Models\Logic\Order;

use App\Models\Logic\Order\Query;
use App\Models\Logic\Order\Query\FO;
use App\Models\Logic\Order\Query\GS;
use App\Models\Logic\Order\Query\KY;
use App\Models\Logic\Order\Query\MB;
use App\Models\Logic\Order\Query\SC;
use App\Models\Logic\Order\Query\YC;
use App\Models\Logic\Order\Query\YGY;
use App\Models\Logic\Order\Query\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Symfony\Component\HttpFoundation\Response;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Query
 */
class QueryTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::__construct
     * Test constructor with valid platform
     */
    public function testConstructorWithValidPlatform()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new Query($parameter);
        $this->assertInstanceOf(Query::class, $logic);
    }

    /**
     * @covers ::__construct
     * Test constructor with invalid platform throws exception
     */
    public function testConstructorWithInvalidPlatformThrowsException()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'invalid_platform'
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new Query($parameter);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'yc'
            ]
        ];

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => ['orders' => []]]);
        
        $mockWorker = Mockery::mock(YC::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);
        
        Mockery::mock('overload:' . YC::class, $mockWorker);

        $logic = new Query($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * Test worker mapping contains expected platforms
     */
    public function testWorkerMappingContainsExpectedPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new Query($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('workerMapping');
        $property->setAccessible(true);
        $mapping = $property->getValue($logic);

        $expectedPlatforms = ['zy', 'yc', 'sc', 'sp', 'fo', 'sx', 'fj', 'gs', 'gsp', 'hb', 'ygy', 'mb', 'ky', 'zjqp'];
        
        foreach ($expectedPlatforms as $platform) {
            $this->assertArrayHasKey($platform, $mapping);
        }
    }

    /**
     * Test constructor sets correct worker for each platform
     */
    public function testConstructorSetsCorrectWorkerForEachPlatform()
    {
        $platforms = [
            'zy' => ZY::class,
            'yc' => YC::class,
            'sc' => SC::class,
            'fo' => FO::class,
            'gs' => GS::class,
            'ygy' => YGY::class,
            'mb' => MB::class,
            'ky' => KY::class,
        ];

        foreach ($platforms as $platform => $expectedClass) {
            $parameter = [
                'auth_data' => [
                    'name_abbreviation' => $platform
                ]
            ];

            $mockWorker = Mockery::mock($expectedClass);
            Mockery::mock('overload:' . $expectedClass, $mockWorker);

            $logic = new Query($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf($expectedClass, $worker);
        }
    }

    /**
     * Test platforms that map to SC class
     */
    public function testPlatformsThatMapToScClass()
    {
        $scPlatforms = ['sc', 'sx', 'fj', 'hb', 'zjqp'];

        foreach ($scPlatforms as $platform) {
            $parameter = [
                'auth_data' => [
                    'name_abbreviation' => $platform
                ]
            ];

            $mockWorker = Mockery::mock(SC::class);
            Mockery::mock('overload:' . SC::class, $mockWorker);

            $logic = new Query($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf(SC::class, $worker);
        }
    }

    /**
     * Test handle method with Response return type
     */
    public function testHandleWithResponseReturnType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fo'
            ]
        ];

        $expectedResponse = new Response('success', 200);
        
        $mockWorker = Mockery::mock(FO::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);
        
        Mockery::mock('overload:' . FO::class, $mockWorker);

        $logic = new Query($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(Response::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * Test handle method with exception from worker
     */
    public function testHandleWithExceptionFromWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        $mockWorker->shouldReceive('handle')->once()->andThrow(new Exception('Worker error', 5000001));
        
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new Query($parameter);
        
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Worker error');
        $this->expectExceptionCode(5000001);
        
        $logic->handle();
    }

    /**
     * Test constructor with empty auth data
     */
    public function testConstructorWithEmptyAuthData()
    {
        $parameter = [
            'auth_data' => []
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new Query($parameter);
    }

    /**
     * Test constructor with missing name_abbreviation
     */
    public function testConstructorWithMissingNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new Query($parameter);
    }

    /**
     * Test that data is properly passed to worker
     */
    public function testDataIsProperlyPassedToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'start_time' => '2023-01-01 00:00:00',
            'end_time' => '2023-01-31 23:59:59'
        ];

        $mockWorker = Mockery::mock(ZY::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn(new JsonResponse(['code' => 0]));
        
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new Query($parameter);
        $logic->handle();
    }
}
