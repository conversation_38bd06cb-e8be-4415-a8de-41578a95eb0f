<?php

namespace Tests\Unit\Models\Logic\Order\Refund;

use App\Models\Logic\Order\Refund\FY;
use App\Models\Logic\Order\Refund\ThirdParty;
use Exception;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Request\FY as FYRequest;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Refund\FY
 */
class FYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test handle method with valid order data
     */
    public function testHandleWithValidOrderData()
    {
        $orderAssocData = [
            'self_order_id' => 'foss-fy-order-123',
            'platform_order_id' => 'fy-trade-456',
            'platform_name' => 'fy',
            'self_order_status' => 1,
            'platform_order_status' => 1
        ];

        // Mock FYRequest
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => 'foss-fy-order-123',
                'tradeId' => 'fy-trade-456',
            ], 'post')
            ->andReturn([
                'status' => ['code' => 0],
                'data' => ['success' => true],
                'msg' => 'Refund successful'
            ]);

        // Create FY instance with order data
        $logic = new FY(['orderAssocData' => $orderAssocData]);

        // Execute handle method
        $result = $logic->handle();

        // Assert that handle method completes without throwing exception
        $this->assertNull($result);
    }

    /**
     * @covers ::handle
     * Test handle method with empty self_order_id
     */
    public function testHandleWithEmptySelfOrderId()
    {
        $orderAssocData = [
            'self_order_id' => '',
            'platform_order_id' => 'fy-trade-789',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => '',
                'tradeId' => 'fy-trade-789',
            ], 'post')
            ->andReturn([
                'status' => ['code' => 0],
                'data' => ['success' => true]
            ]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $result = $logic->handle();
        $this->assertNull($result);
    }

    /**
     * @covers ::handle
     * Test handle method with FYRequest exception
     */
    public function testHandleWithFYRequestException()
    {
        $orderAssocData = [
            'self_order_id' => 'foss-fy-error-order',
            'platform_order_id' => 'fy-error-trade',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest to throw exception
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => 'foss-fy-error-order',
                'tradeId' => 'fy-error-trade',
            ], 'post')
            ->andThrow(new Exception('FY refund failed', 5000020));

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('FY refund failed');
        $this->expectExceptionCode(5000020);

        $logic->handle();
    }

    /**
     * @covers ::handle
     * Test handle method with null order data
     */
    public function testHandleWithNullOrderData()
    {
        $orderAssocData = [
            'self_order_id' => null,
            'platform_order_id' => null,
            'platform_name' => 'fy'
        ];

        // Mock FYRequest
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => null,
                'tradeId' => null,
            ], 'post')
            ->andReturn([
                'status' => ['code' => 0],
                'data' => ['success' => true]
            ]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $result = $logic->handle();
        $this->assertNull($result);
    }

    /**
     * @covers ::handle
     * Test handle method with FY API error response
     */
    public function testHandleWithFYApiErrorResponse()
    {
        $orderAssocData = [
            'self_order_id' => 'foss-fy-api-error',
            'platform_order_id' => 'fy-api-error-trade',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest to return error response
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => 'foss-fy-api-error',
                'tradeId' => 'fy-api-error-trade',
            ], 'post')
            ->andReturn([
                'status' => ['code' => 1],
                'data' => ['success' => false],
                'msg' => 'API error'
            ]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        // Should not throw exception even with error response
        $result = $logic->handle();
        $this->assertNull($result);
    }

    /**
     * @covers ::handle
     * Test handle method with network timeout exception
     */
    public function testHandleWithNetworkTimeoutException()
    {
        $orderAssocData = [
            'self_order_id' => 'foss-fy-timeout',
            'platform_order_id' => 'fy-timeout-trade',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest to throw timeout exception
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Network timeout', 5000021));

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Network timeout');
        $this->expectExceptionCode(5000021);

        $logic->handle();
    }

    /**
     * Test inheritance from ThirdParty class
     */
    public function testInheritanceFromThirdParty()
    {
        $logic = new FY(['orderAssocData' => []]);
        $this->assertInstanceOf(ThirdParty::class, $logic);
    }

    /**
     * Test that orderAssocData property exists and is accessible
     */
    public function testOrderAssocDataPropertyExists()
    {
        $testData = ['test' => 'value'];
        $logic = new FY(['orderAssocData' => $testData]);
        $reflection = new ReflectionClass($logic);

        $this->assertTrue($reflection->hasProperty('orderAssocData'));

        $property = $reflection->getProperty('orderAssocData');
        $property->setAccessible(true);

        // Test getting property
        $this->assertEquals($testData, $property->getValue($logic));
    }

    /**
     * Test handle method parameter validation
     */
    public function testHandleMethodParameterValidation()
    {
        $orderAssocData = [
            'self_order_id' => 'test-order-id',
            'platform_order_id' => 'test-platform-id',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest and verify exact parameters
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with(
                Mockery::on(function ($method) {
                    return $method === 'delTrade';
                }),
                Mockery::on(function ($params) {
                    return isset($params['orderId']) &&
                           isset($params['tradeId']) &&
                           $params['orderId'] === 'test-order-id' &&
                           $params['tradeId'] === 'test-platform-id';
                }),
                Mockery::on(function ($httpMethod) {
                    return $httpMethod === 'post';
                })
            )
            ->andReturn(['status' => ['code' => 0]]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $result = $logic->handle();

        // Verify that the method was called with correct parameters
        $this->assertNull($result);
    }

    /**
     * Test handle method with special characters in order IDs
     */
    public function testHandleWithSpecialCharactersInOrderIds()
    {
        $orderAssocData = [
            'self_order_id' => 'foss-fy-order-特殊字符-123',
            'platform_order_id' => 'fy-trade-特殊字符-456',
            'platform_name' => 'fy'
        ];

        // Mock FYRequest
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => 'foss-fy-order-特殊字符-123',
                'tradeId' => 'fy-trade-特殊字符-456',
            ], 'post')
            ->andReturn([
                'status' => ['code' => 0],
                'data' => ['success' => true]
            ]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $result = $logic->handle();
        $this->assertNull($result);
    }

    /**
     * Test handle method with very long order IDs
     */
    public function testHandleWithVeryLongOrderIds()
    {
        $longOrderId = str_repeat('a', 255);
        $longTradeId = str_repeat('b', 255);

        $orderAssocData = [
            'self_order_id' => $longOrderId,
            'platform_order_id' => $longTradeId,
            'platform_name' => 'fy'
        ];

        // Mock FYRequest
        $mockFYRequest = Mockery::mock('alias:' . FYRequest::class);
        $mockFYRequest->shouldReceive('handle')
            ->once()
            ->with('delTrade', [
                'orderId' => $longOrderId,
                'tradeId' => $longTradeId,
            ], 'post')
            ->andReturn([
                'status' => ['code' => 0],
                'data' => ['success' => true]
            ]);

        $logic = new FY(['orderAssocData' => $orderAssocData]);

        $result = $logic->handle();
        $this->assertNull($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
