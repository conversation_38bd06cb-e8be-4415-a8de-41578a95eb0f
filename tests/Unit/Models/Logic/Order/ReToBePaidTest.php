<?php

namespace Unit\Models\Logic\Order;

use App\Models\Logic\Order\ReToBePaid;
use App\Models\Logic\Order\ReToBePaid\FO;
use App\Models\Logic\Order\ReToBePaid\ZY;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\ReToBePaid
 */
class ReToBePaidTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::__construct
     * Test constructor with valid platform
     */
    public function testConstructorWithValidPlatform()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $this->assertInstanceOf(ReToBePaid::class, $logic);
    }

    /**
     * @covers ::__construct
     * Test constructor with invalid platform throws exception
     */
    public function testConstructorWithInvalidPlatformThrowsException()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'invalid_platform'
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fo'
            ]
        ];

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => ['order_id' => 'test-order']]);
        
        $mockWorker = Mockery::mock(FO::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);
        
        Mockery::mock('overload:' . FO::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * Test worker mapping contains expected platforms
     */
    public function testWorkerMappingContainsExpectedPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('workerMapping');
        $property->setAccessible(true);
        $mapping = $property->getValue($logic);

        $expectedPlatforms = ['zy', 'fo'];
        
        foreach ($expectedPlatforms as $platform) {
            $this->assertArrayHasKey($platform, $mapping);
        }

        $this->assertEquals(ZY::class, $mapping['zy']);
        $this->assertEquals(FO::class, $mapping['fo']);
    }

    /**
     * Test constructor sets correct worker for each platform
     */
    public function testConstructorSetsCorrectWorkerForEachPlatform()
    {
        $platforms = [
            'zy' => ZY::class,
            'fo' => FO::class,
        ];

        foreach ($platforms as $platform => $expectedClass) {
            $parameter = [
                'auth_data' => [
                    'name_abbreviation' => $platform
                ]
            ];

            $mockWorker = Mockery::mock($expectedClass);
            Mockery::mock('overload:' . $expectedClass, $mockWorker);

            $logic = new ReToBePaid($parameter);
            $reflection = new ReflectionClass($logic);
            $property = $reflection->getProperty('worker');
            $property->setAccessible(true);
            $worker = $property->getValue($logic);

            $this->assertInstanceOf($expectedClass, $worker);
        }
    }

    /**
     * Test handle method with exception from worker
     */
    public function testHandleWithExceptionFromWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        $mockWorker->shouldReceive('handle')->once()->andThrow(new Exception('Worker error', 5000001));
        
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Worker error');
        $this->expectExceptionCode(5000001);
        
        $logic->handle();
    }

    /**
     * Test constructor with empty auth data
     */
    public function testConstructorWithEmptyAuthData()
    {
        $parameter = [
            'auth_data' => []
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * Test constructor with missing name_abbreviation
     */
    public function testConstructorWithMissingNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'role' => 1
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * Test constructor with null name_abbreviation
     */
    public function testConstructorWithNullNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => null
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * Test constructor with empty string name_abbreviation
     */
    public function testConstructorWithEmptyStringNameAbbreviation()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => ''
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * Test that data is properly passed to worker
     */
    public function testDataIsProperlyPassedToWorker()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ],
            'station_id' => 'test-station',
            'order_id' => 'test-order',
            'refueling_time' => '2023-01-01 12:00:00'
        ];

        $mockWorker = Mockery::mock(ZY::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn(new JsonResponse(['code' => 0]));
        
        // Verify that the worker is constructed with the correct data
        Mockery::mock('overload:' . ZY::class)
            ->shouldReceive('__construct')
            ->once()
            ->with($parameter)
            ->andReturn($mockWorker);

        $logic = new ReToBePaid($parameter);
        $logic->handle();
    }

    /**
     * Test constructor with case-sensitive platform names
     */
    public function testConstructorWithCaseSensitivePlatformNames()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'ZY'  // uppercase
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);

        new ReToBePaid($parameter);
    }

    /**
     * Test worker mapping is immutable
     */
    public function testWorkerMappingIsImmutable()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy'
            ]
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('workerMapping');
        $property->setAccessible(true);
        $mapping = $property->getValue($logic);

        // Verify the mapping contains only expected platforms
        $this->assertCount(2, $mapping);
        $this->assertArrayHasKey('zy', $mapping);
        $this->assertArrayHasKey('fo', $mapping);
    }

    /**
     * Test handle method returns correct response type
     */
    public function testHandleReturnsCorrectResponseType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fo'
            ]
        ];

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'order_id' => 'test-order-123',
                'status' => 'completed'
            ]
        ]);
        
        $mockWorker = Mockery::mock(FO::class);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);
        
        Mockery::mock('overload:' . FO::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $result = $logic->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse->getContent(), $result->getContent());
        $this->assertEquals($expectedResponse->getStatusCode(), $result->getStatusCode());
    }

    /**
     * Test constructor with additional data fields
     */
    public function testConstructorWithAdditionalDataFields()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'zy',
                'role' => 1,
                'user_id' => 123
            ],
            'station_id' => 'station-001',
            'order_id' => 'order-001',
            'oil_num' => 50.5,
            'price' => 6.8,
            'money' => 343.4,
            'oil_name' => '92#汽油',
            'refueling_time' => '2023-01-01 12:00:00'
        ];

        $mockWorker = Mockery::mock(ZY::class);
        Mockery::mock('overload:' . ZY::class, $mockWorker);

        $logic = new ReToBePaid($parameter);
        $this->assertInstanceOf(ReToBePaid::class, $logic);
    }
}
