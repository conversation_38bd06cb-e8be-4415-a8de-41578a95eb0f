<?php

namespace Unit\Models\Logic\Order;

use App\Models\Data\OrderRefund as OrderRefundData;
use App\Models\Logic\Order\Refund;
use App\Models\Logic\Order\Refund\Main as OrderRefundMainLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Order\Refund
 */
class RefundTest extends TestCase
{
    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @covers ::getRefundList
     * Test getRefundList method with date range
     */
    public function testGetRefundListWithDateRange()
    {
        $data = [
            'created_at' => '2023-01-01 00:00:00 - 2023-01-31 23:59:59',
            'status' => 1
        ];

        $expectedData = [
            'total' => 10,
            'data' => [
                ['id' => 1, 'order_id' => 'order-001'],
                ['id' => 2, 'order_id' => 'order-002']
            ]
        ];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['createdStartTime'] === '2023-01-01 00:00:00' &&
                       $arg['createdEndTime'] === '2023-01-31 23:59:59' &&
                       $arg['status'] === 1;
            }))
            ->andReturn($expectedData);

        // Mock responseFormat function
        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Helper method to mock global functions
     */
    private function mockFunction($functionName, $returnValue)
    {
        if (!function_exists($functionName)) {
            eval("function $functionName() { return \$GLOBALS['mock_$functionName']; }");
        }
        $GLOBALS["mock_$functionName"] = $returnValue;
    }

    /**
     * @covers ::getRefundList
     * Test getRefundList method without date range
     */
    public function testGetRefundListWithoutDateRange()
    {
        $data = [
            'status' => 2,
            'order_id' => 'test-order'
        ];

        $expectedData = [
            'total' => 5,
            'data' => [
                ['id' => 3, 'order_id' => 'test-order']
            ]
        ];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->with(Mockery::on(function ($arg) {
                return $arg['status'] === 2 &&
                       $arg['order_id'] === 'test-order' &&
                       !isset($arg['createdStartTime']) &&
                       !isset($arg['createdEndTime']);
            }))
            ->andReturn($expectedData);

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::getRefundList
     * Test getRefundList method filters empty values
     */
    public function testGetRefundListFiltersEmptyValues()
    {
        $data = [
            'status' => 1,
            'order_id' => '',
            'empty_field' => null,
            'zero_value' => 0,
            'false_value' => false
        ];

        $expectedData = ['total' => 0, 'data' => []];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->with(Mockery::on(function ($arg) {
                // Should only contain non-empty values
                return $arg['status'] === 1 &&
                       !isset($arg['order_id']) &&
                       !isset($arg['empty_field']) &&
                       !isset($arg['zero_value']) &&
                       !isset($arg['false_value']);
            }))
            ->andReturn($expectedData);

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle method (currently empty implementation)
     */
    public function testHandle()
    {
        $data = ['test' => 'data'];
        $logic = new Refund($data);

        // handle method is currently empty, so it should return null
        $result = $logic->handle();
        $this->assertNull($result);
    }

    /**
     * @covers ::doRefund
     * Test doRefund method delegates to OrderRefundMainLogic
     */
    public function testDoRefund()
    {
        $data = [
            'order_id' => 'test-order',
            'refund_amount' => 100.50,
            'reason' => 'Customer request'
        ];

        $expectedResponse = new JsonResponse([
            'code' => 0,
            'data' => ['refund_id' => 'refund-123']
        ]);

        // Mock OrderRefundMainLogic
        $mockRefundLogic = Mockery::mock('overload:' . OrderRefundMainLogic::class);
        $mockRefundLogic->shouldReceive('handle')
            ->once()
            ->andReturn($expectedResponse);

        $logic = new Refund($data);
        $result = $logic->doRefund();

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * @covers ::doRefund
     * Test doRefund method with exception
     */
    public function testDoRefundWithException()
    {
        $data = [
            'order_id' => 'invalid-order'
        ];

        // Mock OrderRefundMainLogic to throw exception
        $mockRefundLogic = Mockery::mock('overload:' . OrderRefundMainLogic::class);
        $mockRefundLogic->shouldReceive('handle')
            ->once()
            ->andThrow(new Exception('Refund failed', 5000001));

        $logic = new Refund($data);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Refund failed');
        $this->expectExceptionCode(5000001);

        $logic->doRefund();
    }

    /**
     * Test constructor sets data correctly
     */
    public function testConstructorSetsDataCorrectly()
    {
        $data = [
            'order_id' => 'test-order',
            'status' => 1,
            'created_at' => '2023-01-01 00:00:00 - 2023-01-31 23:59:59'
        ];

        $logic = new Refund($data);
        $reflection = new ReflectionClass($logic);
        $property = $reflection->getProperty('data');
        $property->setAccessible(true);
        $actualData = $property->getValue($logic);

        $this->assertEquals($data, $actualData);
    }

    /**
     * Test getRefundList with malformed date range
     */
    public function testGetRefundListWithMalformedDateRange()
    {
        $data = [
            'created_at' => 'invalid-date-range',
            'status' => 1
        ];

        $expectedData = ['total' => 0, 'data' => []];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->andReturn($expectedData);

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test getRefundList with single date (no range)
     */
    public function testGetRefundListWithSingleDate()
    {
        $data = [
            'created_at' => '2023-01-01 00:00:00',
            'status' => 1
        ];

        $expectedData = ['total' => 0, 'data' => []];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->andReturn($expectedData);

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test doRefund passes correct data to OrderRefundMainLogic
     */
    public function testDoRefundPassesCorrectDataToLogic()
    {
        $data = [
            'order_id' => 'test-order-123',
            'refund_amount' => 250.75,
            'reason' => 'Product defect',
            'operator_id' => 456
        ];

        $expectedResponse = new JsonResponse(['code' => 0]);

        // Mock OrderRefundMainLogic and verify constructor receives correct data
        $mockRefundLogic = Mockery::mock('overload:' . OrderRefundMainLogic::class);
        $mockRefundLogic->shouldReceive('__construct')
            ->once()
            ->with($data)
            ->andReturnSelf();
        $mockRefundLogic->shouldReceive('handle')
            ->once()
            ->andReturn($expectedResponse);

        $logic = new Refund($data);
        $result = $logic->doRefund();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test getRefundList with empty data array
     */
    public function testGetRefundListWithEmptyData()
    {
        $data = [];

        $expectedData = ['total' => 0, 'data' => []];

        // Mock OrderRefundData
        $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
        $mockOrderRefundData->shouldReceive('getData')
            ->once()
            ->with([])
            ->andReturn($expectedData);

        $expectedResponse = new JsonResponse(['code' => 0, 'data' => $expectedData]);
        $this->mockFunction('responseFormat', $expectedResponse);

        $logic = new Refund($data);
        $result = $logic->getRefundList();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test date range parsing with different formats
     */
    public function testDateRangeParsingWithDifferentFormats()
    {
        $testCases = [
            '2023-01-01 - 2023-01-31' => ['2023-01-01', '2023-01-31'],
            '2023-01-01 00:00:00 - 2023-01-31 23:59:59' => ['2023-01-01 00:00:00', '2023-01-31 23:59:59'],
            '2023-01-01  -  2023-01-31' => ['2023-01-01', '2023-01-31'], // extra spaces
        ];

        foreach ($testCases as $dateRange => $expected) {
            $data = ['created_at' => $dateRange];

            $mockOrderRefundData = Mockery::mock('alias:' . OrderRefundData::class);
            $mockOrderRefundData->shouldReceive('getData')
                ->once()
                ->with(Mockery::on(function ($arg) use ($expected) {
                    return $arg['createdStartTime'] === $expected[0] &&
                           $arg['createdEndTime'] === $expected[1];
                }))
                ->andReturn(['total' => 0, 'data' => []]);

            $expectedResponse = new JsonResponse(['code' => 0, 'data' => []]);
            $this->mockFunction('responseFormat', $expectedResponse);

            $logic = new Refund($data);
            $result = $logic->getRefundList();

            $this->assertInstanceOf(JsonResponse::class, $result);
        }
    }
}
