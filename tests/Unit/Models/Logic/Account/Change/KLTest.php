<?php

namespace Unit\Models\Logic\Account\Change;

use App\Models\Logic\Account\Change\KL;
use App\Models\Logic\Account\Change\ThirdParty;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Change\KL
 */
class KLTest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful account change
     */
    public function testHandleSuccess()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token_123',
            'card_no' => '**********',
            'account_no' => '**********'
        ];

        // Mock FOSS_USERRequest
        $mockFOSSUserRequest = Mockery::mock('alias:Request\FOSS_USER');
        $mockFOSSUserRequest->shouldReceive('handle')
            ->with('api/wxApp/card/changeAccountForAdapter', [
                'X-G7-Api-Token:test_token_123',
                'X-G7-Api-Client:adapter',
            ], [
                'vice_no' => '**********',
                'account_no' => '**********',
            ])
            ->once()
            ->andReturn([]);

        $kl = new KL($parameter);
        $result = $kl->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertEquals(0, $responseData['code']);
    }

    /**
     * Test that KL extends ThirdParty
     */
    public function testKLExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ]
        ];

        $kl = new KL($parameter);
        $this->assertInstanceOf(ThirdParty::class, $kl);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token',
            'card_no' => '**********',
            'account_no' => '**********'
        ];

        $kl = new KL($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($kl);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($kl);
        $this->assertEquals($parameter, $data);
        
        $accessKeyProperty = $reflection->getProperty('accessKey');
        $accessKeyProperty->setAccessible(true);
        $accessKey = $accessKeyProperty->getValue($kl);
        $this->assertEquals('test_access_key', $accessKey);
        
        $secretProperty = $reflection->getProperty('secret');
        $secretProperty->setAccessible(true);
        $secret = $secretProperty->getValue($kl);
        $this->assertEquals('test_secret', $secret);
        
        $nameAbbreviationProperty = $reflection->getProperty('name_abbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviation = $nameAbbreviationProperty->getValue($kl);
        $this->assertEquals('kl', $nameAbbreviation);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token',
            'card_no' => '**********',
            'account_no' => '**********'
        ];

        $kl = new KL($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($kl);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    /**
     * Test with missing parameters
     */
    public function testHandleWithMissingParameters()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ]
            // Missing token, card_no, account_no
        ];

        $kl = new KL($parameter);
        
        // Test that the object can be created even with missing parameters
        $this->assertInstanceOf(KL::class, $kl);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
