<?php

namespace Unit\Models\Logic\Account\Change;

use App\Models\Logic\Account\Change\KL;
use App\Models\Logic\Account\Change\Main;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Change\Main
 */
class MainTest extends TestCase
{
    /**
     * @covers ::__construct
     * Test constructor with KL worker
     */
    public function testConstructorWithKlWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $worker = $workerProperty->getValue($main);

        $this->assertInstanceOf(KL::class, $worker);
    }

    /**
     * @covers ::__construct
     * Test constructor with unsupported worker
     */
    public function testConstructorWithUnsupportedWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'unsupported'
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);
        
        new Main($parameter);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to KL worker and executes real logic
     */
    public function testHandleDelegatesToKLWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token_123',
            'card_no' => '**********',
            'account_no' => '**********'
        ];

        // Mock FOSS_USERRequest for KL worker
        $mockFOSSUserRequest = Mockery::mock('alias:Request\FOSS_USER');
        $mockFOSSUserRequest->shouldReceive('handle')
            ->with('api/wxApp/card/changeAccountForAdapter', [
                'X-G7-Api-Token:test_token_123',
                'X-G7-Api-Client:adapter',
            ], [
                'vice_no' => '**********',
                'account_no' => '**********',
            ])
            ->once()
            ->andReturn([]);

        $main = new Main($parameter);
        $result = $main->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertEquals(0, $responseData['code']);
    }

    /**
     * Test worker mapping contains all expected platforms
     */
    public function testWorkerMappingContainsAllPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerMappingProperty = $reflection->getProperty('workerMapping');
        $workerMappingProperty->setAccessible(true);
        $workerMapping = $workerMappingProperty->getValue($main);

        $expectedMappings = [
            'kl' => KL::class,
        ];

        foreach ($expectedMappings as $key => $expectedClass) {
            $this->assertArrayHasKey($key, $workerMapping);
            $this->assertEquals($expectedClass, $workerMapping[$key]);
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
