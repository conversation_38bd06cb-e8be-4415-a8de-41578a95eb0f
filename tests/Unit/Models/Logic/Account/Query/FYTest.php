<?php

namespace Unit\Models\Logic\Account\Query;

use App\Models\Logic\Account\Query\FY;
use App\Models\Logic\Account\Query\ThirdParty;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Query\FY
 */
class FYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful account balance query with positive cash balance
     */
    public function testHandleSuccessWithPositiveCashBalance()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ]
        ];

        // Mock FOSSRequest
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.getOrgCashBalance', [
                'orgcode' => 'ORG123',
            ])
            ->once()
            ->andReturn([
                'data' => [
                    'cash_money' => '150.50',
                    'fanli_money' => '25.75'
                ]
            ]);

        $fy = new FY($parameter);
        $result = $fy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['status']['code']);
        $this->assertIsArray($responseData['data']);
        $this->assertCount(1, $responseData['data']);

        $accountData = $responseData['data'][0];
        $this->assertEquals('150.50', $accountData['cashAccountBalance']);
        $this->assertEquals('25.75', $accountData['returnAccountBalance']);
        $this->assertEquals('176.25', $accountData['accountBalance']);
    }

    /**
     * @covers ::handle
     * Test handle with negative cash balance (should be set to 0.00)
     */
    public function testHandleWithNegativeCashBalance()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG456'
            ]
        ];

        // Mock FOSSRequest
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.getOrgCashBalance', [
                'orgcode' => 'ORG456',
            ])
            ->once()
            ->andReturn([
                'data' => [
                    'cash_money' => '-50.25',  // Negative balance
                    'fanli_money' => '10.00'
                ]
            ]);

        $fy = new FY($parameter);
        $result = $fy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['status']['code']);
        $this->assertIsArray($responseData['data']);
        $this->assertCount(1, $responseData['data']);

        $accountData = $responseData['data'][0];
        // Negative cash balance should be converted to 0.00
        $this->assertEquals(0.00, $accountData['cashAccountBalance']);
        $this->assertEquals('10.00', $accountData['returnAccountBalance']);
        $this->assertEquals('10.00', $accountData['accountBalance']);
    }

    /**
     * Test that FY extends ThirdParty
     */
    public function testFYExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fy'
            ]
        ];

        $fy = new FY($parameter);
        $this->assertInstanceOf(ThirdParty::class, $fy);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ]
        ];

        $fy = new FY($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($fy);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($fy);
        $this->assertEquals($parameter, $data);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ]
        ];

        $fy = new FY($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($fy);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
