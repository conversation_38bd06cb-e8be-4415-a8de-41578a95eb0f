<?php

namespace Unit\Models\Logic\Account\Query;

use App\Models\Logic\Account\Query\SIMPLE;
use App\Models\Logic\Account\Query\ThirdParty;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Query\SIMPLE
 */
class SIMPLETest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful account balance query
     */
    public function testHandleSuccess()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => '',
                'card_no' => '**********'
            ]
        ];

        // Mock FOSSRequest
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.account_balance', [
                'vice_no' => '**********',
                'isCardBalance' => 1,
            ])
            ->once()
            ->andReturn([
                'data' => [
                    'use_balance' => '150.50'
                ]
            ]);

        $simple = new SIMPLE($parameter);
        $result = $simple->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['code']);
        $this->assertEquals('150.50', $responseData['data']['balance']);
    }

    /**
     * @covers ::handle
     * Test handle with zero balance
     */
    public function testHandleWithZeroBalance()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => '',
                'card_no' => '**********'
            ]
        ];

        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.account_balance', [
                'vice_no' => '**********',
                'isCardBalance' => 1,
            ])
            ->once()
            ->andReturn([
                'data' => [
                    'use_balance' => '0.00'
                ]
            ]);

        $simple = new SIMPLE($parameter);
        $result = $simple->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['code']);
        $this->assertEquals('0.00', $responseData['data']['balance']);
    }



    /**
     * Test that SIMPLE extends ThirdParty
     */
    public function testSIMPLEExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => ''
            ]
        ];

        $simple = new SIMPLE($parameter);
        $this->assertInstanceOf(ThirdParty::class, $simple);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => '',
                'card_no' => '**********'
            ],
            'additional_data' => 'test_value'
        ];

        $simple = new SIMPLE($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($simple);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($simple);
        $this->assertEquals($parameter, $data);
        
        $accessKeyProperty = $reflection->getProperty('accessKey');
        $accessKeyProperty->setAccessible(true);
        $accessKey = $accessKeyProperty->getValue($simple);
        $this->assertEquals('test_access_key', $accessKey);
        
        $secretProperty = $reflection->getProperty('secret');
        $secretProperty->setAccessible(true);
        $secret = $secretProperty->getValue($simple);
        $this->assertEquals('test_secret', $secret);
        
        $nameAbbreviationProperty = $reflection->getProperty('name_abbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviation = $nameAbbreviationProperty->getValue($simple);
        $this->assertEquals('', $nameAbbreviation);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => '',
                'card_no' => '**********'
            ]
        ];

        $simple = new SIMPLE($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($simple);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    /**
     * Test with missing card_no in auth_data
     */
    public function testHandleWithMissingCardNo()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => ''
                // Missing card_no
            ]
        ];

        $simple = new SIMPLE($parameter);
        
        // Test that the object can be created even with missing card_no
        $this->assertInstanceOf(SIMPLE::class, $simple);
    }

    /**
     * Test with empty auth_data
     */
    public function testHandleWithEmptyAuthData()
    {
        $parameter = [
            'auth_data' => []
        ];

        $simple = new SIMPLE($parameter);
        
        // Test that the object can be created with empty auth_data
        $this->assertInstanceOf(SIMPLE::class, $simple);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
