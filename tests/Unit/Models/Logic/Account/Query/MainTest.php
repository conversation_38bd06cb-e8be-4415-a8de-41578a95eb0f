<?php

namespace Unit\Models\Logic\Account\Query;

use App\Models\Logic\Account\Query\FY;
use App\Models\Logic\Account\Query\HTX;
use App\Models\Logic\Account\Query\JDWC;
use App\Models\Logic\Account\Query\KL;
use App\Models\Logic\Account\Query\Main;
use App\Models\Logic\Account\Query\SIMPLE;
use App\Models\Logic\Account\Query\YGY;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Query\Main
 */
class MainTest extends TestCase
{
    /**
     * @covers ::__construct
     * Test constructor with default worker (SIMPLE)
     */
    public function testConstructorWithDefaultWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'unknown'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $worker = $workerProperty->getValue($main);

        $this->assertInstanceOf(SIMPLE::class, $worker);
    }

    /**
     * @covers ::__construct
     * Test constructor with JDWC worker
     */
    public function testConstructorWithJdwcWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'jdwc'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $worker = $workerProperty->getValue($main);

        $this->assertInstanceOf(JDWC::class, $worker);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'jdwc',
                'card_no' => '**********'
            ],
            'cardNo' => '**********'
        ];

        // Mock FOSSRequest for JDWC worker
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.account_balance', [
                'vice_no' => '**********',
                'isCardBalance' => 1,
            ])
            ->once()
            ->andReturn([
                'data' => [
                    'use_balance' => '150.50'
                ]
            ]);

        // Create Main instance and execute handle
        $main = new Main($parameter);
        $result = $main->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['code']);
        $this->assertEquals(150.5, $responseData['data']['balance']);
        $this->assertEquals('**********', $responseData['data']['accountNumber']);
        $this->assertEquals(1, $responseData['data']['source']);
    }

    /**
     * Test worker mapping contains all expected platforms
     */
    public function testWorkerMappingContainsAllPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'test'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerMappingProperty = $reflection->getProperty('workerMapping');
        $workerMappingProperty->setAccessible(true);
        $workerMapping = $workerMappingProperty->getValue($main);

        $expectedMappings = [
            'jdwc' => JDWC::class,
            '' => SIMPLE::class,
            'kl' => KL::class,
            'htx' => HTX::class,
            'ygy' => YGY::class,
            'fy' => FY::class,
        ];

        foreach ($expectedMappings as $key => $expectedClass) {
            $this->assertArrayHasKey($key, $workerMapping);
            $this->assertEquals($expectedClass, $workerMapping[$key]);
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
