<?php

namespace Unit\Models\Logic\Account\Query;

use App\Models\Logic\Account\Query\KL;
use App\Models\Logic\Account\Query\ThirdParty;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Query\KL
 */
class KLTest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful account list query
     */
    public function testHandleSuccess()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token_123',
            'money' => '100.50'
        ];

        // Mock AuthConfigData
        $mockAuthConfigData = Mockery::mock('alias:App\Models\Data\AuthConfig');
        $mockAuthConfigData->shouldReceive('getAuthConfigValByName')
            ->with('KL_STATION_ID')
            ->once()
            ->andReturn('STATION_001');

        // Mock FOSS_USERRequest
        $mockFOSSUserRequest = Mockery::mock('alias:Request\FOSS_USER');
        $mockFOSSUserRequest->shouldReceive('handle')
            ->with('api/wxApp/card/accountListForAdapter', [
                'X-G7-Api-Token:test_token_123',
                'X-G7-Api-Client:adapter',
            ], [
                'token' => 'test_token_123',
                'station_id' => 'STATION_001',
                'oil_name' => '润滑油类',
                'pay_amount' => '100.50',
            ])
            ->once()
            ->andReturn([
                'data' => [
                    [
                        'vice_no' => '**********',
                        'is_check' => 1,
                        'balance' => '150.50'
                    ],
                    [
                        'vice_no' => '**********',
                        'is_check' => 0,
                        'balance' => '200.00'
                    ]
                ]
            ]);

        $kl = new KL($parameter);
        $result = $kl->handle();

        // The method should return a JsonResponse
        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle with empty response data
     */
    public function testHandleWithEmptyData()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token_123',
            'money' => '100.50'
        ];

        $mockAuthConfigData = Mockery::mock('alias:App\Models\Data\AuthConfig');
        $mockAuthConfigData->shouldReceive('getAuthConfigValByName')
            ->with('KL_STATION_ID')
            ->once()
            ->andReturn('STATION_001');

        $mockFOSSUserRequest = Mockery::mock('alias:Request\FOSS_USER');
        $mockFOSSUserRequest->shouldReceive('handle')
            ->with('api/wxApp/card/accountListForAdapter', [
                'X-G7-Api-Token:test_token_123',
                'X-G7-Api-Client:adapter',
            ], [
                'token' => 'test_token_123',
                'station_id' => 'STATION_001',
                'oil_name' => '润滑油类',
                'pay_amount' => '100.50',
            ])
            ->once()
            ->andReturn([
                'data' => []
            ]);

        $kl = new KL($parameter);
        $result = $kl->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * @covers ::handle
     * Test handle with single account
     */
    public function testHandleWithSingleAccount()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token_123',
            'money' => '50.00'
        ];

        $mockAuthConfigData = Mockery::mock('alias:App\Models\Data\AuthConfig');
        $mockAuthConfigData->shouldReceive('getAuthConfigValByName')
            ->with('KL_STATION_ID')
            ->once()
            ->andReturn('STATION_002');

        $mockFOSSUserRequest = Mockery::mock('alias:Request\FOSS_USER');
        $mockFOSSUserRequest->shouldReceive('handle')
            ->with('api/wxApp/card/accountListForAdapter', [
                'X-G7-Api-Token:test_token_123',
                'X-G7-Api-Client:adapter',
            ], [
                'token' => 'test_token_123',
                'station_id' => 'STATION_002',
                'oil_name' => '润滑油类',
                'pay_amount' => '50.00',
            ])
            ->once()
            ->andReturn([
                'data' => [
                    [
                        'vice_no' => '**********',
                        'is_check' => 1,
                        'balance' => '75.25',
                        'account_name' => 'Test Account'
                    ]
                ]
            ]);

        $kl = new KL($parameter);
        $result = $kl->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);
    }

    /**
     * Test that KL extends ThirdParty
     */
    public function testKLExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'kl'
            ]
        ];

        $kl = new KL($parameter);
        $this->assertInstanceOf(ThirdParty::class, $kl);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token',
            'money' => '100.00'
        ];

        $kl = new KL($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($kl);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($kl);
        $this->assertEquals($parameter, $data);
        
        $accessKeyProperty = $reflection->getProperty('accessKey');
        $accessKeyProperty->setAccessible(true);
        $accessKey = $accessKeyProperty->getValue($kl);
        $this->assertEquals('test_access_key', $accessKey);
        
        $secretProperty = $reflection->getProperty('secret');
        $secretProperty->setAccessible(true);
        $secret = $secretProperty->getValue($kl);
        $this->assertEquals('test_secret', $secret);
        
        $nameAbbreviationProperty = $reflection->getProperty('name_abbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviation = $nameAbbreviationProperty->getValue($kl);
        $this->assertEquals('kl', $nameAbbreviation);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token',
            'money' => '100.00'
        ];

        $kl = new KL($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($kl);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    /**
     * Test with missing token parameter
     */
    public function testHandleWithMissingToken()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'money' => '100.00'
            // Missing token
        ];

        $kl = new KL($parameter);
        
        // Test that the object can be created even with missing token
        $this->assertInstanceOf(KL::class, $kl);
    }

    /**
     * Test with missing money parameter
     */
    public function testHandleWithMissingMoney()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'kl'
            ],
            'token' => 'test_token'
            // Missing money
        ];

        $kl = new KL($parameter);
        
        // Test that the object can be created even with missing money
        $this->assertInstanceOf(KL::class, $kl);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
