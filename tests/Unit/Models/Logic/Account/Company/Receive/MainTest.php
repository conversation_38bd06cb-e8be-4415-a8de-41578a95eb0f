<?php

namespace Unit\Models\Logic\Account\Company\Receive;

use App\Models\Logic\Account\Company\Receive\Main;
use App\Models\Logic\Account\Company\Receive\YGY;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Company\Receive\Main
 */
class MainTest extends TestCase
{
    /**
     * @covers ::__construct
     * Test constructor with YGY worker
     */
    public function testConstructorWithYgyWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $worker = $workerProperty->getValue($main);

        $this->assertInstanceOf(YGY::class, $worker);
    }

    /**
     * @covers ::__construct
     * Test constructor with unsupported worker (should use default empty mapping)
     */
    public function testConstructorWithUnsupportedWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'unsupported'
            ]
        ];

        // This should not throw an exception but use the default empty mapping
        $main = new Main($parameter);
        $this->assertInstanceOf(Main::class, $main);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy'
            ]
        ];

        $mockWorker = Mockery::mock(YGY::class);
        $expectedResponse = new JsonResponse(['code' => 0]);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);

        $main = Mockery::mock(Main::class, [$parameter])->makePartial();
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $workerProperty->setValue($main, $mockWorker);

        $result = $main->handle();
        
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    /**
     * Test worker mapping contains all expected platforms
     */
    public function testWorkerMappingContainsAllPlatforms()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerMappingProperty = $reflection->getProperty('workerMapping');
        $workerMappingProperty->setAccessible(true);
        $workerMapping = $workerMappingProperty->getValue($main);

        $expectedMappings = [
            'ygy' => YGY::class,
        ];

        foreach ($expectedMappings as $key => $expectedClass) {
            $this->assertArrayHasKey($key, $workerMapping);
            $this->assertEquals($expectedClass, $workerMapping[$key]);
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
