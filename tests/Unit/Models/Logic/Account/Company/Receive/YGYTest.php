<?php

namespace Unit\Models\Logic\Account\Company\Receive;

use App\Models\Logic\Account\Company\Receive\ThirdParty;
use App\Models\Logic\Account\Company\Receive\YGY;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\Company\Receive\YGY
 */
class YGYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful company receive with status 1 (enable)
     */
    public function testHandleSuccessWithStatusEnable()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT123'
            ],
            'companyId' => 'COMP001',
            'companyName' => 'Test Company',
            'status' => '1',
            'receiptCode' => 'REC001'
        ];

        // Mock FOSSRequest for company receive
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.companyReceive', [
                'org_code' => 'ROOT123',
                'company_id' => 'COMP001',
                'company_name' => 'Test Company',
                'status' => 1,
                'receipt_code' => 'REC001',
            ])
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['result' => 'success']
            ]);

        $ygy = new YGY($parameter);
        $result = $ygy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertEquals(0, $responseData['code']);
    }

    /**
     * @covers ::handle
     * Test successful company receive with status 0 (disable)
     */
    public function testHandleSuccessWithStatusDisable()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT456'
            ],
            'companyId' => 'COMP002',
            'companyName' => 'Another Company',
            'status' => '0',
            'receiptCode' => 'REC002'
        ];

        // Mock FOSSRequest for company receive with status 0
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('gas.org_account.companyReceive', [
                'org_code' => 'ROOT456',
                'company_id' => 'COMP002',
                'company_name' => 'Another Company',
                'status' => 0,
                'receipt_code' => 'REC002',
            ])
            ->once()
            ->andReturn([
                'code' => 0,
                'data' => ['result' => 'disabled']
            ]);

        $ygy = new YGY($parameter);
        $result = $ygy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertEquals(0, $responseData['code']);
    }

    /**
     * Test that YGY extends ThirdParty
     */
    public function testYGYExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'ygy'
            ]
        ];

        $ygy = new YGY($parameter);
        $this->assertInstanceOf(ThirdParty::class, $ygy);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT123'
            ],
            'companyId' => 'COMP001',
            'companyName' => 'Test Company',
            'status' => '1'
        ];

        $ygy = new YGY($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($ygy);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($ygy);
        $this->assertEquals($parameter, $data);
        
        $accessKeyProperty = $reflection->getProperty('accessKey');
        $accessKeyProperty->setAccessible(true);
        $accessKey = $accessKeyProperty->getValue($ygy);
        $this->assertEquals('test_access_key', $accessKey);
        
        $secretProperty = $reflection->getProperty('secret');
        $secretProperty->setAccessible(true);
        $secret = $secretProperty->getValue($ygy);
        $this->assertEquals('test_secret', $secret);
        
        $nameAbbreviationProperty = $reflection->getProperty('name_abbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviation = $nameAbbreviationProperty->getValue($ygy);
        $this->assertEquals('ygy', $nameAbbreviation);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT123'
            ],
            'companyId' => 'COMP001',
            'companyName' => 'Test Company',
            'status' => '1'
        ];

        $ygy = new YGY($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($ygy);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    /**
     * Test with missing parameters
     */
    public function testHandleWithMissingParameters()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'ygy'
            ]
            // Missing companyId, companyName, status
        ];

        $ygy = new YGY($parameter);
        
        // Test that the object can be created even with missing parameters
        $this->assertInstanceOf(YGY::class, $ygy);
    }

    /**
     * Test with different status values
     */
    public function testHandleWithDifferentStatusValues()
    {
        // Test with status 0
        $parameter0 = [
            'auth_data' => [
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT123'
            ],
            'companyId' => 'COMP001',
            'companyName' => 'Test Company',
            'status' => '0'
        ];

        $ygy0 = new YGY($parameter0);
        $this->assertInstanceOf(YGY::class, $ygy0);

        // Test with status 1
        $parameter1 = [
            'auth_data' => [
                'name_abbreviation' => 'ygy',
                'role_code' => 'ROOT123'
            ],
            'companyId' => 'COMP002',
            'companyName' => 'Another Company',
            'status' => '1'
        ];

        $ygy1 = new YGY($parameter1);
        $this->assertInstanceOf(YGY::class, $ygy1);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
