<?php

namespace Unit\Models\Logic\Account\ChangeRecord;

use App\Models\Logic\Account\ChangeRecord\FY;
use App\Models\Logic\Account\ChangeRecord\Main;
use Exception;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\ChangeRecord\Main
 */
class MainTest extends TestCase
{
    /**
     * @covers ::__construct
     * Test constructor with FY worker
     */
    public function testConstructorWithFyWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy'
            ]
        ];

        $main = new Main($parameter);
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $worker = $workerProperty->getValue($main);

        $this->assertInstanceOf(FY::class, $worker);
    }

    /**
     * @covers ::__construct
     * Test constructor with unsupported worker
     */
    public function testConstructorWithUnsupportedWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'unsupported'
            ]
        ];

        $this->expectException(Exception::class);
        $this->expectExceptionCode(4030004);
        
        new Main($parameter);
    }

    /**
     * @covers ::handle
     * Test handle method delegates to worker
     */
    public function testHandleDelegatesToWorker()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy'
            ]
        ];

        $mockWorker = Mockery::mock(FY::class);
        $expectedResponse = new JsonResponse(['code' => 0]);
        $mockWorker->shouldReceive('handle')->once()->andReturn($expectedResponse);

        $main = Mockery::mock(Main::class, [$parameter])->makePartial();
        
        $reflection = new ReflectionClass($main);
        $workerProperty = $reflection->getProperty('worker');
        $workerProperty->setAccessible(true);
        $workerProperty->setValue($main, $mockWorker);

        $result = $main->handle();
        
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals($expectedResponse, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
