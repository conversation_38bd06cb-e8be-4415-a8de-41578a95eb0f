<?php

namespace Unit\Models\Logic\Account\ChangeRecord;

use App\Models\Logic\Account\ChangeRecord\FY;
use App\Models\Logic\Account\ChangeRecord\ThirdParty;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * @coversDefaultClass \App\Models\Logic\Account\ChangeRecord\FY
 */
class FYTest extends TestCase
{
    /**
     * @covers ::handle
     * Test successful change record query with data
     */
    public function testHandleSuccessWithData()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 20
        ];

        // Mock FOSSRequest for change record query
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('oil_adapter.org_account.getAccountChangeRecord', [
                'org_code' => 'ORG123',
                'create_start' => '2023-01-01 00:00:00',
                'create_end' => '2023-01-31 23:59:59',
                'page' => 1,
                'page_size' => 20,
            ])
            ->once()
            ->andReturn([
                'data' => [
                    [
                        'id' => 1,
                        'org_code' => 'ORG123',
                        'org_id' => 456,
                        'change_type' => 1,
                        'cash_amount' => '100.00',
                        'fanli_amount' => '20.00',
                        'cash_balance' => '500.00',
                        'fanli_balance' => '80.00',
                        'created_at' => '2023-01-15 10:30:00',
                        'changeAmount' => '120.00',
                        'changeReason' => 'Test transaction',
                        'balanceAfterChange' => '580.00'
                    ]
                ]
            ]);

        $fy = new FY($parameter);
        $result = $fy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals(0, $responseData['status']['code']);
        $this->assertIsArray($responseData['data']);
        $this->assertCount(1, $responseData['data']);

        $recordData = $responseData['data'][0];
        $this->assertEquals('120.00', $recordData['changeAmount']);
        $this->assertEquals('Test transaction', $recordData['changeReason']);
        $this->assertEquals('580.00', $recordData['balanceAfterChange']);
        $this->assertEquals('2023-01-15 10:30:00', $recordData['recordTime']);

        // Verify that sensitive fields are removed
        $this->assertArrayNotHasKey('id', $recordData);
        $this->assertArrayNotHasKey('org_code', $recordData);
        $this->assertArrayNotHasKey('org_id', $recordData);
        $this->assertArrayNotHasKey('change_type', $recordData);
        $this->assertArrayNotHasKey('cash_amount', $recordData);
        $this->assertArrayNotHasKey('fanli_amount', $recordData);
        $this->assertArrayNotHasKey('cash_balance', $recordData);
        $this->assertArrayNotHasKey('fanli_balance', $recordData);
        $this->assertArrayNotHasKey('created_at', $recordData);
    }

    /**
     * @covers ::handle
     * Test change record query with empty data (should return error code 3)
     */
    public function testHandleWithEmptyData()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG456'
            ],
            'startTime' => '2023-02-01 00:00:00',
            'endTime' => '2023-02-28 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 20
        ];

        // Mock FOSSRequest returning empty data
        $mockFOSSRequest = Mockery::mock('alias:Request\FOSS');
        $mockFOSSRequest->shouldReceive('handle')
            ->with('oil_adapter.org_account.getAccountChangeRecord', [
                'org_code' => 'ORG456',
                'create_start' => '2023-02-01 00:00:00',
                'create_end' => '2023-02-28 23:59:59',
                'page' => 1,
                'page_size' => 20,
            ])
            ->once()
            ->andReturn([
                'data' => []
            ]);

        $fy = new FY($parameter);
        $result = $fy->handle();

        $this->assertInstanceOf(JsonResponse::class, $result);

        // Verify the actual response structure for empty data
        $responseData = json_decode($result->getContent(), true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals(3, $responseData['status']['code']); // Error code 3 for empty data
    }

    /**
     * Test that FY extends ThirdParty
     */
    public function testFYExtendsThirdParty()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fy'
            ]
        ];

        $fy = new FY($parameter);
        $this->assertInstanceOf(ThirdParty::class, $fy);
    }

    /**
     * Test constructor inheritance from Base class
     */
    public function testConstructorInheritanceFromBase()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_access_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 20
        ];

        $fy = new FY($parameter);
        
        // Test that Base constructor properties are set
        $reflection = new ReflectionClass($fy);
        
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);
        $data = $dataProperty->getValue($fy);
        $this->assertEquals($parameter, $data);
        
        $accessKeyProperty = $reflection->getProperty('accessKey');
        $accessKeyProperty->setAccessible(true);
        $accessKey = $accessKeyProperty->getValue($fy);
        $this->assertEquals('test_access_key', $accessKey);
        
        $secretProperty = $reflection->getProperty('secret');
        $secretProperty->setAccessible(true);
        $secret = $secretProperty->getValue($fy);
        $this->assertEquals('test_secret', $secret);
        
        $nameAbbreviationProperty = $reflection->getProperty('name_abbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviation = $nameAbbreviationProperty->getValue($fy);
        $this->assertEquals('fy', $nameAbbreviation);
    }

    /**
     * Test handle method return type
     */
    public function testHandleReturnType()
    {
        $parameter = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 20
        ];

        $fy = new FY($parameter);
        
        // Test that handle method exists and has correct signature
        $reflection = new ReflectionClass($fy);
        $handleMethod = $reflection->getMethod('handle');
        
        $this->assertTrue($handleMethod->isPublic());
        $this->assertEquals('handle', $handleMethod->getName());
        
        // Check return type hint
        $returnType = $handleMethod->getReturnType();
        $this->assertNotNull($returnType);
        $this->assertEquals('Illuminate\Http\JsonResponse', $returnType->getName());
    }

    /**
     * Test with missing parameters
     */
    public function testHandleWithMissingParameters()
    {
        $parameter = [
            'auth_data' => [
                'access_key' => 'test_key',
                'secret' => 'test_secret',
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ]
            // Missing startTime, endTime, pageIndex, pageSize
        ];

        $fy = new FY($parameter);
        
        // Test that the object can be created even with missing parameters
        $this->assertInstanceOf(FY::class, $fy);
    }

    /**
     * Test with different pagination parameters
     */
    public function testHandleWithDifferentPaginationParameters()
    {
        // Test with different page sizes
        $parameter1 = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 10
        ];

        $fy1 = new FY($parameter1);
        $this->assertInstanceOf(FY::class, $fy1);

        // Test with different page index
        $parameter2 = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-01-31 23:59:59',
            'pageIndex' => 5,
            'pageSize' => 50
        ];

        $fy2 = new FY($parameter2);
        $this->assertInstanceOf(FY::class, $fy2);
    }

    /**
     * Test with different date ranges
     */
    public function testHandleWithDifferentDateRanges()
    {
        // Test with single day range
        $parameter1 = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-15 00:00:00',
            'endTime' => '2023-01-15 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 20
        ];

        $fy1 = new FY($parameter1);
        $this->assertInstanceOf(FY::class, $fy1);

        // Test with longer date range
        $parameter2 = [
            'auth_data' => [
                'name_abbreviation' => 'fy',
                'role_code' => 'ORG123'
            ],
            'startTime' => '2023-01-01 00:00:00',
            'endTime' => '2023-12-31 23:59:59',
            'pageIndex' => 1,
            'pageSize' => 100
        ];

        $fy2 = new FY($parameter2);
        $this->assertInstanceOf(FY::class, $fy2);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
