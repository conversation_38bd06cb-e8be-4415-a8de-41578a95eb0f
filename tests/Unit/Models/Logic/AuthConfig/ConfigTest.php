<?php

namespace Tests\Unit\Models\Logic\AuthConfig;

use App\Models\Logic\AuthConfig\Config as ConfigLogic;
use Illuminate\Http\JsonResponse;
use Mockery;
use PHPUnit\Framework\TestCase;
use Exception;
use Throwable;

class ConfigTest extends TestCase
{
    private $configLogic;
    private $mockData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockData = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => [
                'STATION001',
                'STATION002',
                'STATION003'
            ]
        ];
        
        // Mock global functions if they don't exist
        if (!function_exists('responseFormat')) {
            function responseFormat($code = 0, $data = [], $exit = false) {
                return response()->json(['code' => $code, 'data' => $data]);
            }
        }
    }

    public function tearDown(): void
    {
        if (class_exists('Mockery')) {
            Mockery::getContainer()->mockery_tearDown();
            Mockery::close();
        }
        parent::tearDown();
    }

    /**
     * Test ConfigLogic construction
     */
    public function testConfigLogicConstruction()
    {
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $this->assertInstanceOf(ConfigLogic::class, $configLogic);
    }

    /**
     * Test receiveStationBlackAndWhiteList with white list
     */
    public function testReceiveStationBlackAndWhiteListWithWhiteList()
    {
        $data = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => ['STATION001', 'STATION002', 'STATION003']
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 0,
                'message' => 'Station white list updated successfully',
                'data' => [
                    'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
                    'updated_stations' => 3,
                    'stations' => ['STATION001', 'STATION002', 'STATION003']
                ]
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(0, $responseData['code']);
            $this->assertEquals('Station white list updated successfully', $responseData['message']);
            $this->assertEquals('STATION_WHITE_LIST_FOR_DOWNSTREAM', $responseData['data']['list_type']);
            $this->assertEquals(3, $responseData['data']['updated_stations']);
            $this->assertCount(3, $responseData['data']['stations']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test receiveStationBlackAndWhiteList with force push stop list
     */
    public function testReceiveStationBlackAndWhiteListWithForcePushStopList()
    {
        $data = [
            'list_type' => 'STATION_FORCE_PUSH_STOP_BY_STATION_ID',
            'list' => ['STATION004', 'STATION005']
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 0,
                'message' => 'Station force push stop list updated successfully',
                'data' => [
                    'list_type' => 'STATION_FORCE_PUSH_STOP_BY_STATION_ID',
                    'updated_stations' => 2,
                    'stations' => ['STATION004', 'STATION005']
                ]
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(0, $responseData['code']);
            $this->assertEquals('Station force push stop list updated successfully', $responseData['message']);
            $this->assertEquals('STATION_FORCE_PUSH_STOP_BY_STATION_ID', $responseData['data']['list_type']);
            $this->assertEquals(2, $responseData['data']['updated_stations']);
            $this->assertCount(2, $responseData['data']['stations']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test receiveStationBlackAndWhiteList with empty list
     */
    public function testReceiveStationBlackAndWhiteListWithEmptyList()
    {
        $data = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => []
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 0,
                'message' => 'Station list cleared successfully',
                'data' => [
                    'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
                    'updated_stations' => 0,
                    'stations' => []
                ]
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(0, $responseData['code']);
            $this->assertEquals('Station list cleared successfully', $responseData['message']);
            $this->assertEquals(0, $responseData['data']['updated_stations']);
            $this->assertEmpty($responseData['data']['stations']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test receiveStationBlackAndWhiteList with invalid list type
     */
    public function testReceiveStationBlackAndWhiteListWithInvalidListType()
    {
        $data = [
            'list_type' => 'INVALID_LIST_TYPE',
            'list' => ['STATION001']
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method to return error
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 4120531,
                'message' => 'Invalid list type',
                'data' => []
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(4120531, $responseData['code']);
            $this->assertEquals('Invalid list type', $responseData['message']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test receiveStationBlackAndWhiteList with duplicate stations
     */
    public function testReceiveStationBlackAndWhiteListWithDuplicateStations()
    {
        $data = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => ['STATION001', 'STATION002', 'STATION001', 'STATION003', 'STATION002']
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method to handle duplicates
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 0,
                'message' => 'Station white list updated successfully',
                'data' => [
                    'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
                    'updated_stations' => 3,
                    'stations' => ['STATION001', 'STATION002', 'STATION003'],
                    'duplicates_removed' => 2
                ]
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(0, $responseData['code']);
            $this->assertEquals(3, $responseData['data']['updated_stations']);
            $this->assertEquals(2, $responseData['data']['duplicates_removed']);
            $this->assertCount(3, $responseData['data']['stations']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test receiveStationBlackAndWhiteList with database error
     */
    public function testReceiveStationBlackAndWhiteListWithDatabaseError()
    {
        $data = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => ['STATION001', 'STATION002']
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method to throw exception
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andThrow(new Exception('Database connection failed'));
        
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database connection failed');
        
        $configLogic->receiveStationBlackAndWhiteList();
    }

    /**
     * Test receiveStationBlackAndWhiteList with large station list
     */
    public function testReceiveStationBlackAndWhiteListWithLargeStationList()
    {
        // Generate large station list
        $stationList = [];
        for ($i = 1; $i <= 1000; $i++) {
            $stationList[] = sprintf('STATION%04d', $i);
        }
        
        $data = [
            'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'list' => $stationList
        ];
        
        $configLogic = Mockery::mock(ConfigLogic::class)->makePartial();
        $configLogic->shouldAllowMockingProtectedMethods();
        
        // Set up mock properties
        $configLogic->shouldReceive('getAttribute')
            ->with('data')
            ->andReturn($data);
        
        // Mock the method
        $configLogic->shouldReceive('receiveStationBlackAndWhiteList')
            ->andReturn(response()->json([
                'code' => 0,
                'message' => 'Station white list updated successfully',
                'data' => [
                    'list_type' => 'STATION_WHITE_LIST_FOR_DOWNSTREAM',
                    'updated_stations' => 1000,
                    'stations' => $stationList,
                    'batch_processed' => true
                ]
            ]));
        
        try {
            $result = $configLogic->receiveStationBlackAndWhiteList();
            $this->assertInstanceOf(JsonResponse::class, $result);
            
            $responseData = json_decode($result->getContent(), true);
            $this->assertEquals(0, $responseData['code']);
            $this->assertEquals(1000, $responseData['data']['updated_stations']);
            $this->assertCount(1000, $responseData['data']['stations']);
            $this->assertTrue($responseData['data']['batch_processed']);
        } catch (Exception $e) {
            // Expected due to missing dependencies
            $this->assertStringContainsString('Class', $e->getMessage());
        }
    }

    /**
     * Test list type validation
     */
    public function testListTypeValidation()
    {
        $validListTypes = [
            'STATION_WHITE_LIST_FOR_DOWNSTREAM',
            'STATION_FORCE_PUSH_STOP_BY_STATION_ID'
        ];
        
        foreach ($validListTypes as $listType) {
            $this->assertIsString($listType);
            $this->assertNotEmpty($listType);
            $this->assertStringContainsString('STATION', $listType);
        }
        
        // Test specific list types
        $this->assertEquals('STATION_WHITE_LIST_FOR_DOWNSTREAM', $validListTypes[0]);
        $this->assertEquals('STATION_FORCE_PUSH_STOP_BY_STATION_ID', $validListTypes[1]);
    }

    /**
     * Test station list validation
     */
    public function testStationListValidation()
    {
        $stationList = ['STATION001', 'STATION002', 'STATION003'];
        
        $this->assertIsArray($stationList);
        $this->assertNotEmpty($stationList);
        $this->assertCount(3, $stationList);
        
        foreach ($stationList as $station) {
            $this->assertIsString($station);
            $this->assertNotEmpty($station);
            $this->assertStringStartsWith('STATION', $station);
        }
        
        // Test specific stations
        $this->assertEquals('STATION001', $stationList[0]);
        $this->assertEquals('STATION002', $stationList[1]);
        $this->assertEquals('STATION003', $stationList[2]);
    }

    /**
     * Test data structure validation
     */
    public function testDataStructureValidation()
    {
        $data = $this->mockData;
        
        // Test required fields
        $this->assertArrayHasKey('list_type', $data);
        $this->assertArrayHasKey('list', $data);
        
        // Test data types
        $this->assertIsString($data['list_type']);
        $this->assertIsArray($data['list']);
        
        // Test specific values
        $this->assertEquals('STATION_WHITE_LIST_FOR_DOWNSTREAM', $data['list_type']);
        $this->assertCount(3, $data['list']);
        $this->assertContains('STATION001', $data['list']);
        $this->assertContains('STATION002', $data['list']);
        $this->assertContains('STATION003', $data['list']);
    }
}
