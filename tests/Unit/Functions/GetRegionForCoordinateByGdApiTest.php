<?php

namespace Unit\Functions;

use TestCase;

class GetRegionForCoordinateByGdApiTest extends TestCase
{
    public function testGetRegionForCoordinateByGdApi()
    {
        $coordinates2 = ['118.17,39.62', '120.19,30.25'];
        $expectedResult2 = [
            '118.17,39.62' => [
                'provinceName' => '河北',
                'cityName'     => '唐山',
                'provinceCode' => '130000000000',
                'cityCode'     => '130200000000',
            ],
            '120.19,30.25' => [
                'provinceName' => '浙江',
                'cityName'     => '杭州',
                'provinceCode' => '330000000000',
                'cityCode'     => '330100000000',
            ],
        ];
        $result2 = getRegionForCoordinateByGdApi($coordinates2);
        $this->assertEquals($expectedResult2, $result2);
    }
}
