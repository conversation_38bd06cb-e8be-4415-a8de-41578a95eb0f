<?php

namespace Unit\Functions;

use TestCase;

class ConvertOtherCoordinateToGcJ02ByGdApiTest extends TestCase
{
    public function testConvertOtherCoordinateToGcJ02ByGdApi()
    {
        // 测试数据
        $coordinates = [
            '116.481499,39.990475',
            '116.481499,39.990375'
        ];
        $expected = [
            '116.481499,39.990475' => [
                'lng' => 116.4748955248,
                'lat' => 39.984717169345,
            ],
            '116.481499,39.990375' => [
                'lng' => 116.474895552445,
                'lat' => 39.984617178361,
            ],
        ];

        // 调用函数进行转换
        $convertedCoordinates = convertOtherCoordinateToGcJ02ByGdApi($coordinates);

        // 断言结果是否正确
        $this->assertEquals($expected, $convertedCoordinates);
    }
}
