<?php

namespace Tests\Unit\Controllers;

use App\Http\Controllers\Api\AccountController;
use App\Models\Logic\Account\Query\Main as AccountQueryLogic;
use App\Models\Logic\Account\Change\Main as AccountChangeLogic;
use App\Models\Logic\Account\Company\Receive\Main as CompanyReceiveLogic;
use App\Models\Logic\Account\ChangeRecord\Main as ChangeRecordMainLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionMethod;

class AccountControllerTest extends TestCase
{
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new AccountController();
    }

    public function tearDown(): void
    {
        // Reset any global state and close all mocks
        if (class_exists('Mockery')) {
            Mockery::getContainer()->mockery_tearDown();
            Mockery::close();
        }
        parent::tearDown();
    }

    /**
     */
    public function testControllerConstruction()
    {
        $controller = new AccountController();
        $this->assertInstanceOf(AccountController::class, $controller);
        $this->assertNotEmpty($controller->customMessages);
    }

    /**
     * Test controller properties
     */
    public function testControllerProperties()
    {
        $controller = new AccountController();

        // Test that controller has required properties
        $reflection = new ReflectionClass($controller);
        $this->assertTrue($reflection->hasProperty('customRules'));
        $this->assertTrue($reflection->hasProperty('customMessages'));
        $this->assertTrue($reflection->hasProperty('requestData'));
        $this->assertTrue($reflection->hasProperty('authData'));
    }

    /**
     * Test validation method
     */
    public function testValidationMethod()
    {
        $controller = new AccountController();
        $this->assertTrue(method_exists($controller, 'validateRequestParam'));
    }

    /**
     * Test query method with valid request
     */
    public function testQueryWithValidRequest()
    {
        $request = $this->createMockRequest('query', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'test'],
            'data' => '{"org_code": "TEST001"}'
        ]);

        $controller = new AccountController();
        
        // Test method exists and is callable
        $this->assertTrue(method_exists($controller, 'query'));
        $this->assertTrue(is_callable([$controller, 'query']));
        
        // Test return type
        $reflection = new ReflectionMethod($controller, 'query');
        $this->assertTrue($reflection->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $reflection->getReturnType()->getName());
    }

    /**
     * Test query method with different platforms
     */
    public function testQueryWithDifferentPlatforms()
    {
        // Test KL platform
        $request = $this->createMockRequest('query_kl', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'kl'],
            'data' => '{"token": "test-token", "money": "100.00"}'
        ]);

        $controller = new AccountController();
        $this->assertTrue(method_exists($controller, 'query'));

        // Test HTX platform
        $request = $this->createMockRequest('query_htx', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'htx'],
            'data' => '{"company_subject_name": "Test Company"}'
        ]);

        $this->assertTrue(method_exists($controller, 'query'));

        // Test YGY platform
        $request = $this->createMockRequest('query_ygy', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'ygy'],
            'data' => '{"companyId": "YGY123"}'
        ]);

        $this->assertTrue(method_exists($controller, 'query'));
    }

    /**
     * Test change method functionality
     */
    public function testChangeMethodFunctionality()
    {
        $request = $this->createMockRequest('change', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'test'],
            'data' => '{"account_no": "123456", "amount": "50.00"}'
        ]);

        $controller = new AccountController();
        
        // Test method exists and is callable
        $this->assertTrue(method_exists($controller, 'change'));
        $this->assertTrue(is_callable([$controller, 'change']));
        
        // Test return type
        $reflection = new ReflectionMethod($controller, 'change');
        $this->assertTrue($reflection->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $reflection->getReturnType()->getName());
    }

    /**
     * Test change method with switch operation
     */
    public function testChangeWithSwitchOperation()
    {
        $request = $this->createMockRequest('switch', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'kl'],
            'data' => '{"account_no": "123456", "card_no": "789012", "token": "switch-token"}'
        ]);

        $controller = new AccountController();
        
        // Test that change method can handle switch operations
        $this->assertTrue(method_exists($controller, 'change'));
        
        // Test validation rules for switch operation
        $this->assertArrayHasKey('switch', $controller->customMessages);
        $this->assertArrayHasKey('account_no.required', $controller->customMessages['switch']);
        $this->assertArrayHasKey('card_no.required', $controller->customMessages['switch']);
        $this->assertArrayHasKey('token.required', $controller->customMessages['switch']);
    }

    /**
     * Test companyReceive method functionality
     */
    public function testCompanyReceiveMethodFunctionality()
    {
        $request = $this->createMockRequest('companyReceive', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'ygy'],
            'data' => '{"companyId": "YGY456", "status": "1", "companyName": "Test Company", "receiptCode": "RC123"}'
        ]);

        $controller = new AccountController();
        
        // Test method exists and is callable
        $this->assertTrue(method_exists($controller, 'companyReceive'));
        $this->assertTrue(is_callable([$controller, 'companyReceive']));
        
        // Test return type
        $reflection = new ReflectionMethod($controller, 'companyReceive');
        $this->assertTrue($reflection->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $reflection->getReturnType()->getName());
    }

    /**
     * Test changeRecord method functionality
     */
    public function testChangeRecordMethodFunctionality()
    {
        $request = $this->createMockRequest('changeRecord', [
            'auth_data' => ['role' => 1, 'name_abbreviation' => 'test'],
            'data' => '{"account_no": "123456", "start_date": "2024-01-01", "end_date": "2024-01-31"}'
        ]);

        $controller = new AccountController();
        
        // Test method exists and is callable
        $this->assertTrue(method_exists($controller, 'changeRecord'));
        $this->assertTrue(is_callable([$controller, 'changeRecord']));
        
        // Test return type
        $reflection = new ReflectionMethod($controller, 'changeRecord');
        $this->assertTrue($reflection->hasReturnType());
        $this->assertEquals('Illuminate\Http\JsonResponse', $reflection->getReturnType()->getName());
    }

    /**
     * Test validation rules structure for all platforms
     */
    public function testValidationRulesStructure()
    {
        $controller = new AccountController();

        // Test that customMessages property exists and contains validation rules
        $this->assertNotEmpty($controller->customMessages);

        // Test query_kl validation rules
        $this->assertArrayHasKey('query_kl', $controller->customMessages);
        $this->assertArrayHasKey('token.required', $controller->customMessages['query_kl']);
        $this->assertEquals(4120421, $controller->customMessages['query_kl']['token.required']);
        $this->assertArrayHasKey('money.required', $controller->customMessages['query_kl']);
        $this->assertEquals(4120009, $controller->customMessages['query_kl']['money.required']);

        // Test query_htx validation rules
        $this->assertArrayHasKey('query_htx', $controller->customMessages);
        $this->assertArrayHasKey('company_subject_name.required', $controller->customMessages['query_htx']);
        $this->assertEquals(4120468, $controller->customMessages['query_htx']['company_subject_name.required']);

        // Test query_ygy validation rules
        $this->assertArrayHasKey('query_ygy', $controller->customMessages);
        $this->assertArrayHasKey('companyId.required', $controller->customMessages['query_ygy']);
        $this->assertEquals(4120496, $controller->customMessages['query_ygy']['companyId.required']);

        // Test companyReceive_ygy validation rules
        $this->assertArrayHasKey('companyReceive_ygy', $controller->customMessages);
        $this->assertArrayHasKey('status.required', $controller->customMessages['companyReceive_ygy']);
        $this->assertEquals(4120498, $controller->customMessages['companyReceive_ygy']['status.required']);

        // Test additional platform validation rules
        $this->assertArrayHasKey('switch', $controller->customMessages);
        $this->assertArrayHasKey('query', $controller->customMessages);

        // Test that all platform methods have validation rules
        $platformMethods = ['query_kl', 'query_htx', 'query_ygy', 'query', 'switch', 'companyReceive_ygy'];
        foreach ($platformMethods as $method) {
            $this->assertArrayHasKey($method, $controller->customMessages);
            $this->assertNotEmpty($controller->customMessages[$method]);
        }
    }

    /**
     * Test detailed validation rules for each platform
     */
    public function testDetailedPlatformValidationRules()
    {
        $controller = new AccountController();

        // Test KL platform detailed rules
        $klRules = $controller->customMessages['query_kl'];
        $this->assertArrayHasKey('token.required', $klRules);
        $this->assertArrayHasKey('money.required', $klRules);
        $this->assertArrayHasKey('money.numeric', $klRules);

        // Test HTX platform detailed rules
        $htxRules = $controller->customMessages['query_htx'];
        $this->assertArrayHasKey('company_subject_name.required', $htxRules);

        // Test YGY platform detailed rules
        $ygyRules = $controller->customMessages['query_ygy'];
        $this->assertArrayHasKey('companyId.required', $ygyRules);
        $this->assertArrayHasKey('companyId.alpha_num', $ygyRules);

        // Test switch operation rules
        $switchRules = $controller->customMessages['switch'];
        $this->assertArrayHasKey('account_no.required', $switchRules);
        $this->assertArrayHasKey('card_no.required', $switchRules);
        $this->assertArrayHasKey('token.required', $switchRules);

        // Test that all error codes are numeric
        foreach ($klRules as $errorCode) {
            $this->assertIsNumeric($errorCode);
        }

        foreach ($htxRules as $errorCode) {
            $this->assertIsNumeric($errorCode);
        }

        foreach ($ygyRules as $errorCode) {
            $this->assertIsNumeric($errorCode);
        }
    }

    /**
     * Test controller inheritance
     */
    public function testControllerInheritance()
    {
        $controller = new AccountController();
        
        // Test that controller extends BasicController
        $this->assertInstanceOf('App\Http\Controllers\Api\BasicController', $controller);
        
        // Test that controller has inherited methods
        $this->assertTrue(method_exists($controller, 'validateRequestParam'));
    }

    /**
     * Test method parameter requirements
     */
    public function testMethodParameterRequirements()
    {
        $controller = new AccountController();
        
        // Test that all main methods require Request parameter
        $methods = ['query', 'change', 'companyReceive', 'changeRecord'];
        
        foreach ($methods as $methodName) {
            $reflection = new ReflectionMethod($controller, $methodName);
            $parameters = $reflection->getParameters();
            
            $this->assertCount(1, $parameters);
            $this->assertEquals('request', $parameters[0]->getName());
            $this->assertEquals('Illuminate\Http\Request', $parameters[0]->getType()->getName());
        }
    }

    /**
     * Test error handling capabilities
     */
    public function testErrorHandlingCapabilities()
    {
        $controller = new AccountController();
        
        // Test that methods can handle empty requests
        $request = $this->createMockRequest('query', []);
        
        $this->assertTrue(method_exists($controller, 'query'));
        
        // Test reflection access to properties
        $reflection = new ReflectionClass($controller);
        $this->assertTrue($reflection->hasProperty('customMessages'));
        
        // Test that customMessages is accessible
        $customMessages = $reflection->getProperty('customMessages');
        $customMessages->setAccessible(true);
        $messages = $customMessages->getValue($controller);
        $this->assertIsArray($messages);
    }

    /**
     * Helper method to create mock request with route information
     */
    private function createMockRequest($actionName, $data = [])
    {
        $request = Mockery::mock(Request::class);
        $route = Mockery::mock(Route::class);
        
        // Mock route to return action name for ACTION_NAME extraction
        $route->shouldReceive('getActionName')
            ->andReturn("App\\Http\\Controllers\\Api\\AccountController@{$actionName}");
        
        $request->shouldReceive('route')->andReturn($route);
        $request->shouldReceive('all')->andReturn($data);
        
        return $request;
    }
}
