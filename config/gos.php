<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/26
 * Time: 9:49
 */

use App\Models\Data\AuthConfig;


return [

    /**
     * 是否开启日志
     */
    'log'         => false,

    /**
     * 日志保存目录
     */
    'log_dir'     => '',

    /**
     * 接口请求地址
     */
    'url'         => AuthConfig::getAuthConfigValByName("GOS_APP_DOMAIN"),

    /**
     * 接口接入账户
     */
    'app_id'      => AuthConfig::getAuthConfigValByName("GOS_APP_ID"),

    /**
     * 参数加密公钥
     */
    'public_key'  => AuthConfig::getAuthConfigValByName("GOS_APP_SECRET"),

    /**
     * 回调地址
     */
    'callbak_url' => 'http://test.gas.chinawayltd.com/gos',

    /**
     * 翻页需要样式
     * eg: ['gos名'=>'业务系统名','per_page'=>'pageSize','pageNo'=>'current_page']
     */
    'page_style'  => ['total' => 'totalCount', 'per_page' => 'pageSize', 'current_page' => 'pageNo', 'data' => 'result']
];
