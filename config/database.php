<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-03-07
 * Time: 18:23
 */

return [

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by <PERSON><PERSON> is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'mysql' => [
            'driver'      => 'mysql',
            'host'        => getLaravelAndMachineEnv("DB__HOST"),
            'port'        => getLaravelAndMachineEnv("DB__PORT"),
            'database'    => getLaravelAndMachineEnv("DB__DATABASE"),
            'username'    => getLaravelAndMachineEnv("DB__USERNAME"),
            'password'    => getLaravelAndMachineEnv("DB__PASSWORD"),
            'unix_socket' => getLaravelAndMachineEnv("DB__SOCKET"),
            'charset'     => 'utf8mb4',
            'collation'   => 'utf8mb4_unicode_ci',
            'prefix'      => 'adapter_',
            'strict'      => false,
            'engine'      => NULL,
        ],
    ],

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => 'predis',

        'cluster' => env('REDIS_CLUSTER', false),

        'options' => [
            'prefix' => getLaravelAndMachineEnv("REDIS__PREFIX", ""),
        ],

        'default' => [
            'host'               => getLaravelAndMachineEnv("REDIS__HOST"),
            'password'           => getLaravelAndMachineEnv("REDIS__PASSWORD"),
            'port'               => getLaravelAndMachineEnv("REDIS__PORT"),
            'database'           => getLaravelAndMachineEnv("REDIS__DATABASE"),
            'read_write_timeout' => -1,
        ],

        'cache' => [
            'host'               => getLaravelAndMachineEnv("REDIS__HOST"),
            'password'           => getLaravelAndMachineEnv("REDIS__PASSWORD"),
            'port'               => getLaravelAndMachineEnv("REDIS__PORT"),
            'database'           => getLaravelAndMachineEnv("REDIS__DATABASE"),
            'read_write_timeout' => -1,
        ],

    ],
];
