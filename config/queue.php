<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-09
 * Time: 16:11
 */
return [

    'default' => getLaravelAndMachineEnv('QUEUE__DRIVER', 'sync'),

    'connections' => [
        'redis'    => [
            'driver'     => 'redis',
            'connection' => 'default',
            'queue'      => 'adapter_queue',
            'expire'     => 2400,
        ],
        'database' => [
            'driver'      => 'database',
            'table'       => getLaravelAndMachineEnv('QUEUE__TABLE', 'jobs'),
            'queue'       => 'default',
            'retry_after' => 2400,
        ],
        'null'     => [
            'driver' => 'null'
        ],
        'sync'     => [
            'driver' => 'sync'
        ],
    ],

    'failed' => [
        'database' => getLaravelAndMachineEnv('DB__CONNECTION', 'mysql'),
        'table'    => getLaravelAndMachineEnv('QUEUE__FAILED__TABLE', 'failed_jobs'),
    ],
];
