{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}, "GosSDK": {"type": "git", "url": "***********************:hyr/GosSDK.git"}, "library/monitor": {"type": "composer", "url": "https://packagelist.chinawayltd.com"}, "satis": {"type": "composer", "url": "http://satis.ews.chinawayltd.com"}}, "require": {"php": ">=7.2", "ext-bcmath": "*", "ext-curl": "*", "ext-json": "*", "ext-mcrypt": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-simplexml": "*", "ext-soap": "*", "barryvdh/laravel-ide-helper": "^2.5", "bluem/tree": "^3.1", "codeitnowin/barcode": "*", "doctrine/dbal": "~2.3", "endroid/qr-code": "^3.8", "evit/php-gm-crypto": "*", "fzaninotto/faker": "~1.4", "gregwar/captcha": "^1.1", "guzzlehttp/guzzle": "^6.3", "hyr/gos-sdk": "^0.0.1", "illuminate/mail": "^5.7", "illuminate/redis": "^5.7", "laravel/lumen-framework": "5.7.*", "league/flysystem": "^1.0", "library/monitor": "^1.23", "mockery/mockery": "~1.0", "pburggraf/crc": "^0.6.1", "php-spec/php-tracing": "^0.2.0", "phpoffice/phpspreadsheet": "^1.11", "phpunit/phpunit": "~7.0", "predis/predis": "^1.1", "vlucas/phpdotenv": "~2.2", "wn/lumen-generators": "^1.3", "ext-ldap": "*"}, "autoload": {"files": [], "classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/", "Framework\\": "library/Framework/", "Tool\\": "library/Tool/", "Request\\": "library/Request/"}}, "autoload-dev": {"classmap": ["tests/"]}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "secure-http": false, "allow-plugins": {"kylekatarnls/update-helper": true}}, "minimum-stability": "dev", "prefer-stable": true}