{"name": "GosSDK", "description": "对接Gos系统之SDK", "type": "library", "license": "private", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "config": {"process-timeout": 1800, "vendor-dir": "vendor", "secure-http": false, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "require": {"guzzlehttp/guzzle": "^6.2"}, "autoload": {"psr-4": {"GosSDK\\": "src"}}}