<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/8/6
 * Time: 10:47
 */

namespace Library\Monitor;
use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\RequestException;
class Request{
    /**
     * 异步json格式数据请求
     * @param $url
     * @param $param
     * @return boolean
     * @throws \Exception
     */
    public static function requestAsyncJson($url, $param)
    {
        $client = new Client();
        $startTime  = microtime(true);
        $host = parse_url($url, PHP_URL_HOST);
        try {
            $promise = $client->requestAsync('POST', $url, ['json' => $param, 'timeout' => 3, 'connect_timeout' => 3]);
            $promise->then(
                function (ResponseInterface $res) use($startTime, $url) {
                    self::requestToFalcon($url,(microtime(true) - $startTime) * 1000, $res->getStatusCode());
                },
                function (RequestException $e) use ($startTime, $url, $host) {
                    \Library\Monitor\Falcon::inc(sprintf("%s,h=%s,t=api_error",$url,$host));
                }
            );
            $promise->wait();
        } catch (\Exception $e) {
            \Library\Monitor\Falcon::inc(sprintf("%s,h=%s,t=api_error",$url, $host));
        }
        return true;
    }

    /**
     * 发送json raw data 请求
     * @param $requestUrl
     * @param $param
     * @return mixed
     */
    public static function requestJson($requestUrl, $param, $headers = [], $timeout = 30, $tries = 0, callable $finishCallback = null)
    {
        if (substr(php_sapi_name(), 0, 3) == 'cli') {

        } else {
            parse_str($_SERVER['QUERY_STRING'], $arrQuery);
            $headers['logid'] = isset($_SERVER['HTTP_LOGID']) ? $_SERVER['HTTP_LOGID'] : (isset($arrQuery['logid']) ? $arrQuery['logid'] : uniqid() . rand(1, 1000));
            $headers['trace'] = isset($_SERVER['HTTP_TRACE']) ? intval($_SERVER['HTTP_TRACE']) + 1 : (isset($arrQuery['trace']) ? intval($arrQuery['trace']) + 1 : 0);
        }
        $httpClient = new Client();
        $startTime  = microtime(true);
        try {
            $i = 0;
            requestJson:
            $req = $httpClient->request('POST', $requestUrl, ['json' => $param, 'headers' => $headers, 'timeout' => $timeout, 'connect_timeout' => 30]);
            //打点falcon中的次数，请求时长，错误
            self::requestToFalcon($requestUrl,(microtime(true) - $startTime)*1000, $req->getStatusCode());
            $result = $req->getBody()->getContents();
            if(is_callable($finishCallback)) {
                call_user_func_array($finishCallback, [$req, $result]);
            }
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            if ($i < $tries) {
                $i++;
                goto requestJson;
            } else {
                if(is_callable($finishCallback)) {
                    call_user_func_array($finishCallback, [$req, $result]);
                }
                throw $e;
            }
        }
        return $result;
    }


    /**
     * 对外接口请求统一方法
     * @param $method 请求方法：POST|GET
     * @param $requestUrl 请求url
     * @param array $param 请求参数，数组
     * @param array $headers 请求的header ['content-type' => 'application/json']
     * @param int $timeout 请求超时间，默认30秒
     * @param int $tries 重试次数，默认0 不重试
     * @return string 返回内容
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function requestData($method, $requestUrl, $param = [] , $headers = [], $timeout = 30, $tries = 0, Callable $finishCallback = null)
    {
        if (substr(php_sapi_name(), 0, 3) == 'cli') {

        } else {
            parse_str($_SERVER['QUERY_STRING'], $arrQuery);
            $headers['logid'] = isset($_SERVER['HTTP_LOGID']) ? $_SERVER['HTTP_LOGID'] : (isset($arrQuery['logid']) ? $arrQuery['logid'] : uniqid() . rand(1, 1000));
            $headers['trace'] = isset($_SERVER['HTTP_TRACE']) ? intval($_SERVER['HTTP_TRACE']) + 1 : (isset($arrQuery['trace']) ? intval($arrQuery['trace']) + 1 : 0);
        }
        $httpClient = new Client();
        $startTime  = microtime(true);
        try {
            $i = 0;
            query:
            if(strtolower($method) == 'post') {
                $buildParam = ['form_params' => $param, 'headers' => $headers, 'timeout' => $timeout, 'connect_timeout' => 30];
            } else {
                $buildParam = ['query' => $param, 'headers' => $headers, 'timeout' => $timeout, 'connect_timeout' => 30];
            }
            $req = $httpClient->request($method, $requestUrl, $buildParam);
            //打点falcon中的次数，请求时长，错误
            self::requestToFalcon($requestUrl,(microtime(true) - $startTime)*1000, $req->getStatusCode());
            $result = $req->getBody()->getContents();
            if(is_callable($finishCallback)) {
                call_user_func_array($finishCallback, [$req, $result]);
            }
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            if ($i < $tries) {
                $i++;
                goto query;
            } else {
                if(is_callable($finishCallback)) {
                    call_user_func_array($finishCallback, [$req, $result]);
                }
                throw $e;
            }
        }

        return $result;

    }

    /**
     * falcon上报
     * @param $url 请求url
     * @param $costMs 耗时时长（毫秒）
     * @param int $httpCode http返回状态码
     * @return boolean
     * @throws \Exception
     */
    public static function requestToFalcon($url, $costMs, $httpCode = 200){
        $url = trim($url);
        $host = parse_url($url, PHP_URL_HOST);
        if(!$host){
            return false;
        }
        try{
            //调用次数
            \Library\Monitor\Falcon::inc(sprintf("%s,h=%s,t=api_count",$url,$host));

            if(200 != $httpCode){
                //打点服务器异常的次数
                \Library\Monitor\Falcon::inc(sprintf("%s,h=%s,t=api_error,c=%d",$url,$host,$httpCode));
            }
            //打点请求时长
            \Library\Monitor\Falcon::cost(sprintf("%s,h=%s,t=api_cost",$url,$host),$costMs);

        }catch (\Exception $e){
            Report::sendToDingding('钉钉调用失败, '. $url );
            return false;
        }
        return true;

    }
}