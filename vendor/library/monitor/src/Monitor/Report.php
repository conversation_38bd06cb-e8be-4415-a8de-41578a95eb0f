<?php

namespace Library\Monitor;
use Library\Monitor\Falcon;
class Report
{
    static $token;

    static $level;

    static $extra;

    static $reportNum = 1;

    //优化显示效果更友好
    public static $errorType = [
        E_ERROR => 'E_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_NOTICE => 'NOTICE',
        E_CORE_WARNING => 'CORE_WARNING',
        E_WARNING => 'WARNING',
        E_PARSE => 'PARSE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_STRICT => 'STRICT',
        E_DEPRECATED => 'DEPRECATED',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_USER_DEPRECATED => 'USER_DEPRECATED',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_NOTICE => 'USER_NOTICE',
        E_USER_WARNING => 'USER_WARNING',];

    //发送报警给钉钉
    public static function sendToDingding($message, $token = null)
    {
        //超过20条会受限，限制发送频率
        if(self::$reportNum > 3) {
            return false;
        }
        self::$reportNum ++;
        //限制发送内容的长度
        $message = mb_substr($message, 0, 1600, 'UTF-8');
        //异步发送消息
        //return Dingding::sendTextMessage($message, $token);
        return Falcon::dd($token, $message);
    }

    public static function getException($exception,  $extra = [])
    {
        $error = [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'message' => "PHP Fatal error:  Uncaught Exception: ".$exception->getMessage() . " in " .
                $exception->getFile() . ":" . $exception->getLine() ."\n Stack trace: " . $exception->getTraceAsString()
        ];
        $text = self::formatError($error, $extra);
        return $text;
    }

    //程序抛异常报警
    public static function sendExceptionToDingding($exception,  $extra = [], $token = null)
    {
        $text = self::getException($exception, $extra);
        return self::sendToDingding($text, $token);
    }

    //程序抛异常报警
    public static function sendExceptionToFeishu($exception,  $extra = [], $token = null)
    {
        $text = self::getException($exception, $extra);
        //限制发送内容的长度
        $text = mb_substr($text, 0, 1600, 'UTF-8');
        return Falcon::feishu($token, $text);
    }


    //程序抛异常报警
    public static function sendErrorToDingding($error, $extra = [], $token = null)
    {
        $text = self::formatError($error, $extra);
        return self::sendToDingding($text, $token);
    }

    //程序抛异常报警
    public static function sendErrorToFeishu($error, $extra = [], $token = null)
    {
        $text = self::formatError($error, $extra);
        return Falcon::feishu($text, $token);
    }

    //当前地址
    public static function currentUrl()
    {
        $pageURL = 'http';

        if (isset($_SERVER["HTTPS"]) && $_SERVER['HTTPS'] == "on") {
            $pageURL .= "s";
        }
        $pageURL .= "://";
        if ($_SERVER["SERVER_PORT"] != "80") {
            $pageURL .= $_SERVER["SERVER_NAME"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
        } else {
            $pageURL .= $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"];
        }
        return $pageURL;
    }


    //是否是cli模式运行
    public static function is_cli_mode()
    {
        $sapi_type = php_sapi_name();
        if (isset($sapi_type) && substr($sapi_type, 0, 3) == 'cli') {
            return true;
        } else {
            return false;
        }
    }

    //格式化错误信息
    public  static function formatError($error, $extra)
    {
        $text = "";
        if ($error) {
            if ($extra && is_array($extra)) {
                foreach ($extra as $k => $v) {
                    $text .= $k . "：" . $v . "\n";
                }
            }
            if (self::is_cli_mode() == false) {
                $text .= "请求方式：" . $_SERVER['REQUEST_METHOD'] . "\n";
                $text .= "URL：" . self::currentUrl() . "\n";
                $text .= "参数：" . json_encode($_REQUEST, JSON_UNESCAPED_UNICODE) . "\n";
                Falcon::inc("exception,t=".self::currentUrl());
            }
            $text .= "文件：" . $error['file'] . "\n";
            $text .= "行号：" . $error['line'] . "\n";
            $text .= "时间：" . date('Y-m-d H:i:s') . "\n";
            if(isset($error['level'])) {
                if(isset(self::$errorType[$error['level']])) {
                    $text .= self::$errorType[$error['level']] .": ";
                }
            }
            $text .=  $error['message'] . "\n";
            return $text;
        }
        return '';
    }

    //致命错误捕获
    public static  function registerShutdownHandler()
    {
        $error = error_get_last();
        if ($error && self::isFatal($error['type'])) {
            $content = self::formatError($error, self::$extra);
            self::sendToDingding($content, self::$token);
        }
        return !self::getDisplayErrorSet();
    }

    public static function isFatal($type)
    {
        return in_array($type, [E_COMPILE_ERROR, E_CORE_ERROR, E_ERROR, E_PARSE]);
    }

    //注册绑定
    public  static function registMonitor($extra = [], $token, $level = E_NOTICE)
    {
        $self = new self();
        $self::$token = $token;
        $self::$level = $level;
        $self::$extra = $extra;
        set_error_handler([$self, 'handleError']);
        set_exception_handler([$self, 'handleException']);
        register_shutdown_function([$self, 'registerShutdownHandler'], $extra, $token);
    }

    //throw级别捕获
    public static function handleException($e)
    {
        self::sendExceptionToDingding($e, self::$extra, self::$token);
        return !self::getDisplayErrorSet();

    }

    //notice级别捕获
    public  static function handleError($level, $message, $file = '', $line = 0, $context = [])
    {
        if(self::$level & $level) {
            $error = [
                'file' => $file,
                'line' => $line,
                'level' => $level,
                'file' => $file,
                'message' => $message
            ];
            self::sendErrorToDingding($error, self::$extra, self::$token);
        }
        return !self::getDisplayErrorSet();
    }

    //页面是否显示错误
    public static function getDisplayErrorSet()
    {
        return in_array(strtolower(ini_get('display_errors')),  ['on' , 1]);
    }

}
