<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/8/6
 * Time: 10:43
 */

namespace Library\Monitor;
class DingDing
{
    const DINGDING_URL = 'https://oapi.dingtalk.com/robot/send';

    const  DINGDING_TOKEN = '677c8e4110a24008c2f9538d25e6f2f17d9c262715c8e1cae5e56b8245be160d';

    public static function sendMarkdownMessage($title, $message, $token = null)
    {

        if($token == null)
            $token = self::DINGDING_TOKEN;

        $param = [
            'msgtype' => 'markdown',
            'markdown' => [
                'title'  => $title,
                'text'   =>  $message
            ]
        ];
        $url = self::DINGDING_URL . '?access_token=' . $token;
        return Request::requestJson($url, $param);
    }

    public static function sendTextMessage($message, $token = null)
    {

        if($token == null)
            $token = self::DINGDING_TOKEN;

        $param = [
            'msgtype' => 'text',
            'text' => [
                'content' => $message
            ]
        ];
        $url = self::DINGDING_URL . '?access_token=' . $token;
        return Request::requestJson($url, $param);
    }

}