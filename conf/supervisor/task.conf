[program:monitor]
command = /data/web/monitor/monitor/monitor
user = nginx
autostart = true
startretries = 1
startsecs = 1
priority = 1
[program:tailLogToConsole]
process_name = %(program_name)s_%(process_num)02d
command = tail -F -q /data/web/storage/logs/*.log
autostart = true
autorestart = true
startretries = 3
priority = 99
user = nginx
numprocs = 1
redirect_stderr = true
[program:jaegerAgent]
command = /data/web/jaeger/jaeger-agent --reporter.grpc.host-port=%(ENV_JAEGER_AGENT_GRPC_HOST_POST)s
user = nginx
autostart = true
startretries = 1
startsecs = 1
priority = 1

