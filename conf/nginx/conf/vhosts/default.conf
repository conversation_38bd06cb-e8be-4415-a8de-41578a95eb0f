server {
    listen 80 default;
    server_name _;

    access_log  /data/log/nginx/access.log  main;
    error_log  /data/log/nginx/error.log  error;

    root /data/web/public;
    index  index.html index.htm index.php;

    location / {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        root   /data/web/public;
        index  index.html index.htm index.php;
        try_files $uri $uri/ /index.php?$args;
    }

    location /adapter {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';

            if ($request_method = 'OPTIONS') {
                return 204;
            }

            proxy_set_header    Host    $host;
            rewrite ^/adapter/(.*)$ /$1 break;
            proxy_pass  http://127.0.0.1/;
    }

    location /open_api/station {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';

            if ($request_method = 'OPTIONS') {
                return 204;
            }

            proxy_set_header    Host    $host;
            rewrite ^/open_api/station/(.*)$ /$1 break;
            proxy_pass  http://127.0.0.1/;
    }

    location /assets/ {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';

            if ($request_method = 'OPTIONS') {
                return 204;
            }

            alias   /data/web/resources/assets/;
            index  index.html index.htm index.php;
        }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php-fpm.sock;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }

    location ~ ^/fpm-ping {
            include fastcgi_params;
            fastcgi_pass unix:/var/run/php-fpm.sock;
            fastcgi_param SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            access_log off;
            allow 127.0.0.1;
            deny all;
        }

        location ~ ^/fpm-status {
            include fastcgi_params;
            fastcgi_pass unix:/var/run/php-fpm.sock;
            fastcgi_param SCRIPT_FILENAME  $document_root$fastcgi_script_name;
            access_log off;
            allow 127.0.0.1;
            deny all;
        }
}
