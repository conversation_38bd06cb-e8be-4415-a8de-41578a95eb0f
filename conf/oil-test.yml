PHPENV: 'prod'
APPLICATION__NAME: oil-adapter
APPLICATION__DEBUG: true

DB__CONNECTION: mysql
DB__HOST: ***********
DB__PORT: 3306
DB__DATABASE: oil_adapter
DB__USERNAME: cat
DB__PASSWORD: cathyr

APP__NAME: oil-adapter
APP__ENV: local
APP__KEY: base64:OCHJi6Gpj+tf72lo73pulyDc+HRDZXrnMRXZkW1E6FI=
APP__DEBUG: true
APP__LOG__LEVEL: debug
APP__URL: '//test.gas.chinawayltd.com/partner'

BROADCAST__DRIVER: log
CACHE__DRIVER: redis
SESSION__DRIVER: redis
SESSION__LIFETIME: 120
QUEUE_CONNECTION: redis
QUEUE__DRIVER: redis

CAT__AGENT__TYPE: Socket
CAT__AGENT__HOST: host-cat
CAT__AGENT__PORT: 2280

REDIS__HOST: redis
REDIS__PASSWORD: cae0f7fcf1
REDIS__PORT: 6379

MAIL__DRIVER: smtp
MAIL__HOST: smtp.g7ltd.com.cn
MAIL__PORT: 587
MAIL__USERNAME: <EMAIL>
MAIL__PASSWORD: wuyu321..
MAIL__ENCRYPTION: tls
MAIL__FROM__NAME: 油品对接
MAIL__FROM__ADDRESS: <EMAIL>

CRONTAB__ENABLE: 'off'
QUEUE__ENABLE: 'off'
SUPERVISOR__ENABLE: 'on'

RUN__MODE: remote

RUN_SCRIPTS: '1'
SKIP_COMPOSER: 'on'

RUN__ENVIRONMENT: 'test'
