[program:callback_push_result_queue]
process_name = %(program_name)s_%(process_num)02d
command = /usr/local/bin/php /data/web/artisan queue:work redis --daemon --quiet --queue=callback_push_result_queue --timeout=120 --delay=3 --sleep=3 --tries=3
autostart = true
autorestart = true
startretries = 3
priority = 99
user = nginx
numprocs = 2
redirect_stderr = true
stdout_logfile=/data/web_log/callback_push_result_queue.log
