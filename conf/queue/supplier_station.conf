[program:supplier_station_queue]
process_name = %(program_name)s_%(process_num)02d
command = /usr/local/bin/php /data/web/artisan queue:work redis --daemon --quiet --queue=supplier_station_queue --timeout=120 --delay=3 --sleep=3 --tries=3
autostart = true
autorestart = true
startretries = 3
priority = 99
user = nginx
numprocs = 10
redirect_stderr = true
stdout_logfile=/data/web_log/supplier_station_queue.log
