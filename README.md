# 更新日志

## 2017-06-06
- 增加接口必填字段校验规则，规则在Defines/Fields/*.php中。

## 2017-05-25 15:35
 - <strong><red>重要</red></strong>，简化及优化使用方式，请见示例：
 <pre>
 $data = (new \GosSDK\Gos())
     ->setMethod(GosSDK\Defines\Methods\CardVice::CREATE)//设定接口
     ->setParams([])//设定参数
     ->async();//async为队列模式，sync为同步调用
 </pre>
 - 增加了接口可接收字段清单，目录：src/Defines/Fields。
 - 支持sdk的config配置自定义路径，为了支持一键发布及版本管理，增加
GOS_SDK_CONFIG定义，值为配置文件路径。
<br/>

# Gos系统SDK

本sdk是为了<strong>简化</strong>及<strong>标准化</strong>对GOS调用而提供的支持。

## 安全性
post的Data参数采用<strong>AES 256</strong>加解密，请求方使用public_key加密，GOS系统采用对应的key解密。

## 所需依赖
本SDK遵循PHP的namespace使用方式，同时需要依赖以下1个包： 
<pre>
"guzzlehttp/guzzle": "^6.2"
</pre>

请使用composer包管理工具进行安装
<pre>
composer require guzzlehttp/guzzle -vvv
</pre>

附：点击查看 -> [如何安装composer](http://docs.phpcomposer.com/00-intro.html)

然后将GosSDK文件夹放置于系统的任意目录（如：lib或library），在<strong>composer.json</strong>中对psr-4进行命名空间设置：
<pre>
{
  "require": {
    "guzzlehttp/guzzle": "^6.2"
  },
  "autoload": {
    "psr-4": {
      "GosSDK\\": "放置的目录/GosSDK/src"
    }
  }
}
</pre>


## 如何接入GOS
请在GOS系统注册接入系统及对应App，GOS将分配对应的app_id和public_key，请将其配置到<strong>Config/api.php</strong>中

## 配置
SDK配置文件在Config/api.php中，为了支持自定义配置路径，可以将sdk中的配置文件api.php模板复制到项目中集中的配置目录（名称可自定义），然后增加常量定义：GOS_SDK_CONFIG，指定自定义配置文件路径。
<pre>
return [

    /**
     * 是否开启日志
     */
    'log' => TRUE,

    /**
    *  日志保存目录
    */
    'log_dir'   => '',

    /**
     * 接口请求地址
     */
    'url'       => '',

    /**
     * 接口接入账户
     */
    'app_id'    => '',

    /**
     * 参数加密公钥
     */
    'public_key' => '',
    
    /**
     * 回调地址
     */
    'callbak_url' => '',
    
    /**
     * 翻页需要样式
     * eg: ['gos名'=>'业务系统名','per_page'=>'pageSize','pageNo'=>'current_page']
     */
    'page_style'  => [
        'total'        => 'total',
        'per_page'     => 'per_page',
        'current_page' => 'current_page',
        'data'         => 'data'
    ]

];
</pre>

## 声明
SDK中的Defines目录中对
- 接口名（下发队列，等）
- 数据类型(卡片|分配单|充值单，等)
- 请求方式（POST|GET）
- 操作方式（创建|修改|删除）

均做了对应的定义，请使用定义好的名称，如：
<pre>
//操作的数据对象为副卡（卡片）
GosSDK\Defines\DataType::CARD_VICE
</pre>

## 接口支持字段清单
SDK的Defines/Fields目录中有所有数据对象的字段说明，如：
<pre>
[
    'info'   => [
        'name'    => 'CardVice',
        'comment' => '副卡信息'
    ],
    'type'   => 'mongo',
    'fields' => [
        'id'                  => '主键ID',
        'sys_id'              => '系统ID',
        'third_id'            => '业务系统pk',
        'vice_no'             => '副卡号',
        'password'            => '卡密码',
        'oil_com'             => '油卡商（油卡类型）（1:中石化，2:中石油,3:中车油4:上海盛海5:G7预存卡6:G7资金卡）',
        '_oil_com'            => '油卡商（油卡类型）（1:中石化，2:中石油,3:中车油4:上海盛海5:G7预存卡6:G7资金卡）',
        'card_from'           => '卡来源（10汇通卡 20中交卡 30撬装卡 40托管卡 50盛海卡）',
        '_card_from'          => '卡来源',
        ...
    ],
    'casts'  => [
        'id'                  => 'string',
        'third_id'            => 'string',
        'sys_id'              => 'string',
        'vice_no'             => 'string',
        'vice_password'       => 'string',
        'oil_com'             => 'string',
        '_oil_com'            => 'string',
        'card_from'           => 'string',
        ...
    ]
];
</pre>

## 分页查询返回字段映射配置
为了更好的支持分页查询返回格式，可以在配置文件中对分页查询返回字段名做映射配置，如：
<pre>
/**
     * 翻页需要样式
     * eg: ['gos名'=>'业务系统名','per_page'=>'pageSize','pageNo'=>'current_page']
     */
    'page_style'  => [
        'total'        => 'total',
        'per_page'     => 'per_page',
        'current_page' => 'current_page',
        'data'         => 'data'
    ]
</pre>

## 示例
示例文件：[Example.php](./Example.php)

<pre>
/**
 * 请确认入口文件中有此行。
 * 如果项目中已使用composer，则无需在入口文件中再次引用此行，否则请添加此行
 */
require_once './vendor/autoload.php';

$data = (new \GosSDK\Gos())
     ->setMethod(GosSDK\Defines\Methods\CardVice::CREATE)//设定接口
     ->setParams([])//设定参数
     ->async();//async为队列模式，sync为同步调用
</pre>