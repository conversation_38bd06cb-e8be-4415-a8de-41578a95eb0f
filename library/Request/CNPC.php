<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class CNPC extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $params
     * @param int $timeOut
     * @param bool $isAuth
     * @return array|mixed
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $params = null, int $timeOut = 120, bool $isAuth = true)
    {
        $domain = AuthConfigData::getAuthConfigValByName("CNPC_APP_DOMAIN");
        $appKey = AuthConfigData::getAuthConfigValByName("CNPC_APP_KEY");
        $token = 'token';
        if ($isAuth) {

            $redis = app('redis');
            $token = $redis->get("cnpc_token");
            if (empty($token)) {

                $responseData = self::handle('/open/json/open_auth/getAccessToken', [], $timeOut,
                    false);
                $redis->setex("cnpc_token", 7100, $responseData);
                $token = $responseData;
            }
        }

        $data = self::sign([
            'token' => $token
        ]);
        $data['jsondata'] = empty($params) ? self::encrypt(json_encode([]), $appKey) : self::encrypt(
            json_encode($params), $appKey);
        Log::handle("Request cnpc started", "河北中石油", $method, [
            'requestData'      => $data,
            'requestDataEntry' => $params,
        ], "", "info");
        $result = self::curl("$domain$method", json_encode($data), $timeOut, [
            "Content-Type:application/json",
        ], 'post', true);
        $responseHeaders = parseResponseHeader($result['responseHeaderInfo']);
        if (!isset($responseHeaders['Content-Type']) or strpos($responseHeaders['Content-Type'], 'json')
            !== false) {

            $realData = self::decrypt($result['data'] ?? '', $appKey);
            if (strpos($method, 'getAccessToken') === false) {

                $realData = json_decode($realData, true);
            }
            Log::handle("Request cnpc finished", "河北中石油", $method, [
                'requestData'      => $data,
                'requestDataEntry' => $params,
            ], ['responseRawData' => $result, 'parsedResponseData' => $realData], "info");
            if (!array_has($result, ['status', 'data'])) {
                throw new Exception('河北中石油接口输出数据格式错误', 5000999);
            }
            if ($result['status'] != 0) {

                throw new Exception($result['info'] ?? '', 5000666);
            }
        } else {

            if (strpos($responseHeaders['Content-Type'], 'text') !== false) {

                $realData = array_merge($responseHeaders, [
                    'data' => $result['responseRawData']
                ]);
                Log::handle("Request cnpc finished", "河北中石油", $method, [
                    'requestData'      => $data,
                    'requestDataEntry' => $params,
                ], ['responseRawData' => $result, 'parsedResponseData' => $realData], "info");
            } else {

                $realData = array_merge($responseHeaders, [
                    'data' => base64_encode($result['responseRawData'])
                ]);
            }
        }
        return $realData;
    }

    public static function sign(array $params = []): array
    {
        $result = [
            'appNo'     => AuthConfigData::getAuthConfigValByName("CNPC_APP_ID"),
            'token'     => $params['token'] ?? '',
            'timestamp' => $params['timestamp'] ?? strval(time() * 1000),
            'nonce'     => $params['nonce'] ?? uniqid(),
            'appKey'    => AuthConfigData::getAuthConfigValByName("CNPC_APP_KEY"),
        ];
        $tmp = [];
        foreach ($result as $k => $v) {

            $tmp[] = $k . '=' . $v;
        }
        $content = implode($tmp, '&');
        $result['sign'] = md5($content);
        unset($result['appKey']);
        return $result;
    }

    public static function encrypt($input, $key): string
    {
        return base64_encode(openssl_encrypt($input, "DES-EDE3", $key, OPENSSL_PKCS1_PADDING));
    }

    public static function decrypt($encrypted, $key): string
    {
        try {

            return openssl_decrypt(base64_decode($encrypted), 'DES-EDE3', $key, OPENSSL_PKCS1_PADDING);
        } catch (Throwable $throwable) {
            return '';
        }
    }
}
