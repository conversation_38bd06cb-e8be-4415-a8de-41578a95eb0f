<?php


namespace Request;

use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use SoapClient;
use Throwable;
use Tool\Alarm\FeiShu;

class Base
{
    /**
     * 基础请求方法
     * @param string $url
     * @param array|object|null $data
     * @param int $timeout
     * @param array $header
     * @param string $requestMethod
     * @param bool $jsonRequest
     * @param int $jsonRule
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-04-26 17:34
     */
    public static function curl(
        string $url = '',
        $data = null,
        int $timeout = 10,
        array $header = [],
        string $requestMethod = 'post',
        bool $jsonRequest = false,
        int $jsonRule = 512
    ): array {
        $ch = curl_init();

        switch ($requestMethod) {
            case "get":

                $data = http_build_query($data);
                $url = $url . "?" . $data;
                break;
            default:

                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($requestMethod));
                if ($jsonRequest and is_array($data)) {
                    $data = json_encode($data, $jsonRule);
                }
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }

        //如果json请求那么增加json头
        if ($jsonRequest) {
            if (empty(
            array_filter($header, function ($value) {
                return strpos(strtolower($value), 'content-type') !== false;
            })
            )) {
                $header[] = 'Content-Type: application/json;charset=utf-8';
            }
        }


        if (false !== strpos($url, 'https://')) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);  // 跳过证书检查
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);  // 从证书中检查SSL加密算法是否存在
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);

        if ($header) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }

        $output = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $requestHeaderInfo = curl_getinfo($ch, CURLINFO_HEADER_OUT);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
        $appConnectTime = curl_getinfo($ch, CURLINFO_APPCONNECT_TIME);
        $preTransferTime = curl_getinfo($ch, CURLINFO_PRETRANSFER_TIME);
        $startTransferTime = curl_getinfo($ch, CURLINFO_STARTTRANSFER_TIME);
        $redirectTime = curl_getinfo($ch, CURLINFO_REDIRECT_TIME);
        $nameLookUpTime = curl_getinfo($ch, CURLINFO_NAMELOOKUP_TIME);
        $responseHeaderInfo = substr($output, 0, $headerSize);
        $responseBody = strlen($output) > $headerSize ? substr($output, $headerSize) : $output;
        $curlError = curl_error($ch);
        curl_close($ch);

        $returnData = [
            'responseHttpCode'   => $httpCode,
            'responseHeaderInfo' => $responseHeaderInfo,
            'requestHeaderInfo'  => $requestHeaderInfo,
            'curlError'          => $curlError,
            'totalTime'          => $totalTime,
            'connectTime'        => $connectTime,
            'appConnectTime'     => $appConnectTime,
            'preTransferTime'    => $preTransferTime,
            'startTransferTime'  => $startTransferTime,
            'redirectTime'       => $redirectTime,
            'nameLookUpTime'     => $nameLookUpTime,
        ];
        if ($totalTime > 5 and !app()->runningInConsole()) {
            try {
                $classNameArr = explode("\\", get_called_class());
                $className = count($classNameArr) > 1 ? $classNameArr[1] : $classNameArr[0];
                $platformName = DockingPlatformInfoData::getPlatformNameByNameAbbreviation(strtolower($className));
                global $routeId;
                (new FeiShu())->outServiceInterfaceInvalid([
                    'platform_name'      => empty($platformName) ? $className : $platformName,
                    'reason'             => '耗时异常',
                    'request_url'        => $url,
                    'request_total_time' => $totalTime,
                    'route_id'           => $routeId ?? '',
                    'request_error'      => $curlError,
                ]);
            } catch (Throwable $throwable) {}
        }
        if ($responseData = json_decode($responseBody, true)) {
            if (is_array($responseData)) {
                return $responseData + $returnData;
            } else {
                $returnData['responseRawData'] = $responseBody;
            }
        } else {
            $returnData['responseRawData'] = $responseBody;
        }

        return $returnData;
    }

    /**
     * @param mixed $wsdl
     * @param array $options
     * @return SoapClient
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-05-16 15:23
     */
    public static function wsdl($wsdl = null, array $options = []): SoapClient
    {
        return new SoapClient($wsdl, $options);
    }
}
