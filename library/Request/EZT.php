<?php


namespace Request;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;
use Tool\DesToJava;

class EZT extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return mixed|array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-05-21 14:14
     */
    public static function handle(string $method = '', array $data = [])
    {
        $secret = AuthConfigData::getAuthConfigValByName("EZT_APP_SECRET");
        $appKey = AuthConfigData::getAuthConfigValByName("EZT_APP_KEY");
        $appId = AuthConfigData::getAuthConfigValByName("EZT_APP_ID");
        $domain = AuthConfigData::getAuthConfigValByName("EZT_APP_DOMAIN");
        $realUrl = "$domain$method";
        $headers = [
            'Content-Type: multipart/form-data',
        ];
        $data['appId'] = $appId;
        $encData = json_encode([
            'main' => $data
        ]);
        $requestParams = [
            'appId' => $appId,
            'param' => DesToJava::encrypt($encData, $secret, 'DES-CBC', $appKey),
        ];
        Log::handle(
            "Request ezt started",
            DockingPlatformInfoData::getFieldsByNameAbbreviation(
                "ezt",
                'platform_name',
                ''
            )['platform_name'],
            $method,
            [
            "requestParams"     => $requestParams,
            "realUrl"           => $realUrl,
            "realRequestParams" => $data,
            ],
            "",
            "info"
        );
        $responseData = self::curl($realUrl, $requestParams, 10, $headers);
        $realResponseData = json_decode(
            DesToJava::decrypt($responseData['responseRawData'], $secret, 'DES-CBC', $appKey),
            true
        );
        Log::handle(
            "Request ezt finished",
            DockingPlatformInfoData::getFieldsByNameAbbreviation(
                "ezt",
                'platform_name',
                ''
            )['platform_name'],
            $method,
            [
            "requestParams"     => $requestParams,
            "realUrl"           => $realUrl,
            "realRequestParams" => $data,
        ], [
            'realResponseData' => $realResponseData,
            'rawResponseData'  => $responseData
        ],
            "info"
        );

        if (!array_has($realResponseData, ["status", "msg"])) {
            throw new Exception("E站途接口输出数据格式错误", 5000999);
        }

        if ($realResponseData["status"] != 200) {
            throw new Exception($realResponseData['msg'] ?? '', 5000999);
        }

        return $realResponseData;
    }
}
