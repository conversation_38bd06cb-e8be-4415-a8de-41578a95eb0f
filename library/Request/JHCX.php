<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class JHCX extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320)
    {
        Log::handle("Request jhcx started", "湖北中石油", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['appid'] = AuthConfigData::getAuthConfigValByName("JHCX_APP_KEY");
        $sign = self::sign($data, AuthConfigData::getAuthConfigValByName("JHCX_APP_SECRET"));
        $data['sign'] = $sign;
        $apiUrl = AuthConfigData::getAuthConfigValByName('JHCX_APP_DOMAIN');
        $result = self::curl("$apiUrl$method", $data, $timeOut, [], 'post', true);
        Log::handle("Request jhcx finished", "湖北中石油", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['result'])) {

            throw new Exception($result['error'] ?? '', 5000999);
        }
        if ($result['result'] != 1) {

            throw new Exception($result['error'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret, $jsonRule = 320): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $key => $val) {

            if ($key != '' && !is_null($val)) {

                if (is_object($val) or is_array($val)) {

                    $val = json_encode($val, $jsonRule);
                }

                $signStrArr[] = $key . '=' . $val;
            }
        }
        $signStrArr[] = "key=$secret";
        return strtoupper(md5(implode('&', $signStrArr)));
    }
}
