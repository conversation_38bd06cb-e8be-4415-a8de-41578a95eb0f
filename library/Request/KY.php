<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class KY extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param ?array $data
     * @param int $timeOut
     * @param bool $isAuth
     * @param string $path
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = "",
        array $data = null,
        int $timeOut = 120,
        bool $isAuth = true,
        string $path = "router/rest"
    ): array {
        Log::handle("Request ky started", "跨越", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName("KY_APP_DOMAIN");
        $timestamp = getMillisecond();
        $headers = [
            "Content-Type:application/json",
        ];
        $token = '';
        if ($isAuth) {
            $headers[] = "appkey:" . AuthConfig::getAuthConfigValByName("KY_APP_KEY");
            $headers[] = "sign:" . self::sign($timestamp, $data);
            $headers[] = "method:" . $method;
            $headers[] = "timestamp:$timestamp";
            $token = app('redis')->get("ky_token");
            if (!$token) {
                $responseData = self::handle('security.token', [
                    'appkey'    => AuthConfig::getAuthConfigValByName("KY_APP_KEY"),
                    'appsecret' => AuthConfig::getAuthConfigValByName("KY_APP_SECRET"),
                ], $timeOut, false, 'security/token');
                app('redis')->setex(
                    "ky_token",
                    $responseData['data']['expire_time'],
                    $responseData['data']['token']
                );
                $token = $responseData['data']['token'];
            }
            $headers[] = "token:$token";
        }

        $result = self::curl(
            "$domain$path",
            $data,
            $timeOut,
            $headers,
            'post',
            true
        );
        Log::handle("Request ky finished", "跨越", $method, [
            'requestData' => $data,
            'token'       => $token,
        ], $result, "info");
        if (!array_has($result, ['success', 'code', 'data'])) {
            throw new Exception('跨越接口输出数据格式错误', 5000999);
        }
        if (!$result['success'] or $result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000666);
        }
        if ($isAuth and (!$result['data']['success'] or $result['data']['code'] != 0)) {
            throw new Exception($result['data']['msg'] ?? '', 5000666);
        }
        return $result;
    }

    public static function sign(int $timestamp, array $data = []): string
    {
        $secret = AuthConfig::getAuthConfigValByName("KY_APP_SECRET");
        return strtoupper(md5($secret . $timestamp . json_encode($data)));
    }
}
