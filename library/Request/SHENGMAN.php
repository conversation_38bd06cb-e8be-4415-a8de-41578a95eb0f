<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;

class SHENGMAN extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @param string $nameAbbreviation
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $apiUrl = '';
        $headers = [
            "Content-Type: application/json",
            'Expect:',
        ];
        if ($method == 'station') {
            $timestamp = getMillisecond();
            $apiUrl = AuthConfigData::getAuthConfigValByName(
                    "SHENGMAN_STATION_APP_URL"
                ) . '?' . http_build_query([
                    "timestamp" => $timestamp,
                    "topic" => AuthConfigData::getAuthConfigValByName("SHENGMAN_STATION_APP_TOPIC"),
                ]);
            $appId = AuthConfigData::getAuthConfigValByName("SHENGMAN_STATION_APP_ID");
            $appSecret = AuthConfigData::getAuthConfigValByName("SHENGMAN_STATION_APP_SECRET");
            $headers[] = "appId:$appId";
            $headers[] = "signType:2";
            if (checkIsAssocArray($data)) {
                ksort($data);
            } else {
                array_walk($data, function (&$v) {
                    if (is_array($v)) {
                        ksort($v);
                    }
                });
            }
            $headers[] = "sign:" . sha1($appId . $appSecret . $timestamp . json_encode($data, 320));
        }
        if ($method == 'trade') {
            $apiUrl = AuthConfigData::getAuthConfigValByName("SHENGMAN_TRADE_APP_URL");
            $appId = AuthConfigData::getAuthConfigValByName("SHENGMAN_TRADE_APP_ID");
            $appSecret = AuthConfigData::getAuthConfigValByName("SHENGMAN_TRADE_APP_SECRET");
            $cmd = $data['cmd'];
            unset($data['cmd']);
            if (checkIsAssocArray($data['data'])) {
                ksort($data['data']);
            } else {
                array_walk($data['data'], function (&$v) {
                    if (is_array($v)) {
                        ksort($v);
                    }
                });
            }
            $data['source'] = $appId;
            $data['ticket'] = Uuid::uuid();
            $data['secret'] = md5($appId . $appSecret . $cmd . json_encode($data['data'], 320));
            $data['cmd'] = $cmd;
        }
        $platformName = DockingPlatformInfoData::getPlatformNameByNameAbbreviation('shengman');
        Log::handle("Request shengman started", $platformName, $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl($apiUrl, $data, 20, $headers, "post", true, 320);
        Log::handle("Request shengman finished", $platformName, $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception("{$platformName}接口输出数据格式错误", 5000999);
        }
        if ($method == 'station' and $result['code'] != 2000000000) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($method == 'trade' and $result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }
}