<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;

class ZWL extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @param string $requestMethod
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = [], string $requestMethod = 'post'): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("ZWL_APP_DOMAIN");
        $data = array_merge(self::sign($data), $data);
        Log::handle("Request zwl started", "中物流", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $data,
            20,
            strtolower($requestMethod) == 'post' ? [
                "Content-Type: application/json",
                'Expect:',
            ] : [
                'Expect:'
            ],
            $requestMethod,
            !strtolower($requestMethod == 'get'),
            320
        );
        Log::handle("Request zwl finished", "中物流", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['error_code'])) {
            throw new Exception('中物流接口输出数据格式错误', 5000999);
        }
        if ($result['error_code'] != 0) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $signData = []): array
    {
        $signData['nonce'] = $signData['nonce'] ?? md5(Uuid::uuid());
        $signData['accesskey_id'] = AuthConfigData::getAuthConfigValByName('ZWL_APP_KEY');
        $signData['timestamp'] = $signData['timestamp'] ?? time();
        ksort($signData);
        $signArr = [];
        foreach ($signData as $k => $v) {
            if (empty($v)) {
                continue;
            }
            $signArr[] = $k . "=" . $v;
        }
        $signArr[] = "key=" . AuthConfigData::getAuthConfigValByName('ZWL_APP_SECRET');
        $signData['signature'] = strtoupper(
            md5(
                implode('&', $signArr)
            )
        );
        return $signData;
    }
}
