<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;
use Throwable;

class DT extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array|mixed
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 320)
    {
        $domain = AuthConfigData::getAuthConfigValByName('DT_APP_DOMAIN');
        $appId = AuthConfigData::getAuthConfigValByName('DT_APP_KEY');
        $appSecret = AuthConfigData::getAuthConfigValByName('DT_APP_SECRET');
        $appRsaPrivateKey = AuthConfigData::getAuthConfigValByName('G7_APP_RSA_PRIVATE_KEY');
        $requestData = [
            'appId'                   => $appId,
            'appSecret'               => $appSecret,
            'timestamp'               => date("Y-m-d H:i:s"),
            'businessData'            => self::encryptData($appSecret, empty($data) ? '{}' : json_encode(
                $data, $jsonRule)),
            'version'                 => 'V2',
            'businessDataEncryptType' => '3DES',
            'digestType'              => 'SHA256',
            'signType'                => 'RSA',
            'requestId'               => Uuid::uuid(),
        ];
        $requestData['sign'] = self::sign($appRsaPrivateKey, $requestData);
        unset($requestData['appSecret']);
        Log::handle("Request dt started", "DT", $method, [
            'requestData' => $requestData,
            'initData' => $data,
        ], "", "info");
        $result = self::curl("$domain$method", $requestData, $timeOut, [
            "Content-Type: application/json",
        ], 'post', true, $jsonRule);
        Log::handle("Request dt finished", "DT", $method, [
            'requestData' => $requestData,
            'initData' => $data,
        ], [
            'result' => $result,
        ], "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('DT接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {

            throw new Exception($result['message'] ?? '', 5000999);
        }
        $realData = json_decode(self::decryptData($appSecret, $result['data']), true);
        Log::handle("Request dt finished", "DT", $method, [
            'requestData' => $requestData,
            'initData' => $data,
        ], [
            'parseData' => $realData,
        ], "info");
        return $realData;
    }

    public static function encryptData(string $appSecret, string $data): string
    {
        $saltAppSecret = md5($appSecret . $appSecret);
        $desKey = mb_substr($saltAppSecret, 0, 24);
        $desIv = mb_substr($saltAppSecret, 0, 8);
        return base64_encode(openssl_encrypt($data, "DES-EDE3-CBC", $desKey,
            OPENSSL_PKCS1_PADDING, $desIv));
    }

    public static function decryptData(string $appSecret, string $data)
    {
        $saltAppSecret = md5($appSecret . $appSecret);
        $desKey = mb_substr($saltAppSecret, 0, 24);
        $desIv = mb_substr($saltAppSecret, 0, 8);
        return openssl_decrypt(base64_decode($data), "DES-EDE3-CBC", $desKey,
            OPENSSL_PKCS1_PADDING, $desIv);
    }

    public static function sign(string $rsaKey, array $data): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $k => $v) {

            $signStrArr[] = "$k=$v";
        }
        $signStr = implode("&", $signStrArr);
        $signStr = hash("sha256", $signStr);
        $privateKeyResource = openssl_get_privatekey($rsaKey);
        $keyLen = openssl_pkey_get_details($privateKeyResource)['bits'];
        $sign = '';
        $partLen = $keyLen / 8 - 11;
        $parts = str_split($signStr, $partLen);
        foreach ($parts as $part) {

            $encryptedTemp = '';
            openssl_private_encrypt($part, $encryptedTemp, $rsaKey);
            $sign .= $encryptedTemp;
        }
        return base64_encode($sign);
    }
}
