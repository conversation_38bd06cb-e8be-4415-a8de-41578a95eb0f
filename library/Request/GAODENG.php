<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class GAODENG extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @param int $jsonRule
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = [], int $jsonRule = 320): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("GAODENG_APP_DOMAIN");
        $data['enterpriseNo'] = AuthConfigData::getAuthConfigValByName("GAODENG_ENTERPRISE_NO");
        $requestData = [
            'reqData' => base64_encode(json_encode($data, $jsonRule)),
        ];
        $requestData['timeStamp'] = getMillisecond();
        $requestData['merchantNo'] = AuthConfigData::getAuthConfigValByName("GAODENG_MERCHANT_NO");
        $requestData['version'] = AuthConfigData::getAuthConfigValByName("GAODENG_APP_VERSION");
        $requestData['requestSerial'] = $requestData['timeStamp'];
        $requestData['sign'] = self::sign($requestData, $jsonRule);
        Log::handle("Request GAODENG started", "高灯", $method, [
            "realRequestParams" => $data,
            "requestParams"     => $requestData,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $requestData,
            20,
            [],
            "post",
            true,
            320
        );
        Log::handle("Request GAODENG finished", "高灯", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['respCode', 'bizCode'])) {
            throw new Exception('高灯接口输出数据格式错误', 5000999);
        }
        if ($result['respCode'] != '00000') {
            throw new Exception(($result['respMsg'] ?? '') . ($result['bizMsg'] ?? ''), 5000999);
        }
        if ($result['bizCode'] != 'SUCCESS') {
            throw new Exception($result['bizMsg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $data = [], int $jsonRule = 320): string
    {
        openssl_sign(
            self::getSignData($data, $jsonRule),
            $sign,
            transJavaRsaKeyToPhpOpenSSL(
                AuthConfigData::getAuthConfigValByName(
                    "GAODENG_RSA_PRIVATE_KEY"
                ),
                false
            ),
            OPENSSL_ALGO_MD5
        );
        return base64_encode($sign);
    }

    public static function getSignData(array $data = [], int $jsonRule = 320): string
    {
        $signData = [];
        ksort($data);
        foreach ($data as $k => $v) {
            if (empty($k) or empty($v)) {
                continue;
            }
            if (is_array($v) or is_object($v)) {
                $signData[] = $k . "=" . json_encode($v, $jsonRule);
                continue;
            }
            $signData[] = $k . "=" . $v;
        }
        $signData[] = 'key=' . AuthConfigData::getAuthConfigValByName("GAODENG_APP_KEY");
        return implode("&", $signData);
    }

    public static function verifySign(array $data = [], int $jsonRule = 320): bool
    {
        $sign = $data['sign'];
        unset($data['sign']);
        return openssl_verify(
            self::getSignData($data, $jsonRule),
            base64_decode($sign),
            transJavaRsaKeyToPhpOpenSSL(AuthConfigData::getAuthConfigValByName("GAODENG_RSA_PUBLIC_KEY")),
            OPENSSL_ALGO_MD5
        );
    }
}
