<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class GS extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 60): array
    {
        Log::handle("Request sc started", "港森", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['access_key'] = AuthConfigData::getAuthConfigValByName('GS_APP_KEY');
        $data['token'] = self::getToken();
        $data['agreement_code'] = AuthConfigData::getAuthConfigValByName('GS_APP_MERCHANT');
        $data['sign'] = self::sign($data);
        $apiUrl = AuthConfigData::getAuthConfigValByName('GS_APP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, $timeOut);
        Log::handle("Request sc finished", "港森", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['success', 'errorcode'])) {

            Log::handle("Request gs is failed", "港森", $method, [
                'requestData' => $data,
            ], $result, "error");
            throw new Exception('港森接口输出数据格式错误', 5000999);
        } else {
            if ($result['errorcode'] != '0' or !$result['success']) {

                Log::handle("Request gs is failed", "", $method, [
                    'requestData' => $data,
                ], $result, "error");
                if (in_array($result['errorcode'], [10027, 10028])) {

                    throw new Exception($result['errormsg'] ?? '', $result['errorcode']);
                }
                throw new Exception($result['errormsg'] ?? '', 5000999);
            }
        }

        return $result;
    }

    /**
     * @return mixed|string
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/27 2:22 下午
     */
    public static function getToken()
    {
        $apiTokenCacheKey = "GS_API_TOKEN";
        $token = app('redis')->get($apiTokenCacheKey);
        if (!empty($token)) {

            return $token;
        }

        $data['access_key'] = AuthConfigData::getAuthConfigValByName('GS_APP_KEY');
        $data['api_secret'] = AuthConfigData::getAuthConfigValByName('GS_APP_SECRET');
        $data['api_appid'] = AuthConfigData::getAuthConfigValByName('GS_APP_ID');
        $data['sign'] = self::sign($data);
        $method = 'Get_Token/createToken';
        $apiUrl = AuthConfigData::getAuthConfigValByName('GS_APP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, 60);
        Log::handle("Request sc finished", "港森", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!isset($result['token']) or empty($result['token']) or !isset($result['expire_time']) or
            empty($result['expire_time'])) {

            throw new Exception($result['errormsg'] ?? '', 5000999);
        }
        if ($result['expire_time'] - 1 > 0) {

            app('redis')->setex($apiTokenCacheKey, $result['expire_time'], $result['token']);
        }

        return $result['token'];
    }

    public static function sign(array $data): string
    {
        if (isset($data['access_key'])) {

            unset($data['access_key']);
        }

        $params = implode('', $data);
        $accessKey = AuthConfigData::getAuthConfigValByName('GS_APP_KEY');
        $signKey = AuthConfigData::getAuthConfigValByName('GS_APP_SIGN_KEY');
        return md5($accessKey . '_' . $params . '_' . $signKey);
    }
}
