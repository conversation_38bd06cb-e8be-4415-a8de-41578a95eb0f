<?php

namespace Request;

use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class CIEC extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2022/2/7 11:36 AM
     */
    public static function handle(string $method = "", array $data = []): array
    {
        $requestData['data'] = $data;
        $requestData['timestamp'] = date("Y-m-d H:i:s");
        $requestData['app_key'] = AuthConfig::getAuthConfigValByName("CIEC_APP_KEY");
        $domain = AuthConfig::getAuthConfigValByName("CIEC_APP_DOMAIN");
        $requestData['sign'] = self::sign($requestData);
        Log::handle("Request ciec started", "中国国际能源", $method, [
            'requestData' => $requestData,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $requestData,
            10,
            [],
            'post',
            true,
            320
        );
        Log::handle("Request ciec finished", "中国国际能源", $method, [
            'requestData' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('中国国际能源接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array &$data): string
    {
        $secret = AuthConfig::getAuthConfigValByName("CIEC_APP_SECRET");
        $signArr = [$secret];
        ksort($data);
        foreach ($data as $k => &$v) {
            if (empty($v)) {
                $signArr[] = "$k";
                continue;
            }
            if (is_array($v) or is_object($v)) {
                $v = json_encode($v, 320);
                $signArr[] = "$k$v";
                continue;
            }
            $signArr[] = "$k$v";
        }
        $signArr[] = $secret;
        return strtoupper(md5(implode('', $signArr)));
    }
}
