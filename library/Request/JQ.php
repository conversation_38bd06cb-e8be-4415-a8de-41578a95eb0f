<?php
// 鲸启

namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class JQ extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $requestMethod = null, $data = null, int $timeOut = 60)
    {
        Log::handle("Request jq started", "鲸启", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['timestamp'] = getMillisecond();
        $data['appKey'] = AuthConfigData::getAuthConfigValByName("JQ_APP_KEY");
        $data['sign'] = self::sign($data, AuthConfigData::getAuthConfigValByName("JQ_APP_SECRET"));
        $apiDomain = AuthConfigData::getAuthConfigValByName('JQ_APP_DOMAIN');
        $result = self::curl(
            "$apiDomain$method",
            $data,
            $timeOut,
            [],
            $requestMethod,
            !(strtolower($requestMethod) == 'get'),
            320
        );
        Log::handle("Request jq finished", "鲸启", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('鲸启接口输出数据格式错误.', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret): string
    {
        ksort($data);
        $signStrArr = [
            $secret,
        ];
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val) && $val != '') {
                if (is_array($val) or is_object($val)) {
                    $val = php2JavaArrayValuesToString($val);
                }
                $signStrArr[] = "$key$val";
            }
        }
        $signStrArr[] = "$secret";
        return md5(implode('', $signStrArr));
    }
}
