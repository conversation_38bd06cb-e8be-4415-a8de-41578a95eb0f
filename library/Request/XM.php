<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class XM extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        $data['timestamp'] = time();
        $data = array_merge($data, self::sign($data['timestamp']));
        Log::handle("Request xm started", "小萌", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfigData::getAuthConfigValByName("XM_APP_DOMAIN");
        $result = self::curl("$domain$method", $data, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);
        Log::handle("Request xm finished", "小萌", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['message'], 5000999);
        }
        return $result;
    }

    private static function sign(int $timestamp): array
    {
        $appKey = AuthConfigData::getAuthConfigValByName("XM_APP_KEY");
        $appSecret = AuthConfigData::getAuthConfigValByName("XM_APP_SECRET");
        return [
            'appId' => $appKey,
            'sign'  => strtoupper(md5($appKey . $timestamp . $appSecret)),
        ];
    }
}
