<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class YDT extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320)
    {
        Log::handle("Request ydt started", "一点通", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['timestamp'] = time();
        $signData = self::sign($data);
        $headers = [];
        foreach ($signData as $k => $v) {
            $headers[] = "$k:$v";
        }
        $apiUrl = AuthConfigData::getAuthConfigValByName('YDT_APP_DOMAIN');
        $result = self::curl("$apiUrl$method", $data, $timeOut, $headers, 'post', true);
        Log::handle("Request ydt finished", "一点通", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code', 'success'])) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 0 or !$result['success']) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $params): array
    {
        //对请求参数按照键名进行升序排序
        ksort($params);
        //过滤值为空的参数
        $data = [];
        array_walk($params, function ($v, $k) use (&$data) {
            if (!empty($v)) {
                $data[$k] = $v;
            }
        });
        //将请求参数用&拼接
        $dataStr = http_build_query($data, '', '&', PHP_QUERY_RFC3986);
        $appKey = AuthConfigData::getAuthConfigValByName("YDT_APP_KEY");
        $appSecret = AuthConfigData::getAuthConfigValByName("YDT_APP_SECRET");
        //实用sha256算法生成加密字符串，并转为大写
        return [
            'appkey'    => $appKey,
            'signature' => strtoupper(hash_hmac('sha256', $dataStr . $appKey . $appSecret, $appSecret))
        ];
    }
}
