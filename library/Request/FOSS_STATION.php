<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class FOSS_STATION extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 512): array
    {
        Log::handle("Request foss_station started", "G7能源油站端", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName('FOSS_STATION_APP_DOMAIN');
        $result = self::curl(
            "$domain$method",
            $data,
            $timeOut,
            array_merge([
                'Content-Type: application/json',
            ], getTraceInjectHeaders(true)),
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request foss_station finished", "G7能源油站端", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 0) {
            if (in_array($result['code'], config('error.CODE_PERSIST.FOSS_STATION'))) {
                throw new Exception($result['msg'], $result['code']);
            }
            throw new Exception($result['msg'] ?? '', 5000666);
        }

        return $result;
    }
}
