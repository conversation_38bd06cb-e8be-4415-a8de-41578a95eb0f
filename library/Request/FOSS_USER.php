<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class FOSS_USER extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $headers
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle(
        $method = null,
        $headers = null,
        $data = null,
        int $timeOut = 60,
        int $jsonRule = 512
    ): array
    {
        Log::handle("Request foss_user started", "1号卡用户端", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName('FOSS_USER_APP_DOMAIN');
        $result = self::curl(
            "$domain$method",
            $data,
            $timeOut,
            array_merge($headers ?? [], getTraceInjectHeaders(true)),
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request foss_user finished", "1号卡用户端", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000666);
        }

        return $result;
    }
}
