<?php


namespace Request;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use stdClass;
use Throwable;
use Tool\Des3ToZsh;

class ZSH extends Base implements ThirdParty
{
    private static $requestParamKeyMapping = [
        'GetCouponTypeList'   => 'requestXML',
        'CouponPurchase'      => 'requestXML',
        'OrderQuery'          => 'requestXML',
        'PrePayBalanceQuery'  => 'requestJson',
        'SubMchQuery'         => 'requestJson',
        'CancelEOCoupon'      => 'requestJson',
        'GetPrefAmount'       => 'requestJson',
        'GetCouponTypeListV2' => 'requestXML',
        'CouponQuery'         => 'requestXML',
    ];
    private static $responseParamKeyMapping = [
        'GetCouponTypeList'   => 'GetCouponTypeListResult',
        'CouponPurchase'      => 'CouponPurchaseResult',
        'OrderQuery'          => 'OrderQueryResult',
        'PrePayBalanceQuery'  => 'PrePayBalanceQueryResult',
        'SubMchQuery'         => 'SubMchQueryResult',
        'CancelEOCoupon'      => 'CancelEOCouponResult',
        'GetPrefAmount'       => 'GetPrefAmountResult',
        'GetCouponTypeListV2' => 'GetCouponTypeListV2Result',
        'CouponQuery'         => 'CouponQueryResult',
    ];

    /**
     * @param string $method
     * @param array $data
     * @return mixed|array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-05-21 14:14
     */
    public static function handle(string $method = '', array $data = [])
    {
        if (!isset(self::$requestParamKeyMapping[$method]) or !isset(self::$responseParamKeyMapping[$method])) {
            throw new Exception("广东中石化未提供该方法");
        }

        $wsdl = AuthConfigData::getAuthConfigValByName("ZSH_DOMAIN");
        $publicKey = AuthConfigData::getAuthConfigValByName("ZSH_PUBLIC_KEY");
        $customerCode = AuthConfigData::getAuthConfigValByName("ZSH_PARTNER_ID");
        $secret = AuthConfigData::getAuthConfigValByName("ZSH_SECRET");
        $data['CustomerCode'] = $customerCode;
        $requestData = str_replace("\n", "", createXmlData("req", $data));
        $dynamicKey = getUniqueStringFor16();
        $pu_key = openssl_pkey_get_public($publicKey);
        openssl_public_encrypt($dynamicKey, $encrypted, $pu_key);
        $signKey = base64_encode($encrypted);
        $des3 = new Des3ToZsh();
        $firstEncData = $des3->encrypt($requestData, $secret);
        $lastEncData = base64_encode($des3->encrypt(base64_encode($firstEncData), $dynamicKey));
        $realRequestData = json_encode([
            'SignKey'      => $signKey,
            'CustomerCode' => $customerCode,
            'Data'         => $lastEncData
        ], 64);
        Log::handle("Request zsh finished.This is unParsed", "广东中石化", $method, [
            'requestData' => [
                self::$requestParamKeyMapping[$method] => $realRequestData,
            ],
            'data'        => $data,
        ], '', "error");
        $soap = self::wsdl("$wsdl?wsdl", ['trace' => true]);
        $responseData = get_object_vars(
            $soap->$method([
                self::$requestParamKeyMapping[$method] => $realRequestData,
            ])
        );

        Log::handle("Request zsh finished.This is unParsed", "广东中石化", $method, [
            'requestData' => [
                self::$requestParamKeyMapping[$method] => $realRequestData,
            ],
        ], $responseData, "error");

        if (!$result = json_decode($responseData[self::$responseParamKeyMapping[$method]], true)) {
            return new stdClass();
        }

        try {
            $privateKey = AuthConfigData::getAuthConfigValByName("ZSH_G7_PRIVATE_KEY");
            $pi_key = openssl_pkey_get_private($privateKey);
            openssl_private_decrypt(base64_decode($result['SignKey']), $zshDynamicDesKey, $pi_key);
            $firstDecData = $des3->decrypt($result['Data'], $zshDynamicDesKey);
            $lastDecData = $des3->decrypt($firstDecData, $secret);
            Log::handle("Request zsh finished.This is parse", "广东中石化", $method, [
                'requestData' => [
                    self::$requestParamKeyMapping[$method] => $realRequestData,
                ],
            ], $lastDecData, "info");
            $responseData = json_decode(json_encode(simplexml_load_string($lastDecData)), true);

            if ($responseData['RetCode'] != '00') {
                throw new Exception("请求广东中石化失败");
            }

            return $responseData;
        } catch (Throwable $exception) {
            Log::handle("Request zsh has exception", "广东中石化", $method, [
                'requestData' => [
                    self::$requestParamKeyMapping[$method] => $realRequestData,
                ],
                'exception'   => $exception,
            ], $responseData, "error");
            throw $exception;
        }
    }
}