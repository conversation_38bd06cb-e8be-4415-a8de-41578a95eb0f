<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class YUNDATONG extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 336, $isAuth = true)
    {
        Log::handle("Request yundatong started", "运达通", $method, [
            'requestData' => $data,
        ], "", "info");
        $requestData['data'] = base64_encode(
            openssl_encrypt(
                json_encode($data, 336),
                'AES-128-ECB',
                self::getAesKey(),
                OPENSSL_RAW_DATA
            )
        );
        $requestData['appKey'] = AuthConfigData::getAuthConfigValByName("YUNDATONG_APP_KEY");
        $requestData['appSecret'] = AuthConfigData::getAuthConfigValByName("YUNDATONG_APP_SECRET");
        $requestData['timestamp'] = time();
        $requestData['sign'] = self::sign($requestData);
        unset($requestData['appSecret']);
        $requestHeaders = [];
        $apiUrl = AuthConfigData::getAuthConfigValByName('YUNDATONG_APP_DOMAIN');
        if ($isAuth) {
            $redis = app('redis');
            $token = $redis->get("yundatong_token");
            if (empty($token)) {
                $responseData = self::handle(
                    'open/api/customer/auth/token',
                    [],
                    $timeOut,
                    true,
                    false
                );
                $redis->setex("yundatong_token", 86340, $responseData['data']['token']);
                $token = $responseData['data']['token'];
            }
            $requestHeaders[] = "Authorization:$token";
        }
        $result = self::curl("$apiUrl$method", $requestData, $timeOut, $requestHeaders, 'post', true);
        Log::handle("Request yundatong finished", "运达通", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('运达通接口输出数据格式异常', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $data, $jsonRule = 320): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val)) {
                if (is_object($val) or is_array($val)) {
                    $val = json_encode($val, $jsonRule);
                }

                $signStrArr[] = $key . '=' . $val;
            }
        }
        return strtoupper(md5(implode('&', $signStrArr)));
    }

    public static function getAesKey(): string
    {
        return substr(
            sha1(AuthConfigData::getAuthConfigValByName("YUNDATONG_APP_SECRET"), true),
            0,
            16
        );
    }
}
