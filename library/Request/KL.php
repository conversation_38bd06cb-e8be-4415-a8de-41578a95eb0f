<?php


namespace Request;

use App\Models\Data\AuthConfig;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;


class KL extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param string $requestType
     * @param array $headers
     * @param int $timeOut
     * @param bool $isAuth
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        bool $isAuth = true,
        string $requestType = 'post',
        array $headers = [],
        int $timeOut = 120
    ): array {
        Log::handle("Request kl started", "昆仑", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName("KL_APP_DOMAIN");

        if ($isAuth) {
            $token = app('redis')->get("kl_token");

            if (!$token) {
                $responseData = self::handle('token/', [
                    'email'    => AuthConfigData::getAuthConfigValByName("KL_ADMIN_NAME"),
                    'password' => AuthConfigData::getAuthConfigValByName("KL_ADMIN_PASSWORD"),
                ], false, 'post', [], $timeOut);
                app('redis')->setex("kl_token", 604600, $responseData['token']);
                $token = $responseData['token'];
            }

            $headers[] = "Authorization: JWT $token";
        }

        $result = self::curl("$domain$method", $data, $timeOut, $headers, $requestType);
        Log::handle("Request kl finished", "昆仑", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (array_has($result, ['responseRawData'])) {
            throw new Exception('昆仑接口输出数据格式错误', 5000999);
        }
        return $result;
    }
}
