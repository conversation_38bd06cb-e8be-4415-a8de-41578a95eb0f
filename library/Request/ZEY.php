<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZEY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @param bool $isAuth
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        int $timeOut = 120,
        int $jsonRule = 320,
        bool $isAuth = true
    ): array {
        Log::handle("Request zey started", "则一", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName("ZEY_APP_DOMAIN");
        $headers = [
            "Content-Type: application/json;charset=utf-8",
        ];
        $token = '';

        if ($isAuth) {
            $token = app('redis')->get("zey_token");

            if (!$token) {
                $responseData = self::handle('login', [
                    'name' => AuthConfig::getAuthConfigValByName("ZEY_APP_KEY"),
                    'password' => AuthConfig::getAuthConfigValByName("ZEY_APP_SECRET")
                ], $timeOut, $jsonRule, false);
                app('redis')->setex("zey_token", 540, $responseData['result']);
                $token = $responseData['result'];
            }

            $headers[] = "Authorization: Bearer $token";
        }

        $result = self::curl(
            "$domain$method",
            $data,
            $timeOut,
            $headers,
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request zey finished", "则一", $method, [
            'requestData' => $data,
            'token'       => $token,
        ], $result, "info");

        if (!array_has($result, ['success', 'businessException'])) {
            throw new Exception('则一接口输出数据格式错误', 5000999);
        }

        if ($result['businessException'] or !$result['success'] or (isset($result['result']['code'])
                                                                    and $result['result']['code'] != 0)) {
            throw new Exception($result['result']['message'] ?? '', 5000666);
        }

        return $result;
    }
}
