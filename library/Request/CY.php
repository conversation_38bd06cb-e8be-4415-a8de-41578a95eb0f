<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class CY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @param bool $isAuth
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        int $timeOut = 120,
        int $jsonRule = 320,
        bool $isAuth = true
    ): array
    {
        $domain = AuthConfig::getAuthConfigValByName("CY_APP_DOMAIN");
        $appKey = AuthConfig::getAuthConfigValByName("CY_APP_KEY");
        $appSecret = AuthConfig::getAuthConfigValByName("CY_APP_SECRET");
        $appAesKey = AuthConfig::getAuthConfigValByName("CY_APP_AES_KEY");
        $appAesIv = AuthConfig::getAuthConfigValByName("CY_APP_AES_IV");
        $headers = [
            "Content-Type: application/json;charset=utf-8",
        ];
        $requestData = self::sign($appKey, $appSecret, $appAesKey, $appAesIv, $data ?? [], $jsonRule);
        Log::handle("Request cy started", "畅油", $method, [
            'requestData'     => $data,
            'requestRealData' => $requestData,
        ], "", "info");
        if ($isAuth) {

            $token = app('redis')->get("cy_token");
            if (!$token) {

                $responseData = self::handle('/thirdApi/auth/token', [], $timeOut, $jsonRule,
                    false);
                app('redis')->setex("cy_token", 2591998, $responseData['data']['token']);
                $token = $responseData['data']['token'];
            }
            $headers[] = "ttoken: $token";
        }

        $result = self::curl("$domain$method", $requestData, $timeOut, $headers, 'post',
            true, $jsonRule);
        Log::handle("Request cy finished", "畅油", $method, [
            'requestData'     => $data,
            'requestRealData' => $requestData,
        ], $result, "info");

        if (!array_has($result, ['errno'])) {
            throw new Exception('畅油接口输出数据格式错误', 5000999);
        }

        if ($result['errno'] != 0) {

            throw new Exception($result['errmsg'] ?? '', 5000666);
        }

        return $result;
    }

    public static function sign(string $appKey, string $appSecret, string $key, string $iv, array $data = [], int $jsonRule = 320): array
    {
        $data = base64_encode(openssl_encrypt(empty($data) ? '' : json_encode($data, $jsonRule), 'AES-128-CBC',
            $key, OPENSSL_RAW_DATA, $iv));
        $timestamp = time();
        return [
            'appKey'    => $appKey,
            'data'      => $data,
            'sign'      => strtoupper(md5("appKey=$appKey&appSecret=$appSecret&data=$data&timestamp=$timestamp")),
            'timestamp' => $timestamp,
        ];
    }
}
