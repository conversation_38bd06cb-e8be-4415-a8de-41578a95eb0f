<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class HBKJ extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 336)
    {
        Log::handle("Request hbkj started", "湖北中石油(跨界)", $method, [
            'requestRealData' => $data,
        ], "", "info");
        $appId = AuthConfigData::getAuthConfigValByName('HBKJ_APP_ID');
        $timestamp = time();
        $nonce = mt_rand(100000, 999999);
        $encryptData = self::encrypt(
            json_encode($data, $jsonRule),
            $appId
        );
        $requestData = [
            'appId'     => $appId,
            'timestamp' => $timestamp,
            'nonce'     => $nonce,
            'sign'      => self::getSHA1(
                $appId,
                $timestamp,
                $nonce,
                $encryptData
            ),
            'jsonData'  => $encryptData
        ];
        $apiUrl = AuthConfigData::getAuthConfigValByName('HBKJ_APP_DOMAIN');
        $result = self::curl("$apiUrl$method", $requestData, $timeOut, [], 'post', true);
        Log::handle("Request hbkj finished", "湖北中石油(跨界)", $method, [
            'requestRealData' => $data,
            'requestData'     => $requestData,
        ], ['responseRawData' => $result], "info");
        if (!array_has($result, ['resultCode'])) {
            throw new Exception('湖北中石油(跨界)接口输出数据格式错误.', 5000999);
        }
        if ($result['resultCode'] != '0000') {
            throw new Exception($result['errMsg'] ?? '', 5000999);
        }
        $realData = json_decode(self::decrypt($result['jsonResult']), true) ?? [];
        Log::handle("Request hbkj finished", "湖北中石油(跨界)", $method, [
            'requestRealData' => $data,
            'requestData'     => $requestData,
        ], ['responseRawData' => $result, 'parsedResponseData' => $realData], "info");
        return $realData;
    }

    protected static function getSHA1($appId, $timestamp, $nonce, $encrypt_msg): string
    {
        $array = func_get_args();
        sort($array, SORT_STRING);
        return sha1(implode($array));
    }

    protected static function getRandomStr(): string
    {
        $str = "";
        $str_pol = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
        $max = strlen($str_pol) - 1;
        for ($i = 0; $i < 16; $i++) {
            $str .= $str_pol[mt_rand(0, $max)];
        }
        return $str;
    }

    protected static function encode($text): string
    {
        $amount_to_pad = 32 - (strlen($text) % 32);
        if ($amount_to_pad == 0) {
            $amount_to_pad = 32;
        }
        $pad_chr = chr($amount_to_pad);
        $tmp = str_repeat($pad_chr, $amount_to_pad);
        return $text . $tmp;
    }

    protected static function decode($text): string
    {
        $pad = ord(substr($text, -1));
        if ($pad < 1 || $pad > 32) {
            $pad = 0;
        }
        return substr($text, 0, (strlen($text) - $pad));
    }

    protected static function encrypt($text, $appid): string
    {
        //获得16位随机字符串，填充到明文之前
        $random = self::getRandomStr();
        $text = $random . pack("N", strlen($text)) . $text . $appid;
        $key = base64_decode(AuthConfigData::getAuthConfigValByName('HBKJ_AES_SECRET') . '=');
        $iv = substr(
            $key,
            0,
            16
        );
        //使用自定义的填充方式对明文进行补位填充
        $text = self::encode($text);
        //使用BASE64对加密后的字符串进行编码
        return base64_encode(
            openssl_encrypt(
                $text,
                'aes-256-cbc',
                $key,
                OPENSSL_NO_PADDING,
                $iv
            )
        );
    }

    protected static function decrypt($encrypted): string
    {
        //使用BASE64对需要解密的字符串进行解码
        $ciphertext_dec = base64_decode($encrypted);
        $key = base64_decode(
            AuthConfigData::getAuthConfigValByName('HBKJ_AES_SECRET') . '='
        );
        $iv = substr(
            $key,
            0,
            16
        );
        //解密
        $decrypted = openssl_decrypt(
            $ciphertext_dec,
            'aes-256-cbc',
            $key,
            OPENSSL_NO_PADDING,
            $iv
        );
        //去除补位字符
        $result = self::decode($decrypted); //去除16位随机字符串,网络字节序和AppId
        if (strlen($result) < 16) {
            return "";
        }
        $content = substr($result, 16, strlen($result));
        $len_list = unpack("N", substr($content, 0, 4));
        $json_len = $len_list[1];
        return substr($content, 4, $json_len);
    }
}
