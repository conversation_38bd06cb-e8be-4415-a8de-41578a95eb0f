<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SFFY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320): array
    {
        $timestamp = date("YmdHis");
        $sign = self::sign(json_encode($data, $jsonRule), $timestamp);
        Log::handle("Request sffy started", "丰油", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfigData::getAuthConfigValByName("SFFY_APP_DOMAIN");
        $appId = AuthConfigData::getAuthConfigValByName("SFFY_APP_KEY");
        $result = self::curl("$domain$method", $data, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
            "sign:$sign",
            "appId:$appId",
            "timestamp:$timestamp"
        ], 'post', true, $jsonRule);
        Log::handle("Request sffy finished", "丰油", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['succ'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['succ'] != 'ok') {
            throw new Exception($result['msg'], 5000999);
        }
        return $result;
    }

    private static function sign(string $paramsStr, int $timestamp): string
    {
        $appSecret = AuthConfigData::getAuthConfigValByName("SFFY_APP_SECRET");
        return strtoupper(md5($appSecret . $paramsStr . $timestamp . $appSecret));
    }
}
