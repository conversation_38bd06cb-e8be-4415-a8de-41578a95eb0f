<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class JDWC extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        Log::handle("Request jdwc started", "京东无车", $method, [
            'requestData' => $data,
        ], "", "info");
        $dataContent = "[" . json_encode($data, $jsonRule) . "]";
        $secret = AuthConfig::getAuthConfigValByName("JINGDONG_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("JINGDONG_APP_DOMAIN");
        $appKey = AuthConfig::getAuthConfigValByName("JINGDONG_APP_KEY");
        $lopDn = AuthConfig::getAuthConfigValByName("JINGDONG_APP_LOP_DN");
        $xDate = gmdate("D, d M Y H:i:s") . " GMT";
        $contentMd5 = md5($dataContent);
        $signStr = "X-Date: $xDate\ncontent-md5: $contentMd5";
        $sign = hash_hmac("sha1", $signStr, $secret, true);
        $realSign = base64_encode($sign);
        $auth = "hmac username=\"$appKey\", algorithm=\"hmac-sha1\", headers=\"X-Date content-md5\",signature=\"$realSign\"";
        $result = self::curl("$domain$method", $dataContent, $timeOut, [
            "LOP-DN: $lopDn",
            "content-type: application/json;",
            "X-Date: $xDate",
            "content-md5: $contentMd5",
            "Authorization: $auth",
        ], 'post', false, $jsonRule);

        Log::handle("Request jdwc finished", "京东无车", $method, [
            'requestData' => $data,
            'secret'      => $secret,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('京东无车接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 200) {

            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
