<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Evit\PhpGmCrypto\Encryption\EvitSM4Encryption;
use Exception;

class AD extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("AD_APP_DOMAIN");
        $requestData = [
            "appId"     => AuthConfigData::getAuthConfigValByName("AD_APP_KEY"),
            "timestamp" => time(),
        ];
        $appSecret = AuthConfigData::getAuthConfigValByName("AD_APP_SECRET");
        $dataEncKey = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_KEY");
        $dataEncIv = AuthConfigData::getAuthConfigValByName("AD_APP_DATA_ENC_IV");
        $requestData['encryptData'] = base64_encode(
            (new EvitSM4Encryption([
                "key" => $dataEncKey,
                "iv"  => $dataEncIv,
                "mode" => "cbc",
            ]))->encrypt(json_encode($data))
        );
        $requestData['digest'] = base64_encode(
            md5(
                urlencode(
                    $requestData['encryptData'] . $requestData['timestamp'] . $appSecret
                ),
                true
            )
        );
        Log::handle("Request ad started", "安得", $method, [
            "requestParams" => $requestData,
            'initData'      => $data,
        ], "", "info");
        $result = self::curl("$domain$method", $requestData, 20, [
            "Content-Type: application/json",
            'Expect:',
        ], "post", true, 320);
        Log::handle("Request ad finished", "安得", $method, [
            "requestParams" => $requestData,
            'initData'      => $data,
        ], $result, "info");
        if (!array_has($result, ['encryptData'])) {
            if (!array_has($result, ['code'])) {
                throw new Exception('安得接口输出数据格式错误', 5000999);
            }
            if ($result['code'] != 0) {
                throw new Exception($result['msg'] ?? '', 5000999);
            }
            if (!empty($result['data'])) {
                $result['decryptData'] = json_decode(
                                             (new EvitSM4Encryption([
                                                 "key"  => $dataEncKey,
                                                 "iv"   => $dataEncIv,
                                                 "mode" => "cbc",
                                             ]))->decrypt(base64_decode($result['data'])),
                                             true
                                         ) ?? [];
            }
            Log::handle("Request ad finished", "安得", $method, [
                "requestParams" => $requestData,
                'initData'      => $data,
            ], $result, "info");
            return $result;
        }
        if (!empty($result['encryptData'])) {
            $result['decryptData'] = json_decode(
                                         (new EvitSM4Encryption([
                                             "key"  => $dataEncKey,
                                             "iv"   => $dataEncIv,
                                             "mode" => "cbc",
                                         ]))->decrypt(base64_decode($result['encryptData'])),
                                         true
                                     ) ?? [];
        }
        Log::handle("Request ad finished", "安得", $method, [
            "requestParams" => $requestData,
            'initData'      => $data,
        ], $result['decryptData'], "info");
        if (!array_has($result['decryptData'], ['code']) or $result['decryptData']['code'] != 0) {
            throw new Exception($decryptData['msg'] ?? '', 5000999);
        }
        return $result;
    }
}
