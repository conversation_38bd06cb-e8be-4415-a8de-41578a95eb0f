<?php
// 卓壹(气)

namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class ZHUOYIQ extends Base implements ThirdParty
{
    private static $desKey;
    private static $desIv;

    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array|mixed
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 320)
    {
        $domain = AuthConfigData::getAuthConfigValByName('ZHUOYIQ_APP_DOMAIN');
        $requestData = [
            'method'    => $method,
            'timestamp' => time(),
            'data'      => self::encryptData(
                empty($data) ? '{}' : json_encode(
                    $data,
                    $jsonRule
                )
            ),
            'sign_type' => 'sha256',
            'userid'    => AuthConfigData::getAuthConfigValByName('ZHUOYIQ_APP_ID'),
        ];
        $requestData['sign'] = self::sign($requestData);
        unset($requestData['appSecret']);
        Log::handle("Request zhuoyiq started", "卓壹(气)", $method, [
            'requestData' => $requestData,
            'initData'    => $data,
        ], "", "info");
        $result = self::curl($domain, $requestData, $timeOut, [
            "Content-Type: application/json",
        ], 'post', true, $jsonRule);
        Log::handle("Request zhuoyiq finished", "卓壹(气)", $method, [
            'requestData' => $requestData,
            'initData'    => $data,
        ], [
            'result' => $result,
        ], "info");
        if (!array_has($result, ['respCode'])) {
            throw new Exception('卓壹(气)接口输出数据格式错误', 5000999);
        }
        if ($result['respCode'] != 200) {
            throw new Exception($result['respMsg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function encryptData(string $data): string
    {
        self::initDesConfig();
        return base64_encode(
            openssl_encrypt(
                $data,
                "DES-EDE3-CBC",
                self::$desKey,
                OPENSSL_RAW_DATA,
                self::$desIv
            )
        );
    }

    public static function initDesConfig()
    {
        if (empty(self::$desKey) or empty(self::$desIv)) {
            self::$desKey = base64_decode(
                AuthConfigData::getAuthConfigValByName(
                    'ZHUOYIQ_APP_3DES_KEY'
                )
            );
            self::$desIv = substr(self::$desKey, 0, 8);
        }
    }

    public static function sign(array $data): string
    {
        $signKey = AuthConfigData::getAuthConfigValByName('ZHUOYIQ_APP_KEY');
        return hash_hmac(
            "sha256",
            "data=" . $data['data'] . "&timestamp=" . $data['timestamp'] .
            "&userid=" . $data['userid'] . "&userkey=" . $signKey,
            $signKey
        );
    }

    public static function decryptData(string $data)
    {
        self::initDesConfig();
        return openssl_decrypt(
            base64_decode($data),
            "DES-EDE3-CBC",
            self::$desKey,
            OPENSSL_RAW_DATA,
            self::$desIv
        );
    }
}