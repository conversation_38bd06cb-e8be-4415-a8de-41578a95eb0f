<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SC extends Base implements ThirdParty
{
    public static $paddingPrecisionFields = [
        'quantity'         => 3,
        'originalPrice'    => 3,
        'amount'           => 2,
        'shellDiscount'    => 2,
        'discount'         => 2,
        'fuelsDiscount'    => 2,
        'nonfuelsDiscount' => 2,
        'totalDiscount'    => 2,
    ];

    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 60): array
    {
        Log::handle("Request sc started", "四川壳牌", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['merchantNum'] = AuthConfigData::getAuthConfigValByName('SC_APP_KEY');
        $data['timestamp'] = time();
        $data['sign'] = self::sign($data);
        $apiUrl = AuthConfigData::getAuthConfigValByName('SC_APP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, $timeOut, [], 'get');
        Log::handle("Request sc finished", "四川壳牌", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['respCode', 'respMsg'])) {
            if ($method == 'reconciliation/file') {
                return $result;
            }

            throw new Exception('四川壳牌接口输出数据格式错误', 5000999);
        } elseif ($result['respCode'] != '00000') {
            throw new Exception($result['respMsg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, string $secret = ''): string
    {
        self::prepareData($data);
        ksort($data);
        $signData = [];

        foreach ($data as $k => $v) {
            if (!$v) {
                continue;
            }

            if (is_array($v)) {
                $v = json_encode($v);

                foreach (self::$paddingPrecisionFields as $key => $value) {
                    $v = preg_replace_callback("|\"{$key}\":(\".*?\")|", function ($matches) {
                        return str_replace($matches[1], '', $matches[0]) . str_replace('"', '', $matches[1]);
                    }, $v);
                }
            }

            $signData[] = $k . $v;
        }

        $signStr = implode('', $signData) . (empty($secret) ? AuthConfigData::getAuthConfigValByName(
                'SC_APP_SECRET'
            ) : $secret);
        return hash('sha256', $signStr);
    }

    public static function prepareData(array &$data)
    {
        foreach ($data as $k => &$v) {
            if (is_array($v)) {
                if (checkIsAssocArray($v)) {
                    foreach ($v as $ck => &$cv) {
                        if (isset(self::$paddingPrecisionFields[$ck])) {
                            $cv = number_format($cv, self::$paddingPrecisionFields[$ck], '.', '');
                        }
                    }
                } else {
                    self::prepareData($v);
                }
            } elseif (isset(self::$paddingPrecisionFields[$k])) {
                $v = number_format($v, self::$paddingPrecisionFields[$k], '.', '');
            }
        }
    }
}
