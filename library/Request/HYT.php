<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class HYT extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param string $requestMethod
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/6 5:47 下午
     */
    public static function handle(
        $method = null,
        $data = null,
        string $requestMethod = 'post',
        int $timeOut = 60,
        int $jsonRule = 512
    ): array {
        $appKey = AuthConfigData::getAuthConfigValByName("HYT_APP_KEY");
        $appSecret = AuthConfigData::getAuthConfigValByName("HYT_APP_SECRET");
        $appVersion = AuthConfigData::getAuthConfigValByName("HYT_APP_VERSION");
        $domain = AuthConfigData::getAuthConfigValByName("HYT_APP_DOMAIN");
        Log::handle("Request hyt started", "惠运通", $method, [
            'funcArgs'   => func_get_args(),
            'appKey'     => $appKey,
            'appSecret'  => $appSecret,
            'appVersion' => $appVersion,
        ], "", "info");
        $params = [
            'timestamp' => date("Y-m-d H:i:s"),
            'appid'     => $appKey,
            'version'   => $appVersion,
        ];
        $dataSign = "";
        $jsonRequest = false;
        if (!empty($data)) {

            $params['data'] = json_encode($data, $jsonRule);
            $jsonRequest = true;
            $dataSign = strtoupper(md5($params['data']));
        }
        // 判断请求方式，填充header文档类型
        $headers = [];
        if (strtolower($requestMethod) != "get") {
            $headers[] = "Content-Type: application/json;charset=utf-8";
        }
        $params['sign'] = strtoupper(md5($appKey . $appSecret . $dataSign . $params['timestamp'] .
            $appVersion . $appSecret));
        $result = self::curl($domain . $method, $params, $timeOut, $headers, $requestMethod,
            $jsonRequest, $jsonRule);
        Log::handle("Request hyt finished", "惠运通", $method, [
            'funcArgs'   => func_get_args(),
            'params'     => $params,
            'appKey'     => $appKey,
            'appSecret'  => $appSecret,
            'appVersion' => $appVersion,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('惠运通接口输出数据格式错误', 5000999);
        }

        if ($result['code'] !== "0") {

            if ($result['code'] == 717) {

                throw new Exception($result['message'] ?? '', 717);
            }
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
