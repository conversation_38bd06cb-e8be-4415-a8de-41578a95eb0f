<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class BQ extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("BQ_APP_DOMAIN");
        $data = [
            'data' => json_encode($data),
        ];
        $data['apId'] = AuthConfigData::getAuthConfigValByName("BQ_APP_KEY");
        $data['timeStamp'] = date("Y-m-d H:i:s");
        $data['authSign'] = strtoupper(
            md5(
                $data['apId'] .
                $data['timeStamp'] .
                AuthConfigData::getAuthConfigValByName("BQ_APP_SECRET")
            )
        );
        Log::handle("Request WSY started", "宝奇", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $data,
            20,
            [],
            "post",
            true,
            320
        );
        Log::handle("Request BQ finished", "宝奇", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('宝奇接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }
}
