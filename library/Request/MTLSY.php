<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class MTLSY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param array|null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @param bool $throwRawErrCode
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        array $data = null,
        int $timeOut = 20,
        int $jsonRule = 512,
        bool $throwRawErrCode = false
    ): array
    {
        Log::handle("Request mtlSySy started", "梦驼铃(上游)", $method, [
            'requestData' => $data,
        ], "", "info");
        $requestData = self::sign($data, $method, $jsonRule);
        $result = self::curl(AuthConfig::getAuthConfigValByName("MTLSY_APP_DOMAIN"), $requestData, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
            'Expect:',
        ], 'post', true, $jsonRule);
        Log::handle("Request mtlSy finished", "梦驼铃(上游)", $method, [
            'requestData' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('梦驼铃(上游)接口返回数据格式异常', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', $throwRawErrCode ? $result['code'] : 5000999);
        }

        return $result;
    }

    public static function sign(array $data, string $name = '', int $jsonRule = 320): array
    {
        $signData = [];
        $signData['name'] = $name;
        $signData['data'] = urlencode(json_encode($data, $jsonRule));
        $signData['version'] = '';
        $signData['format'] = 'json';
        $signData['timestamp'] = date('Y-m-d H:i:s');
        $signData['app_key'] = AuthConfig::getAuthConfigValByName("MTLSY_APP_KEY");
        $secret = AuthConfig::getAuthConfigValByName("MTLSY_APP_SECRET");
        ksort($signData);
        $signArr = [];
        foreach ($signData as $k => $v) {
            $signArr[] = $k . $v;
        }
        $signStr = $secret . implode("", $signArr) . $secret;
        $signData['sign'] = strtoupper(md5($signStr));
        return $signData;
    }
}
