<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZDC extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $requestMethod = null, $data = null, int $timeOut = 60)
    {
        Log::handle("Request zdc started", "掌多车", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['timestamp'] = getMillisecond();
        $data['appKey'] = AuthConfigData::getAuthConfigValByName("ZDC_APP_KEY");
        $data['sign'] = self::sign($data, AuthConfigData::getAuthConfigValByName("ZDC_APP_SECRET"));
        $apiDomain = AuthConfigData::getAuthConfigValByName('ZDC_APP_DOMAIN');
        $result = self::curl(
            "$apiDomain$method",
            $data,
            $timeOut,
            [],
            $requestMethod,
            !(strtolower($requestMethod) == 'get'),
            320
        );
        Log::handle("Request zdc finished", "掌多车", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('掌多车接口输出数据格式错误.', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret): string
    {
        ksort($data);
        $signStrArr = [
            $secret,
        ];
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val) && $val != '') {
                if (is_array($val) or is_object($val)) {
                    $val = php2JavaArrayValuesToString($val);
                }
                $signStrArr[] = "$key$val";
            }
        }
        $signStrArr[] = "$secret";
        return md5(implode('', $signStrArr));
    }
}
