<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;
use Throwable;


class FOSS extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/5/18 11:49 上午
     */
    public static function handle($method = null, $data = null): array
    {
        Log::handle("Request foss started", "FOSS", $method, [
            'requestData' => $data,
        ], "", "info");

        $params = [
            'method'    => $method,
            'data'      => json_encode($data),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => AuthConfig::getAuthConfigValByName("API_FOSS_APP_KEY"),
            'sign'      => AuthConfig::getAuthConfigValByName("API_FOSS_APP_SIGN"),
        ];
        $api_url = AuthConfig::getAuthConfigValByName('API_FOSS_API_URL');
        $result = self::curl(
            $api_url,
            $params,
            10,
            getTraceInjectHeaders(true)
        );

        Log::handle("Request foss finished", "FOSS", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code', 'data'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }
}
