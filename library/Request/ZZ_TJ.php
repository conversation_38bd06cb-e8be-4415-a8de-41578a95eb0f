<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class ZZ_TJ extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 512): array
    {
        Log::handle("Request zz_tj started", "智猪(天津)", $method, [
            'requestData' => $data,
        ], "", "info");

        $secret = AuthConfig::getAuthConfigValByName("ZZ_TJ_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("ZZ_TJ_APP_DOMAIN");
        $params = [
            'data'      => json_encode($data, $jsonRule),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => AuthConfig::getAuthConfigValByName("ZZ_TJ_APP_KEY")
        ];
        $sign = createSign($params, $secret);
        $params['sign'] = $sign;
        $result = self::curl("{$domain}{$method}", $params, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
        ], 'post', true, $jsonRule);

        Log::handle("Request zz_tj finished", "智猪(天津)", $method, [
            'requestData' => $params,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }
}
