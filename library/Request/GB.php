<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class GB extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param string $requestType
     * @param array|null $data
     * @param string $identifier
     * @param int $timeOut
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle(
        $method = null,
        string $requestType = 'get',
        ?array $data = [],
        string $identifier = "",
        int $timeOut = 60
    ): array {
        $realData = $data;
        $data['identifier'] = $identifier;
        $realData['sign'] = self::sign($data);
        $platformName = DockingPlatformInfoData::getFieldsByNameAbbreviation(
            $identifier,
            "platform_name",
            ""
        )['platform_name'];
        Log::handle("Request gb started", $platformName, $method, [
            'requestData' => $realData,
        ], "", "info");
        $apiUrl = AuthConfigData::getAuthConfigValByName(strtoupper($identifier) . '_APP_DOMAIN') . "/channelCode/" .
                  AuthConfigData::getAuthConfigValByName(strtoupper($identifier) . "_APP_KEY") . "/" . $method;
        $result = self::curl($apiUrl, $realData, $timeOut, [
            'timestamp:' . getMillisecond(),
        ], $requestType, true, 320);
        Log::handle("Request gb finished", $platformName, $method, [
            'requestData' => $realData,
        ], $result, "info");
        if (!array_has($result, 'resultCode')) {
            throw new Exception($result['resultMsg'] ?? '', 5000999);
        }
        if (array_has($result, 'resultCode') and $result['resultCode'] != 0) {
            throw new Exception($result['resultMsg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(?array $data): string
    {
        $identifier = $data['identifier'] ?? '';
        unset($data['identifier']);
        $signStr = "";
        ksort($data);
        foreach ($data as $k => $v) {
            switch (gettype($v)) {
                case "object":
                case "array":
                    continue 2;
            }
            if (empty($k) or (string)$v == "") {
                continue;
            }
            $signStr .= "$k$v";
        }
        $signStr .= AuthConfigData::getAuthConfigValByName(strtoupper($identifier) . '_APP_SECRET');
        return md5($signStr);
    }
}
