<?php


namespace Request;


use App\Models\Data\Log\RequestLog as Log;
use Exception;
use GosSDK\Gos as GosSdk;
use Throwable;


class GOS extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null)
    {
        Log::handle("Request gos started", "GOS", $method, [
            'requestData' => $data,
        ], "", "info");
        try {

            $result = (new GosSdk())
                ->setMethod($method)
                ->setParams($data)
                ->sync();
            Log::handle("Request gos finished", "GOS", $method, [
                'requestData' => $data,
            ], $result, "info");
        } catch (Throwable $exception) {

            throw new Exception($exception->getMessage(), 5000999);
        }

        return $result;
    }
}
