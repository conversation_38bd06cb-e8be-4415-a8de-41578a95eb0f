<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Faker\Provider\Uuid;
use Tool\RsaToJava;


class XYN extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param bool $needToken
     * @param int $timeOut
     * @param int $jsonRule
     * @return array|bool
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        bool $needToken = true,
        int $timeOut = 20,
        int $jsonRule = 320
    ) {
        Log::handle("Request xyn started", "星油(新)", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfigData::getAuthConfigValByName("XYN_APP_SECRET");
        $domain = AuthConfigData::getAuthConfigValByName("XYN_APP_DOMAIN");
        $headers = [
            "Content-Type: application/json;charset=utf-8",
            "instance:" . AuthConfigData::getAuthConfigValByName("XYN_APP_INSTANCE"),
        ];
        if ($needToken) {
            $token = app('redis')->get("xyn_token");
            if (empty($token)) {
                $responseData = self::handle('oil-open-api/openApplication/applyToken', [
                    'appKey' => AuthConfigData::getAuthConfigValByName("XYN_APP_KEY"),
                    'appSecret' => $secret,
                ], false, $timeOut, $jsonRule);
                app('redis')->setex("xyn_token", 43199, $responseData['data']['accessToken']);
                $token = $responseData['data']['accessToken'];
            }
            $nonceStr = md5(Uuid::uuid());
            $jsSessionId = preg_replace(
                '/\$2y\$/',
                '\$2a\$',
                password_hash(
                    md5(
                        $nonceStr . $secret
                    ),
                    PASSWORD_DEFAULT
                ),
                1
            );
            $data['timestamp'] = time();
            ksort($data);
            $jsonData = json_encode($data, $jsonRule);
            $data = [
                'params' => (new RsaToJava(
                    transJavaRsaKeyToPhpOpenSSL(
                        AuthConfigData::getAuthConfigValByName("XYN_APP_RSA_PUBLIC_KEY")
                    ), null, function (string $str) {
                    return base64_encode($str);
                }
                ))->publicEncrypt($jsonData),
            ];
            $headers[] = "JSESSIONID:" . $jsSessionId;
            $headers[] = "token:" . $nonceStr;
            $headers[] = "signType:MD5";
            $headers[] = "sign:" . strrev(md5($jsonData . $secret));
            $headers[] = "Authorization:$token";
        }
        $result = self::curl(
            $domain . $method,
            $data,
            $timeOut,
            $headers,
            'post',
            true,
            $jsonRule
        );
        if (isset($result['data']) and is_string($result['data'])) {
            $result['parsed_data'] = json_decode(
                (new RsaToJava(
                    '', transJavaRsaKeyToPhpOpenSSL(
                    AuthConfigData::getAuthConfigValByName("XYN_APP_RSA_PRIVATE_KEY"),
                    false
                ), null, function (string $str) {
                    return base64_decode($str);
                }
                ))->privateDecrypt($result['data']),
                true
            );
        }
        Log::handle("Request xyn finished", "星油(新)", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code', 'msg'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 20000) {
            throw new Exception($result['msg'], 5000999);
        }
        return $result;
    }
}
