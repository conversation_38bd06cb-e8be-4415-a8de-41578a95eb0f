<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;

class DMD extends Base implements ThirdParty
{
    public static function handle(string $method = '', array $data = [], string $reqMethod = '', array $headers = [])
    {
        $domain = AuthConfig::getAuthConfigValByName("DMD_DOMAIN");
        $realUrl = $domain . $method;
        $requestParams['data'] = json_encode($data);

        Log::handle("Request dmd started", "多蒙德", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $realUrl
        ], "", "info");
        $result = self::curl($realUrl, $requestParams, 10, $headers, $reqMethod);
        Log::handle("Request dmd finished", "多蒙德", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $realUrl
        ], $result, "info");
        return $result;
    }
}
