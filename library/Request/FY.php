<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class FY extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @param string $reqMethod
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 16:25
     */
    public static function handle(string $method = '', array $data = [], string $reqMethod = ''): array
    {
        $domain = AuthConfig::getAuthConfigValByName("FY_DOMAIN");
        $requestTime = strtotime(date("Y-m-d H:i:s"));
        $appKey = AuthConfig::getAuthConfigValByName("FY_APP_KEY");
        $secret = AuthConfig::getAuthConfigValByName("FY_APP_SECRET");
        $sign = md5("$appKey&$requestTime&$secret");
        $requestParams['signStr'] = $sign;
        $requestParams['reqTimestamp'] = $requestTime;
        $requestParams['appKey'] = $appKey;
        $requestParams['data'] = json_encode($data);
        $realUrl = $domain . $method;
        Log::handle("Request fy started", "福佑", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $realUrl
        ], "", "info");
        $result = self::curl($realUrl, $requestParams, 10, [
            "Expect:"
        ], $reqMethod);
        Log::handle("Request fy finished", "福佑", $method, [
            "requestParams" => $requestParams,
            "realUrl"       => $realUrl
        ], $result, "info");

        if (!array_has($result, ["status"])) {
            throw new Exception("福佑接口输出数据格式错误");
        }
        if (!array_has($result["status"], ["code"])) {
            throw new Exception("福佑接口输出数据格式错误", 5000999);
        }
        if ($result["status"]["code"] != 0) {
            throw new Exception($result['status']['desc'] ?? '', 5000999);
        }
        return $result;
    }
}
