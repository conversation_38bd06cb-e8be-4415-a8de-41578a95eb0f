<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class HLL extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("HLL_APP_DOMAIN");
        Log::handle("Request HLL started", "货拉拉", $method, [
            "requestParams" => $data,
        ], "", "info");
        $data = [
            'data'      => json_encode($data),
            'timestamp' => time(),
            'app_key'   => AuthConfigData::getAuthConfigValByName("HLL_APP_KEY"),
        ];
        $data['sign'] = createSign(
            $data,
            AuthConfigData::getAuthConfigValByName(
                "HLL_APP_SECRET"
            )
        );
        $result = self::curl(
            "$domain$method" . AuthConfigData::getAuthConfigValByName("HLL_APP_ID"),
            $data,
            20,
            [],
            "post",
            true,
            320
        );
        Log::handle("Request HLL finished", "货拉拉", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('货拉拉接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        return $result;
    }
}
