<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class PCKJ extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        Log::handle("Request pckj started", "跑车科技", $method, [
            'requestData' => $data,
        ], "", "info");

        $domain = AuthConfig::getAuthConfigValByName("PCKJ_APP_DOMAIN");
        $result = self::curl("$domain$method", $data, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);

        Log::handle("Request pckj finished", "跑车科技", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {

            throw new Exception($result['message'] ?? '', 5000999);
        }

        if ($result['code'] != 0) {

            throw new Exception($result['message'], 5000999);
        }

        return $result;
    }
}
