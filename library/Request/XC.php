<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class XC extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        Log::handle("Request xc started", "讯车", $method, [
            'requestData' => $data,
        ], "", "info");

        $secret = AuthConfig::getAuthConfigValByName("XC_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("XC_APP_DOMAIN");
        $sign = self::sign(
            array_merge($data ?? [], [
                'merch_key' => $secret,
            ]),
            $secret,
            $jsonRule
        );
        $data['signature'] = $sign;
        $result = self::curl("$domain$method", $data, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
            "x-api-ver: 1.0",
            "x-api-key: $secret",
        ], 'post', true, $jsonRule);

        Log::handle("Request xc finished", "讯车", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('讯车接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if (!array_has($result, ['data'])) {
            throw new Exception('讯车接口输出数据格式错误', 5000999);
        }

        return $result;
    }

    protected static function sign(array $data, string $secret, int $jsonRule = 512): string
    {
        $prepareData = [];
        ksort($data);

        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val)) {
                if (is_array($val) or is_object($val)) {
                    $val = json_encode($val, $jsonRule);
                }

                $prepareData[] = "$key=$val";
            }
        }

        $prepareStr = implode("&", $prepareData);
        $dataEncrypted = base64_encode($prepareStr);
        $currentDay = date("Ymd");
        return md5(md5(md5($dataEncrypted) . '.' . $secret) . $currentDay);
    }
}
