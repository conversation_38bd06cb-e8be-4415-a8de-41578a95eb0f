<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class DDE extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param string $requestMethod
     * @param null $data
     * @param int $timeOut
     * @param bool $jsonRequest
     * @param bool $throwException
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        string $requestMethod = 'post',
        $data = null,
        int $timeOut = 20,
        bool $jsonRequest = true,
        bool $throwException = true
    ): array {
        Log::handle("Request dde started", "道达尔", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfig::getAuthConfigValByName("DDE_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("DDE_APP_DOMAIN");
        $params = [
            'data'      => json_encode($data),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => AuthConfig::getAuthConfigValByName("DDE_APP_KEY")
        ];
        $sign = createSign($params, $secret);
        $result = self::curl(
            "$domain$method?sign=$sign",
            $params,
            $timeOut,
            [],
            $requestMethod,
            $jsonRequest);
        Log::handle("Request dde finished", "道达尔", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {

            if ($throwException) {
                throw new Exception('道达尔接口输出数据格式错误', 5000999);
            }
        }

        if ($result['code'] != 0) {

            if ($throwException) {

                throw new Exception($result['msg'] ?? '', 5000999);
            }
        }

        return $result;
    }
}
