<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class AJSW extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("AJSW_APP_DOMAIN");
        $data = self::sign($data);
        Log::handle("Request ajsw started", "安吉四维", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl("$domain$method", $data, 20, [
            "Content-Type: application/json",
            'Expect:',
        ], "post", true, 320);
        Log::handle("Request ajsw finished", "安吉四维", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('安吉四维接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $data): array
    {
        $signData = [
            'appId'     => AuthConfigData::getAuthConfigValByName("AJSW_APP_ID"),
            'json'      => json_encode($data, 320),
            'secretKey' => AuthConfigData::getAuthConfigValByName("AJSW_APP_SECRET"),
            'timestamp' => getMillisecond()
        ];
        ksort($signData);
        $waitSignArr = [];
        foreach ($signData as $k => $v) {

            $waitSignArr[] = "$k=$v";
        }
        return [
            'secretKey' => $signData['secretKey'],
            'appId'     => $signData['appId'],
            'json'      => $signData['json'],
            'sign'      => md5(implode("&", $waitSignArr) . $signData['appId']),
            'timestamp' => (string)$signData['timestamp'],
        ];
    }
}
