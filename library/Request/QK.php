<?php
// 汽开

namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class QK extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 320): array
    {
        $data = [
            'data' => $data,
        ];
        $data['method'] = $method;
        $data['appId'] = AuthConfig::getAuthConfigValByName('QK_APP_ID');
        $data['channelType'] = AuthConfig::getAuthConfigValByName('QK_APP_CHANNEL_TYPE');
        $data['timestamp'] = time();
        self::dataSort($data);
        $data['sign'] = self::sign($data);
        Log::handle("Request QK started", "汽开", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName('QK_APP_DOMAIN');
        $result = self::curl(
            "$domain",
            $data,
            $timeOut,
            array_merge([
                'Content-Type: application/json',
            ], getTraceInjectHeaders(true)),
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request QK finished", "汽开", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('汽开接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function dataSort(array &$data)
    {
        ksort($data);
        ksort($data['data']);
        foreach ($data['data'] as &$v) {
            if (is_array($v)) {
                if (checkIsAssocArray($v)) {
                    ksort($v);
                }
                if (!checkIsAssocArray($v)) {
                    foreach ($v as &$vv) {
                        ksort($vv);
                    }
                }
                unset($v);
            }
        }
    }

    public static function sign(array $data, $jsonRule = 320): string
    {
        $appKey = AuthConfig::getAuthConfigValByName('QK_APP_KEY');
        $appSecret = AuthConfig::getAuthConfigValByName('QK_APP_SECRET');
        ksort($data);
        ksort($data['data']);
        foreach ($data['data'] as &$v) {
            if (is_array($v)) {
                $v = json_encode($v, $jsonRule);
                unset($v);
            }
        }
        $data['data'] = implode('', $data['data']);
        $signStr = implode('', $data);
        return strtoupper(md5($appKey . $appSecret . $signStr));
    }
}
