<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class RY extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("RY_APP_DOMAIN");
        Log::handle("Request RY started", "瑞源", $method, [
            "requestParams" => $data,
        ], "", "info");
        $data = [
            'data'      => json_encode($data),
            'timestamp' => date('Y-m-d H:i:s'),
            'app_key'   => AuthConfigData::getAuthConfigValByName("RY_APP_KEY"),
        ];
        $data['sign'] = createSign(
            $data,
            AuthConfigData::getAuthConfigValByName(
                "RY_APP_SECRET"
            )
        );
        $result = self::curl(
            "$domain$method",
            $data,
            20,
            [],
            "post",
            true
        );
        Log::handle("Request RY finished", "瑞源", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('瑞源接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['msg'] ?? ($result['message'] ?? ''), 5000999);
        }
        return $result;
    }
}
