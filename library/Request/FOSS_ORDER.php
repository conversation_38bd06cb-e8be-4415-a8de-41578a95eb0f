<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class FOSS_ORDER extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/12/25 6:18 下午
     */
    public static function handle($method = null, $data = null, int $timeOut = 60, int $jsonRule = 512): array
    {
        Log::handle("Request foss_order started", "G7能源订单服务", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName('FOSS_ORDER_APP_DOMAIN');
        $result = self::curl(
            "$domain$method",
            $data,
            $timeOut,
            array_merge([
                'Content-Type: application/json',
            ], getTraceInjectHeaders(true)),
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request foss_order finished", "G7能源订单服务", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'], 5000999);
        }

        if ($result['code'] != 0) {
            if ($result['code'] == 3106413011001 or
                (strpos($result['msg'] ?? '', '订单') !== false and
                 (strpos($result['msg'] ?? '', '已使用') !== false or
                  strpos($result['msg'] ?? '', '重复') !== false))) {
                throw new Exception('', 5000126);
            }
            if (in_array($result['code'], config('error.CODE_PERSIST.FOSS_ORDER'))) {
                throw new Exception($result['msg'], $result['code']);
            }
            throw new Exception($result['msg'], 5000999);
        }

        return $result;
    }
}
