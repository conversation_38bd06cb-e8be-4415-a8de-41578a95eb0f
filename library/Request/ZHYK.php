<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZHYK extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle(
        $method = null,
        $data = null,
        $requestMethod = 'post',
        int $timeOut = 60,
        int $jsonRule = 320
    ) {
        Log::handle("Request zhyk started", "智慧油客", $method, [
            'requestData' => $data,
        ], "", "info");
        if ($requestMethod == 'post') {
            $data = [
                'body' => $data,
            ];
        }
        $data['appid'] = AuthConfigData::getAuthConfigValByName("ZHYK_APP_KEY");
        $data['timestamp'] = date('YmdHis');
        $data['sign'] = self::sign($data, AuthConfigData::getAuthConfigValByName("ZHYK_APP_SECRET"));
        $apiUrl = AuthConfigData::getAuthConfigValByName('ZHYK_APP_DOMAIN');
        if ($requestMethod == 'post') {
            $method = "$method?appid={$data['appid']}&timestamp={$data['timestamp']}&sign={$data['sign']}";
        }
        $result = self::curl(
            "$apiUrl$method",
            $requestMethod == 'post' ? $data['body'] : $data,
            $timeOut,
            $requestMethod == 'post' ? [
                "Content-Type: application/json;charset=utf-8",
            ] : [],
            $requestMethod,
            $requestMethod == 'post'
        );
        Log::handle("Request zhyk finished", "智慧油客", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('智慧油客接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 100) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret, $jsonRule = 320): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val)) {
                if (is_object($val) or is_array($val)) {
                    $val = json_encode($val, $jsonRule);
                }

                $signStrArr[] = $key . '=' . $val;
            }
        }
        $signStrArr[] = "key=$secret";
        return strtoupper(md5(implode('&', $signStrArr)));
    }
}
