<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class HR extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 5): array
    {
        $timestamp = getMillisecond();
        $requestData = json_encode($data, 320);
        $sign = self::sign($requestData, $timestamp);
        Log::handle("Request hr started", "昊锐", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfigData::getAuthConfigValByName("HR_APP_DOMAIN");
        $result = self::curl("$domain$method", $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
            "timestamp: $timestamp",
            "sign: $sign",
        ], 'post', true);
        Log::handle("Request hr finished", "昊锐", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('昊锐接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    private static function sign(string $data, int $timestamp): string
    {
        return md5($data . "&secret=" . AuthConfigData::getAuthConfigValByName(
                "HR_APP_SECRET") . "&timestamp=" . $timestamp);
    }
}
