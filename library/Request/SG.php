<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Faker\Provider\Uuid;

class SG extends Base implements ThirdParty
{

    public static function handle(string $biz_id = '', array $biz_content = [], string $version = '1.0',
                                  string $sign_type = 'md5')
    {
        $sgDomain = AuthConfig::getAuthConfigValByName("SG_DOMAIN");
        $sgSecret = AuthConfig::getAuthConfigValByName("SG_SECRET");
        $sgBasicUrl = $sgDomain;
        $requestParams = [];
        $sign = '';
        $requestParams['biz_id'] = $biz_id;
        $requestParams['version'] = $version;
        $requestParams['request_id'] = Uuid::uuid();
        $requestParams['biz_content'] = !empty($biz_content) ? json_encode($biz_content) : '';
        $requestParams['sign_type'] = $sign_type;
        ksort($requestParams);
        $signStr = implode('', array_values($requestParams));

        if ($sign_type == 'md5') {

            $sign = md5($signStr . $sgSecret);
        }

        $requestParams['sign'] = $sign;
        Log::handle("Request sg started", "山高", $biz_id, [
            "requestParams" => $requestParams,
            "secret"        => $sgSecret,
            "realUrl"       => $sgBasicUrl
        ], "", "info");
        $result = self::curl($sgBasicUrl, $requestParams, 20, [
            'Content-Type: application/json'
        ], 'post', true);
        Log::handle("Request sg finished", "山高", $biz_id, [
            "requestParams" => $requestParams,
            "secret"        => $sgSecret,
            "realUrl"       => $sgBasicUrl
        ], $result, "info");

        return $result;
    }
}
