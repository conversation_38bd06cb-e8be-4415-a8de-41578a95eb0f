<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class CN extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320)
    {
        Log::handle("Request cn started", "村鸟", $method, [
            'requestData' => $data,
        ], "", "info");
        $secret = AuthConfig::getAuthConfigValByName("CN_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("CN_APP_DOMAIN");
        $appId = AuthConfig::getAuthConfigValByName("CN_APP_KEY");
        $requestData = [];
        $requestData['appId'] = $appId;
        $requestData['method'] = $method;
        $requestData['timestamp'] = time();
        $sign = self::sign($data ?? [], $appId, $secret, $method, $requestData['timestamp'], $jsonRule);
        $requestData['data'] = json_encode($data, 320);
        $requestData['sign'] = $sign;
        $result = self::curl($domain, $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);
        Log::handle("Request cn finished", "村鸟", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    protected static function sign(array $data, string $appId, string $secret, string $method, int $timestamp,
        int $jsonRule = 512
    ): string
    {
        $prepareData = [];
        ksort($data);

        foreach ($data as $key => $val) {

            if ($key != '' && !is_null($val)) {

                if (!in_array($key, ['appId', 'timestamp', 'method'])) {

                    if (is_array($val) or is_object($val)) {

                        $val = json_encode($val, $jsonRule);
                    }

                    $prepareData[] = "$key=$val";
                }
            }
        }

        $prepareStr = $secret . $appId . implode(",", $prepareData) . $method . $timestamp . $secret;
        return strtoupper(md5($prepareStr));
    }
}
