<?php
// 中通

namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZTO extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("ZTO_APP_DOMAIN");
        Log::handle("Request ZTO started", "中通", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl(
            "$domain",
            $data,
            20,
            array_merge(self::getSignAndHeader(json_encode($data, 320))['headers'], [
                "method:$method"
            ]),
            "post",
            true,
            320
        );
        Log::handle("Request ZTO finished", "中通", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('中通接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function getSignAndHeader(string $data): array
    {
        $appKey = AuthConfigData::getAuthConfigValByName("ZTO_APP_KEY");
        $appSecret = AuthConfigData::getAuthConfigValByName("ZTO_APP_SECRET");
        $timestamp = time();
        $sign = strtoupper(md5("appKey=$appKey&appSecret=$appSecret&requestData=$data&timestamp=$timestamp"));
        return [
            'sign'    => $sign,
            'headers' => [
                "appKey:$appKey",
                "timestamp:$timestamp",
                "appSign:$sign",
            ],
        ];
    }
}
