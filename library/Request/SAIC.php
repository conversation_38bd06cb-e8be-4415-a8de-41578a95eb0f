<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SAIC extends Base implements ThirdParty
{
    public static $paddingPrecisionFields = [
        'amount' => 2,
    ];

    /**
     * @param array $authInfo
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle(array $authInfo = [], $method = null, $data = null, int $timeOut = 60): array
    {
        $data = $data ?? [];
        ksort($data);
        $realData = [];
        $realData['body'] = $data;
        $realData['appId'] = $authInfo['app_id'] ?? '';
        $realData['sign'] = self::sign($data, $authInfo['app_key'] ?? '');
        Log::handle("Request saic started", "上汽", $method, [
            'requestData' => $realData,
        ], "", "info");
        $realData = json_encode($realData, 320);
        foreach (self::$paddingPrecisionFields as $k => $v) {

            $realData = preg_replace_callback("|\"{$k}\":(.*?),|", function ($matches) {

                $matches1 = $matches[1];
                $matches1 = str_replace('"', '', $matches1);
                $floatSplit = explode('.', $matches1);
                $floatSplit[1] = count($floatSplit) > 1 ? $floatSplit[1] : '';
                $floatSplit[1] .= str_repeat('0', (2 - strlen($floatSplit[1])));
                return str_replace($matches[1], implode('.', $floatSplit), $matches[0]);
            }, $realData);
        }
        $realNameAbbreviation = (json_decode(AuthConfigData::getAuthConfigValByName(
            'SAIC_APP_ID_TO_SUPPLIER_IDENTIFIER_MAPPING'), true) ??
            [])[$authInfo['app_id'] ?? ''] ?? '';
        if (!$baseUrl = AuthConfig::getAuthConfigValByName("SAIC_" . strtoupper($realNameAbbreviation) .
            "_APP_DOMAIN")) {

            $baseUrl = AuthConfig::getAuthConfigValByName("SAIC_APP_DOMAIN");
        }
        $apiUrl = $baseUrl . $method;
        $result = self::curl($apiUrl, $realData, $timeOut, [
            'Content-Type: application/json;charset=utf-8'
        ]);
        Log::handle("Request saic finished", "上汽", $method, [
            'requestData' => $realData,
        ], $result, "info");
        if ((array_has($result, 'status') and $result['status'] != 'OK') or
            (array_has($result, 'code') and $result['code'] != '200') and $result['code'] != '300') {

            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign($data, $secret): string
    {
        $signStr = $data;
        if (is_array($data)) {

            $signStr = json_encode($data, 320) . $secret;
            foreach (self::$paddingPrecisionFields as $k => $v) {

                $signStr = preg_replace_callback("|\"{$k}\":(.*?),|", function ($matches) {

                    $matches1 = $matches[1];
                    $matches1 = str_replace('"', '', $matches1);
                    $floatSplit = explode('.', $matches1);
                    $floatSplit[1] = count($floatSplit) > 1 ? $floatSplit[1] : '';
                    $floatSplit[1] .= str_repeat('0', (2 - strlen($floatSplit[1])));
                    return str_replace($matches[1], implode('.', $floatSplit), $matches[0]);
                }, $signStr);
            }
        }
        return strtoupper(md5($signStr));
    }
}
