<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Throwable;

class CZB extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param mixed $data
     * @param int $timeOut
     * @return array
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-15 19:59
     */
    public static function handle(string $method = '', $data = [], int $timeOut = 60): array
    {
        $accessKey = AuthConfig::getAuthConfigValByName("CZB_APP_KEY");
        $secret = AuthConfig::getAuthConfigValByName("CZB_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("CZB_DOMAIN");
        $signData = array_merge([
            'timestamp' => getMillisecond(),
            'app_key'   => $accessKey,
        ], $data);
        $signData['sign'] = createSignForCzb($signData, $secret, 320);
        Log::handle("Request czb started", "车主邦", $method, [
            'requestData' => $signData,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], "", "info");
        $result = self::curl("$domain$method", http_build_query($signData), $timeOut, [
            'Content-Type: application/x-www-form-urlencoded',
        ], 'post', false, 320);
        Log::handle("Request czb finished", "车主邦", $method, [
            'requestData' => $signData,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], $result, "info");

        if (!array_has($result, ['code', 'message'])) {
            if (array_has($result, ['responseRawData']) and $result['responseRawData'] == 200) {
                return ['code' => 0, 'result' => [], 'message' => '成功'];
            }

            throw new Exception("车主邦接口输出数据格式错误");
        }

        if ($result['code'] != 200) {
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
