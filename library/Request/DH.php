<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Tool\Des3;

class DH extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 60)
    {
        $data['timesTamp'] = date("YmdHis");
        $data['appUserId'] = AuthConfigData::getAuthConfigValByName("DH_APP_ID");
        $data['channel'] = AuthConfigData::getAuthConfigValByName("DH_APP_CHANNEL");
        $requestData = [
            'data' => (new Des3(self::getDataEncryptKey()))->encrypt(
                json_encode($data, 320)
            )
        ];
        Log::handle("Request dh started", "乐道东华", $method, [
            'requestData' => $data,
            'requestBody' => $requestData,
        ], "", "info");
        $apiDomain = AuthConfigData::getAuthConfigValByName('DH_APP_DOMAIN');
        $result = self::curl(
            "$apiDomain$method",
            http_build_query($requestData),
            $timeOut
        );
        $result['data'] = json_decode(
            (new Des3(self::getDataEncryptKey()))->decrypt($result['responseRawData']),
            true
        );
        Log::handle("Request dh finished", "乐道东华", $method, [
            'requestData' => $data,
            'requestBody' => $requestData,
        ], $result, "info");
        if (!array_has($result, ['data']) or !array_has($result['data'], ['respCode'])) {
            throw new Exception('乐道东华接口输出数据格式错误.', 5000999);
        }
        if ($result['data']['respCode'] != 1) {
            throw new Exception($result['data']['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function getDataEncryptKey(): string
    {
        $secret = AuthConfigData::getAuthConfigValByName("DH_APP_SECRET");
        $key = str_split(pack('C*', ...array_fill(0, 24, 0)));
        $secretBytes = array_values(unpack('C*', $secret));
        for ($i = 0; $i < 24 and $i < count($secretBytes); $i++) {
            $key[$i] = $secretBytes[$i];
        }
        return pack('C*', ...$key);
    }
}
