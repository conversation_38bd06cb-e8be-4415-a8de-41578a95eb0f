<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class DESP extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @param string $identifier
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = [], string $identifier = ""): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("DESP_APP_DOMAIN");
        Log::handle("Request desp started", "数字能源" . $identifier, $method, [
            "requestParams" => $data,
        ], "", "info");
        $data = [
            'data'      => json_encode($data),
            'timestamp' => time(),
            'app_key'   => AuthConfigData::getAuthConfigValByName("DESP_APP_KEY_$identifier"),
        ];
        $data['sign'] = createSign($data, AuthConfigData::getAuthConfigValByName(
            "DESP_APP_SECRET_$identifier"));
        $result = self::curl("$domain$method", $data, 20, [
            "Content-Type: application/json",
        ], "post", true, 320);
        Log::handle("Request desp finished", "数字能源" . $identifier, $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['sub_status'])) {
            throw new Exception('数字能源接口输出数据格式错误', 5000999);
        }
        if ($result['sub_status'] != 0) {

            throw new Exception($result['sub_msg'] ?? '', 5000999);
        }
        return $result;
    }
}
