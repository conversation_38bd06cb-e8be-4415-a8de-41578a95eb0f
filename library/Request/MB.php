<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class MB extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("MB_APP_DOMAIN");
        Log::handle("Request MB started", "满帮", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl(
            "$domain$method",
            $data,
            20,
            [
                'x-sign-type: RSA',
                'x-sign: ' . self::sign($data),
                'x-ap-channel: ' . AuthConfigData::getAuthConfigValByName(
                    "MB_APP_KEY"
                ),
            ]
        );
        Log::handle("Request MB finished", "满帮", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['status'])) {
            throw new Exception('满帮接口输出数据格式错误', 5000999);
        }
        if ($result['status'] != 'OK') {
            throw new Exception($result['errorMsg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $data): string
    {
        ksort($data);
        $signData = [];
        foreach ($data as $k => $v) {
            if ((is_string($v) and empty($v)) or is_null($v)) {
                continue;
            }
            $signData[] = $k . '=' . $v;
        }
        $signStr = implode('&', $signData);
        openssl_sign(
            $signStr,
            $sign,
            transJavaRsaKeyToPhpOpenSSL(
                AuthConfigData::getAuthConfigValByName(
                    "G7_MB_RSA_PRIVATE_KEY"
                ),
                false
            )
        );
        return base64_encode($sign);
    }
}