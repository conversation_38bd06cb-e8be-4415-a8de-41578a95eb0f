<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class SP extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param string $accessKey
     * @param string $secret
     * @param int $timeOut
     * @return array
     * @throws Exception ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/23 4:47 下午
     */
    public static function handle(
        $method = null,
        $data = null,
        string $accessKey = '',
        string $secret = '',
        int $timeOut = 20
    ): array {
        $thirdParty = "慧加油" . (array_slice(
                                      explode('_', AuthConfigData::getAuthConfigNameByVal($accessKey)),
                                      -1
                                  )[0] ?? '');
        Log::handle("Request sp started", $thirdParty, $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], "", "info");

        $params = [
            'data'      => json_encode($data),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $accessKey
        ];

        $sign = createSign($params, $secret);
        $params['sign'] = $sign;
        $basicUrl = AuthConfigData::getAuthConfigValByName('SP_APP_DOMAIN');
        $result = self::curl($basicUrl . $method, $params, $timeOut);

        Log::handle("Request sp finished", $thirdParty, $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if (!array_has($result, ['data'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }
}
