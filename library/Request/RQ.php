<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class RQ extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 320)
    {
        Log::handle("Request rq started", "荣庆", $method, [
            'requestData' => $data,
        ], "", "info");
        $params = [
            'data'      => json_encode($data, $jsonRule),
            'timestamp' => Carbon::now('Asia/Shanghai')->format("YmdHis"),
            'appid'     => AuthConfigData::getAuthConfigValByName("RQ_APP_KEY")
        ];
        $sign = self::sign($params, AuthConfigData::getAuthConfigValByName("RQ_APP_SECRET"));
        $params['sign'] = $sign;
        $apiUrl = AuthConfigData::getAuthConfigValByName('RQ_APP_DOMAIN');
        $result = self::curl("$apiUrl?api=$method&v=" . AuthConfigData::getAuthConfigValByName(
                "RQ_APP_VERSION"), $params, $timeOut, [], 'post', true);
        Log::handle("Request rq finished", "荣庆", $method, [
            'requestData' => $params,
        ], $result, "info");
        if (!array_has($result, ['code', 'success'])) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != 0 or !$result['success']) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret, $jsonRule = 320): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $key => $val) {

            if ($key != '' && !is_null($val)) {

                if (!is_string($val)) {

                    $val = json_encode($val, $jsonRule);
                }

                $signStrArr[] = $key . '=' . $val;
            }
        }
        $signStrArr[] = "key=$secret";
        return strtoupper(md5(implode('&', $signStrArr)));
    }
}
