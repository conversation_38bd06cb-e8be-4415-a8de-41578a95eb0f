<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class YB extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        Log::handle("Request yb started", "驭帮", $method, [
            'requestData' => $data,
        ], "", "info");

        $secret = AuthConfig::getAuthConfigValByName("YB_APP_SECRET");
        $domain = AuthConfig::getAuthConfigValByName("YB_APP_DOMAIN");
        $params = [
            'data'      => json_encode($data, $jsonRule),
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => AuthConfig::getAuthConfigValByName("YB_APP_KEY")
        ];
        $sign = createSign($params, $secret);
        $params['sign'] = $sign;
        $result = self::curl("$domain$method", $params, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
        ], 'post', true, $jsonRule);

        Log::handle("Request yb finished", "驭帮", $method, [
            'requestData' => $params,
            'secret'      => $secret,
        ], $result, "info");

        if (!array_has($result, ['errorcode', 'datastr'])) {
            throw new Exception('驭帮接口输出数据格式错误', 5000999);
        }

        if ($result['errorcode'] != 0) {
            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
