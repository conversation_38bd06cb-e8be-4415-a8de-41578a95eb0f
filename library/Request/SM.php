<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SM extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        Log::handle("Request sm started", "世盟", $method, [
            'requestData' => $data,
        ], "", "info");

        $domain = AuthConfig::getAuthConfigValByName("SM_APP_DOMAIN");
        $result = self::curl("$domain$method", http_build_query($data ?? []), $timeOut, [
            'Content-Type: application/x-www-form-urlencoded',
        ]);

        Log::handle("Request sm finished", "世盟", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('世盟接口输出数据格式错误', 5000999);
        }

        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }

        if (!array_has($result, ['data'])) {
            throw new Exception('世盟接口输出数据格式错误', 5000999);
        }

        return $result;
    }
}
