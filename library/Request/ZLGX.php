<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class ZLGX extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @param bool $isAuth
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle(
        $method = null,
        $data = null,
        int $timeOut = 120,
        int $jsonRule = 320,
        bool $isAuth = true
    ): array {
        $token = "";
        if ($isAuth) {
            $responseData = self::handle('api/thirdparty/G7/v1/auth', [
                'companyKey' => AuthConfig::getAuthConfigValByName("ZLGX_APP_KEY"),
            ], $timeOut, $jsonRule, false);
            $token = $responseData['data'];
        }
        Log::handle("Request zlgx started", "中联钢信", $method, [
            'requestData' => $data,
        ], "", "info");
        $domain = AuthConfig::getAuthConfigValByName("ZLGX_APP_DOMAIN");
        $headers = [
            "Content-Type: application/json;charset=utf-8",
        ];
        if ($isAuth) {
            $headers[] = "jwtToken: $token";
        }
        $result = self::curl(
            "$domain$method",
            $data,
            $timeOut,
            $headers,
            'post',
            true,
            $jsonRule
        );
        Log::handle("Request zlgx finished", "中联钢信", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('中联钢信接口输出数据格式错误', 5000999);
        }

        $successCode = $isAuth ? 0 : 10000;
        if ($result['code'] != $successCode) {
            throw new Exception($isAuth ? ($result['msg'] ?? '') : ($result['message'] ?? ''), 5000999);
        }

        return $result;
    }
}
