<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class HSY extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle($method = null, $requestMethod = null, $data = null, int $timeOut = 60)
    {
        Log::handle("Request hsy started", "华商云", $method, [
            'requestData' => $data,
        ], "", "info");
        $data['timestamp'] = getMillisecond();
        $data['appId'] = AuthConfigData::getAuthConfigValByName("HSY_APP_KEY");
        $data['sign'] = self::sign($data, AuthConfigData::getAuthConfigValByName("HSY_APP_SECRET"));
        $apiDomain = AuthConfigData::getAuthConfigValByName('HSY_APP_DOMAIN');
        $result = self::curl("$apiDomain$method", $data, $timeOut, [
            "stationType: " . AuthConfigData::getAuthConfigValByName("HSY_STATION_TYPE"),
        ], $requestMethod);
        Log::handle("Request hsy finished", "华商云", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('华商云接口输出数据格式错误.', 5000999);
        }
        if ($result['code'] != 0) {

            throw new Exception($result['msg'] ?? '', 5000999);
        }

        return $result;
    }

    public static function sign(array $data, $secret, $jsonRule = 320): string
    {
        ksort($data);
        $signStrArr = [];
        foreach ($data as $key => $val) {

            if ($key != '' && !is_null($val)) {

                if (!is_string($val)) {

                    $val = json_encode($val, $jsonRule);
                }

                $signStrArr[] = "$key$val";
            }
        }
        $signStrArr[] = "$secret";
        return md5(implode('', $signStrArr));
    }
}
