<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Carbon\Carbon;
use Exception;

class D<PERSON>NDI extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-24 16:27
     */
    public static function handle($method = null, $data = null, int $timeOut = 20): array
    {
        $accessKey = AuthConfig::getAuthConfigValByName("DIANDI_APP_KEY");
        $secret = AuthConfig::getAuthConfigValByName("DIANDI_SECRET");
        Log::handle("Request dd started", "点滴", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], "", "info");

        $params = [
            'data'      => $data,
            'timestamp' => Carbon::now('Etc/GMT')->timestamp,
            'app_key'   => $accessKey
        ];

        $sign = createSignForDD($params, $secret, 320);
        $api_url = AuthConfig::getAuthConfigValByName('DIANDI_DOMAIN');
        $result = self::curl("$api_url$method", $data, $timeOut, [
            'Content-Type: application/json;charset=utf-8',
            "x-diandi-timestamp:{$params['timestamp']}",
            "x-diandi-appkey:{$params['app_key']}",
            "x-diandi-sign:$sign",
        ], 'post', true, 320);

        Log::handle("Request dd finished", "点滴", $method, [
            'requestData' => $data,
            'accessKey'   => $accessKey,
            'secret'      => $secret
        ], $result, "info");

        if (!array_has($result, ['code', 'data'])) {
            throw new Exception('点滴接口输出数据格式错误', 5000999);
        }

        if ($result["code"] != 0) {

            throw new Exception($result['message'] ?? '', 5000999);
        }

        return $result;
    }
}
