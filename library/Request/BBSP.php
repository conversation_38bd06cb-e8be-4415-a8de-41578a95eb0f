<?php


namespace Request;


use App\Models\Data\AuthConfig;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class BBSP extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param null $data
     * @param int $timeOut
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:29
     */
    public static function handle($method = null, $data = null, int $timeOut = 20): array
    {
        Log::handle("Request bbsp started", "叭叭速配", $method, [
            'requestData' => $data,
        ], "", "info");

        $apiUrl = AuthConfig::getAuthConfigValByName('BBSP_DOMAIN') . $method;
        $result = self::curl($apiUrl, $data, $timeOut, [
            "Content-Type: application/json",
            "Accept:application/vnd.web.miniapp-1.0+json",
            "App-Version:1.0",
        ], 'post', true);

        Log::handle("Request bbsp finished", "叭叭速配", $method, [
            'requestData' => $data,
        ], $result, "info");

        if (!array_has($result, ['code'])) {
            throw new Exception('叭叭速配接口输出数据格式错误', 5000999);
        } else {

            if ($result['code'] != 0) {

                throw new Exception($result['msg'] ?? '', 5000999);
            }

            if (!array_has($result, ['data'])) {
                throw new Exception('Bbsp 接口输出数据格式错误', 5000999);
            }
            $result['data'] = json_decode($result['data'], true);
        }

        return $result;
    }
}
