<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;
use Tool\RsaToJava;

class MY extends Base implements ThirdParty
{
    /**
     * @param null $method
     * @param array|null $data
     * @param int $timeOut
     * @param int $jsonRule
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 18:03
     */
    public static function handle($method = null, array $data = null, int $timeOut = 20, int $jsonRule = 256): array
    {
        $requestData = self::sign($data);
        $requestData['method'] = $method;
        Log::handle("Request my started", "满易", $method, [
            'requestData'     => $data,
            'requestRealData' => $requestData,
        ], "", "info");
        $domain = AuthConfigData::getAuthConfigValByName("MY_APP_DOMAIN");
        $result = self::curl($domain, $requestData, $timeOut, [
            "Content-Type: application/json;charset=utf-8",
        ], 'post', true, $jsonRule);
        $g7RsaPrivateKey = transJavaRsaKeyToPhpOpenSSL(
            AuthConfigData::getAuthConfigValByName(
                "G7_MY_RSA_PRIVATE_KEY"
            ),
            false
        );
        $decryptedData = (new RsaToJava('', $g7RsaPrivateKey, null, function (string $str) {
            return base64_decode($str);
        }))->privateDecrypt($result['data'] ?? '');
        Log::handle("Request my finished", "满易", $method, [
            'requestData'   => $data,
            'decryptedData' => $decryptedData,
        ], $result, "info");
        $result['data'] = json_decode($decryptedData, true);
        if (!array_has($result, ['code'])) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        if ($result['code'] != '0000') {
            throw new Exception($result['msg'], 5000999);
        }
        return $result;
    }

    private static function sign(array $data): array
    {
        $myRsaPublicKey = transJavaRsaKeyToPhpOpenSSL(
            AuthConfigData::getAuthConfigValByName(
                "MY_RSA_PUBLIC_KEY"
            )
        );
        $g7RsaPrivateKey = transJavaRsaKeyToPhpOpenSSL(
            AuthConfigData::getAuthConfigValByName(
                "G7_MY_RSA_PRIVATE_KEY"
            ),
            false
        );
        $appId = AuthConfigData::getAuthConfigValByName("MYB_APP_ID");
        $encryptedData = (new RsaToJava($myRsaPublicKey, '', function (string $str) {
            return base64_encode($str);
        }))->publicEncrypt(json_encode($data, 320));
        openssl_sign($encryptedData, $sign, $g7RsaPrivateKey);
        return [
            'channelCode' => $appId,
            'requestTime' => date('YmdHis'),
            'data'        => $encryptedData,
            'sign'        => base64_encode($sign),
        ];
    }
}
