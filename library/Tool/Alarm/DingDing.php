<?php


namespace Tool\Alarm;

use App\Models\Data\AuthConfig as AuthConfigData;
use Library\Monitor\Report;

class DingDing extends Protocol
{
    private $accessToken;
    private $accessTokenTypeToConfigNameMapping = [
        'price'                       => 'DING_DING_ACCESS_TOKEN_PRICE',
        'station'                     => 'DING_DING_ACCESS_TOKEN',
        ''                            => 'DING_DING_ACCESS_TOKEN',
        'order'                       => 'DING_DING_ACCESS_TOKEN_ORDER',
        'up_stream_order'             => 'DING_DING_ACCESS_TOKEN_UP_STREAM_ORDER',
        'down_stream_order'           => 'DING_DING_ACCESS_TOKEN_DOWN_STREAM_ORDER',
        'balance'                     => 'DING_DING_ACCESS_TOKEN_BALANCE',
        'down_stream_bill'            => 'DING_DING_ACCESS_TOKEN_DOWN_STREAM_BILL',
        'simple'                      => 'DING_DING_ACCESS_TOKEN_SIMPLE',
        'reservation_order_exception' => 'DING_DING_ACCESS_TOKEN_RESERVATION_ORDER_EXCEPTION',
        'push_order_status_failed'    => 'DING_DING_ACCESS_TOKEN_PUSH_ORDER_STATUS_FAILED',
        'supplier_refund_alarm'       => 'DING_DING_ACCESS_TOKEN_SUPPLIER_REFUND_ALARM',
        'fy_order_alarm'              => 'DING_DING_ACCESS_TOKEN_FY_ORDER_ALARM',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function saleGeGunForPrice(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        $parameters['title'] = '站点价格异常';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    /**
     * @param array $parameters ['stationName' => 'x', 'stationId' => 'x', 'platformName' => 'x']
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/9/4 10:08 下午
     */
    public function stationPushFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['station']
        );
        $parameters['title'] = '站点推送失败';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        global $routeId;
        $parameters['routeId'] = $routeId;
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function oilConvertFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['station']
        );
        $parameters['title'] = '站点接收/拉取失败(油品转换失败)';
        $parameters['createdAt'] = date("Y-m-d H:i:s");
        global $routeId;
        $parameters['routeId'] = $routeId;
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function pushOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['down_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function queryOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function deductionMainAccountFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['down_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function getPayQrCodeBySupplierFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName($this->accessTokenTypeToConfigNameMapping['order']);
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function writtenOffFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function getSecondaryCertificateFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function stationOilRepeat(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName($this->accessTokenTypeToConfigNameMapping['order']);
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function tradeDifferent(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function priceNotMeetRequirements(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function insufficientBalance(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['balance']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function gdPermissionInvalid(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['balance']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function queueMonitoringAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['balance']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function freeOrderMoney(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function pushOrderStatusFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['push_order_status_failed']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function reToBePaidApproveResult(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['push_order_status_failed']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function reservationOrderException(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['reservation_order_exception']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function supplierOrderFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function compareSupplierMoneyFailed(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->accessToken, $this->renderTemplate(__FUNCTION__, $parameters));
    }

    public function oilAndGunListedPriceNotMatch(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function oilAndGunSalePriceNotMatch(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function gunListedPriceNoUnique(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function gunSalePriceNoUnique(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['up_stream_order']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function outServiceInterfaceInvalid(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['balance']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }


    public function customerRefundCallback(string $accessToken, array $parameters = [])
    {
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $accessToken);
    }

    public function supplierRefundAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['supplier_refund_alarm']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }

    public function fyOrderUseTimeExceptionAlarm(array $parameters = [])
    {
        $this->accessToken = AuthConfigData::getAuthConfigValByName(
            $this->accessTokenTypeToConfigNameMapping['fy_order_alarm']
        );
        Report::sendToDingding($this->renderTemplate(__FUNCTION__, $parameters), $this->accessToken);
    }
}
