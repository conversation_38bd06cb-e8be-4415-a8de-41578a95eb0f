<?php


namespace Tool\Alarm;


abstract class Protocol
{
    protected $templates;

    public function __construct()
    {
        $currentClass = explode("\\", get_called_class());
        $this->templates = config("template." . $currentClass[count($currentClass) - 1]);
    }

    abstract public function stationPushFailed();

    abstract public function saleGeGunForPrice();

    abstract public function oilConvertFailed(array $parameters = []);

    abstract public function pushOrderFailed(array $parameters = []);

    abstract public function queryOrderFailed(array $parameters = []);

    abstract public function deductionMainAccountFailed(array $parameters = []);

    abstract public function getSecondaryCertificateFailed(array $parameters = []);

    abstract public function stationOilRepeat(array $parameters = []);

    abstract public function tradeDifferent(array $parameters = []);

    abstract public function priceNotMeetRequirements(array $parameters = []);

    abstract public function insufficientBalance(array $parameters = []);

    abstract public function gdPermissionInvalid(array $parameters = []);

    abstract public function queueMonitoringAlarm(array $parameters = []);

    abstract public function freeOrderMoney(array $parameters = []);

    abstract public function pushOrderStatusFailed(array $parameters = []);

    abstract public function reToBePaidApproveResult(array $parameters = []);

    abstract public function reservationOrderException(array $parameters = []);

    abstract public function supplierOrderFailed(array $parameters = []);

    abstract public function compareSupplierMoneyFailed(array $parameters = []);

    abstract public function oilAndGunListedPriceNotMatch(array $parameters = []);

    abstract public function oilAndGunSalePriceNotMatch(array $parameters = []);

    abstract public function gunListedPriceNoUnique(array $parameters = []);

    abstract public function gunSalePriceNoUnique(array $parameters = []);

    abstract public function outServiceInterfaceInvalid(array $parameters = []);

    abstract public function customerRefundCallback(string $accessToken, array $parameters = []);

    abstract public function supplierRefundAlarm(array $parameters = []);

    abstract public function fyOrderUseTimeExceptionAlarm(array $parameters = []);

    public function renderTemplate(string $templateName, array $templateParameters): string
    {
        return renderMessageTemplate($this->templates[$templateName], $templateParameters);
    }
}
