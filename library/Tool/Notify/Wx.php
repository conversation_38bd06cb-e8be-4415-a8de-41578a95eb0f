<?php


namespace Tool\Notify;

use Request\GAS as GASRequest;
use Throwable;

class Wx implements Protocol
{

    public static function sendByTemplateAndTarget(string $templateId, string $target, array $macros, string $accessKey,
                                                   string $secret)
    {
        try {

            GASRequest::handle("gas.api.sendTemplateMsg", [
                "templateId" => $templateId,
                "cardNo"     => $target,
                "content"    => $macros,
            ], $accessKey, $secret);
        } catch (Throwable $exception) {
        }
    }
}
