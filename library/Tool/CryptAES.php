<?php


namespace Tool;

use Throwable;

/**
 * 对接Java AES
 * Class CryptAES
 * @package Tool
 */
class CryptAES
{
    /**
     *
     * @param string $string 需要加密的字符串
     * @param string $key 密钥
     * @return string
     */
    public static function encrypt(string $string, string $key): string
    {
        $data = openssl_encrypt($string, 'AES-128-ECB', $key, OPENSSL_RAW_DATA);
        return strtoupper(bin2hex($data));
    }


    /**
     * @param string $string 需要解密的字符串
     * @param string $key 密钥
     * @return string
     */
    public static function decrypt(string $string, string $key): string
    {
        try {
            return openssl_decrypt(hex2bin($string), 'AES-128-ECB', $key, OPENSSL_RAW_DATA);
        } catch (Throwable $exception) {
            return '';
        }
    }
}
