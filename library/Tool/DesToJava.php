<?php


namespace Tool;


class DesToJava
{
    public static function encrypt($input, $key, $method = 'DES-ECB', $iv = null): string
    {
        $iv = $iv ?: self::createIv();
        return strtoupper(bin2hex(openssl_encrypt($input, $method, $key, OPENSSL_RAW_DATA, $method == 'DES-ECB' ? '' : $iv)));
    }

    public static function createIv(): string
    {
        return self::hexToStr("0000000000000000");
    }

    public static function hexToStr($hex): string
    {
        $string = '';

        for ($i = 0; $i < strlen($hex) - 1; $i += 2) {

            $string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
        }

        return $string;
    }

    public static function decrypt($input, $key, $method = 'DES-ECB', $iv = null)
    {
        $iv = $iv ?: self::createIv();
        $input = self::hexToStr(strtolower($input));
        return openssl_decrypt($input, $method, $key, OPENSSL_RAW_DATA, $method == 'DES-ECB' ? '' : $iv);
    }
}
