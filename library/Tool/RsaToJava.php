<?php


namespace Tool;


use Closure;

class RsaToJava
{
    protected $public_key;
    protected $private_key;
    protected $key_len;

    public $base64EncodeFunc;
    public $base64DecodeFunc;

    public function __construct($pub_key, $pri_key = null, Closure $base64EncodeFunc = null, Closure $base64DecodeFunc = null)
    {
        $this->public_key = $pub_key;
        $this->private_key = $pri_key;

        if ($this->public_key) {

            $pub_id = openssl_get_publickey($this->public_key);
            $this->key_len = openssl_pkey_get_details($pub_id)['bits'];
        }

        if ($this->private_key) {

            $pri_id = openssl_get_privatekey($this->private_key);
            $this->key_len = openssl_pkey_get_details($pri_id)['bits'];
        }

        $this->loadBase64EncodeAndDecodeFunc();
        if ($base64EncodeFunc) {

            $this->base64EncodeFunc = $base64EncodeFunc;
        }

        if ($base64DecodeFunc) {

            $this->base64DecodeFunc = $base64DecodeFunc;
        }
    }

    /*
     * 公钥加密
     */
    public function publicEncrypt($data)
    {
        $encrypted = '';
        $part_len = $this->key_len / 8 - 11;
        $parts = str_split($data, $part_len);

        foreach ($parts as $part) {
            $encrypted_temp = '';
            openssl_public_encrypt($part, $encrypted_temp, $this->public_key);
            $encrypted .= $encrypted_temp;
        }

        return ($this->base64EncodeFunc)($encrypted);
    }

    /*
     * 私钥解密
     */
    public function privateDecrypt($encrypted): string
    {
        if (empty($encrypted)) {

            return "";
        }
        $decrypted = "";
        $part_len = $this->key_len / 8;
        $base64_decoded = ($this->base64DecodeFunc)($encrypted);
        $parts = str_split($base64_decoded, $part_len);
        foreach ($parts as $part) {

            $decrypted_temp = '';
            openssl_private_decrypt($part, $decrypted_temp, $this->private_key);
            $decrypted .= $decrypted_temp;
        }
        return $decrypted;
    }

    /*
     * 私钥加密
     */
    public function privateEncrypt($data)
    {
        $encrypted = '';
        $part_len = $this->key_len / 8 - 11;
        $parts = str_split($data, $part_len);

        foreach ($parts as $part) {
            $encrypted_temp = '';
            openssl_private_encrypt($part, $encrypted_temp, $this->private_key);
            $encrypted .= $encrypted_temp;
        }

        return ($this->base64EncodeFunc)($encrypted);
    }

    /*
     * 公钥解密
     */
    public function publicDecrypt($encrypted): string
    {
        if (empty($encrypted)) {

            return "";
        }
        $decrypted = "";
        $part_len = $this->key_len / 8;
        $base64_decoded = ($this->base64DecodeFunc)($encrypted);
        $parts = str_split($base64_decoded, $part_len);

        foreach ($parts as $part) {
            $decrypted_temp = '';
            openssl_public_decrypt($part, $decrypted_temp, $this->public_key);
            $decrypted .= $decrypted_temp;
        }
        return $decrypted;
    }

    /*
     * 数据加签
     */
    public function sign($data, $algorithm = OPENSSL_ALGO_SHA256)
    {
        openssl_sign($data, $sign, $this->private_key, $algorithm);

        return ($this->base64EncodeFunc)($sign);
    }

    /*
     * 数据签名验证
     */
    public function verify($data, $sign, $algorithm = OPENSSL_ALGO_SHA256)
    {
        $pub_id = openssl_get_publickey($this->public_key);
        return openssl_verify($data, ($this->base64DecodeFunc)($sign), $pub_id, $algorithm);
    }

    public function loadBase64EncodeAndDecodeFunc()
    {
        $this->base64EncodeFunc = function (string $str) {

            return ($this->base64EncodeFunc)($str);
        };
        $this->base64DecodeFunc = function (string $str) {

            return ($this->base64DecodeFunc)($str);
        };
    }
}
