<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-03-26
 * Time: 18:32
 */

namespace Tool;

class Des3
{
    private $paddingBlockSize;
    private $key;
    private $mode;

    public function __construct(string $key, string $mode = 'ecb')
    {
        $this->paddingBlockSize = @mcrypt_get_block_size(MCRYPT_3DES, $mode);
        $this->key = $key;
        $this->mode = $mode;
    }

    public function encrypt(string $input, string $iv = ''): string
    {
        $data = $this->pkcs7Padding($input);
        $encData = @mcrypt_encrypt(MCRYPT_3DES, $this->key, $data, $this->mode, $iv);
        return base64_encode($encData);
    }

    public function pkcs7Padding(string $input): string
    {
        $pad = $this->paddingBlockSize - (strlen($input) % $this->paddingBlockSize);

        return $input . str_repeat(chr($pad), $pad);
    }

    public function decrypt(string $input, string $iv = ''): string
    {
        $data = base64_decode($input);
        $data = @mcrypt_decrypt(MCRYPT_3DES, $this->key, $data, $this->mode, $iv);
        return $this->pkcs7UnPadding($data);
    }

    public function pkcs7UnPadding(string $input)
    {
        $pad = ord($input[strlen($input) - 1]);

        if ($pad > strlen($input)) return false;

        if (strspn($input, chr($pad), strlen($input) - $pad) != $pad) return false;

        return substr($input, 0, -1 * $pad);
    }
}
