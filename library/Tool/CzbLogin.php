<?php


namespace Tool;


use App\Models\Data\AuthConfig as AuthConfigData;
use Illuminate\Support\Facades\Redis;
use Request\CZB as CZBRequest;
use Throwable;

class CzbLogin
{
    /**
     * @param string $phone
     * @param bool $cache 是否缓存token
     * @return mixed
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-02 19:18
     */
    public static function handle(string $phone, bool $cache = true)
    {
        if ($cache) {
            $tokenData = Redis::connection()->get("czb_token_$phone");

            if ($tokenData != '') {

                return $tokenData;
            }
        }

        $resData = CZBRequest::handle("begin/platformLoginSimpleAppV4", [
            'platformCode' => $phone,
            'platformType' => AuthConfigData::getAuthConfigValByName("CZB_PLATFORM_NO")
        ]);

        if ($cache) {
            Redis::connection()->setex("czb_token_$phone", 20 * 86400, $resData['result']['token']);
        }

        return $resData['result']['token'];
    }
}
