replicaCount: 2
env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=prod,product=petroleum-card,appid=oil-adapter-queue
image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/oil-adapter:c992ec0b.2
nameOverride: "oil-adapter-queue"
fullnameOverride: "oil-adapter-queue"
configmap:
  envdata:
    PHPENV: "prod"
    APPLICATION__NAME: "oil-adapter"
    APPLICATION__DEBUG: "true"
    DB__CONNECTION: "mysql"
    DB__HOST: "rm-bp1fy853ye7r0p0o3.mysql.rds.aliyuncs.com"
    DB__PORT: "3306"
    DB__DATABASE: "oil_adapter"
    DB__USERNAME: "oil_adapter_rw"
    DB__PASSWORD: "vpTJmb1nixELdkcq"
    APP__ENV: "prod"
    APP__NAME: "oil-adapter"
    APP__URL: ""
    CACHE__DRIVER: "redis"
    QUEUE_CONNECTION: "redis"
    QUEUE__DRIVER: "redis"
    REDIS__HOST: "r-bp1c2ui1y43d83dzjr.redis.rds.aliyuncs.com"
    REDIS__PASSWORD: "pd6jOWzSXJhu0tb8"
    REDIS__PORT: "6379"
    CRONTAB__ENABLE: "off"
    QUEUE__ENABLE: "on"
    SUPERVISOR__ENABLE: "on"
    RUN__MODE: "remote"
    RUN_SCRIPTS: "1"
    WEBROOT: "/data/web/public"
    PHP_ERRORS_STDERR: "true"
    SKIP_COMPOSER: "on"
    MAIL__DRIVER: "smtp"
    MAIL__HOST: "smtp.feishu.cn"
    MAIL__PORT: "587"
    MAIL__USERNAME: "<EMAIL>"
    MAIL__PASSWORD: "wjS8tLhvQRt2loXn"
    MAIL__ENCRYPTION: "tls"
    MAIL__FROM__NAME: "油品对接"
    MAIL__FROM__ADDRESS: "<EMAIL>"
resources:
  limits:
    cpu: 100m
    memory: 1000Mi
  requests:
    cpu: 50m
    memory: 1024Mi
healthProbes:
  readinessProbe:
    failureThreshold: 3
    exec:
      command:
        - /bin/sh
        - -c
        - ps -ef | grep -v grep | grep artisan
    initialDelaySeconds: 60
    periodSeconds: 2
    successThreshold: 2
    timeoutSeconds: 2
  livenessProbe:
    failureThreshold: 3
    exec:
      command:
        - /bin/sh
        - -c
        - ps -ef | grep -v grep | grep artisan
    initialDelaySeconds: 60
    periodSeconds: 2
    successThreshold: 1
    timeoutSeconds: 2
