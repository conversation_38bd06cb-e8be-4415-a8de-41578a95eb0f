replicaCount: 1
env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=test,product=petroleum-card,appid=oil-adapter-cron
image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/oil-adapter:c992ec0b.2
nameOverride: "oil-adapter-cron"
fullnameOverride: "oil-adapter-cron"
configmap:
  envdata:
    PHPENV: "prod"
    APPLICATION__NAME: "oil-adapter"
    APPLICATION__DEBUG: "true"
    DB__CONNECTION: "mysql"
    DB__HOST: "*************"
    DB__PORT: "3306"
    DB__DATABASE: "oil_adapter"
    DB__USERNAME: "root"
    DB__PASSWORD: "cc509a6f75849b"
    APP__NAME: "oil-adapter"
    APP__URL: ""
    CACHE__DRIVER: "redis"
    QUEUE_CONNECTION: "redis"
    QUEUE__DRIVER: "redis"
    REDIS__HOST: "*************"
    REDIS__PASSWORD: "123456"
    REDIS__PORT: "6382"
    REDIS__PREFIX: "adapter_"
    CRONTAB__ENABLE: "on"
    QUEUE__ENABLE: "off"
    SUPERVISOR__ENABLE: "on"
    RUN__ENVIRONMENT: "test"
    RUN__MODE: "remote"
    RUN_SCRIPTS: "1"
    WEBROOT: "/data/web/public"
    PHP_ERRORS_STDERR: "true"
    SKIP_COMPOSER: "on"
    MAIL__DRIVER: "smtp"
    MAIL__HOST: "smtp.feishu.cn"
    MAIL__PORT: "587"
    MAIL__USERNAME: "<EMAIL>"
    MAIL__PASSWORD: "wjS8tLhvQRt2loXn"
    MAIL__ENCRYPTION: "tls"
    MAIL__FROM__NAME: "油品对接"
    MAIL__FROM__ADDRESS: "<EMAIL>"
resources:
  limits:
    cpu: 100m
    memory: 1000Mi
  requests:
    cpu: 50m
    memory: 500Mi
