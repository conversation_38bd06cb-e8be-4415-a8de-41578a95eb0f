replicaCount: 2
env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=demo,product=petroleum-card,appid=oil-adapter-queue
image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/oil-adapter:c992ec0b.2
nameOverride: "oil-adapter-queue"
fullnameOverride: "oil-adapter-queue"
configmap:
  envdata:
    PHPENV: "prod"
    APPLICATION__NAME: "oil_adapter"
    DB__CONNECTION: "mysql"
    DB__HOST: "************"
    DB__PORT: "3306"
    DB__DATABASE: "oil_adapter"
    DB__USERNAME: "oil_adapter"
    DB__PASSWORD: "oil_adapter@mysql"
    APP__NAME: "oil-adapter"
    APP__ENV: "demo"
    APP__DEBUG: "true"
    APP__URL: ""
    CACHE__DRIVER: ""
    QUEUE__DRIVER: ""
    QUEUE_CONNECTION: ""
    REDIS__HOST: "************"
    REDIS__PASSWORD: "cae0f7fcf1"
    REDIS__PORT: "6379"
    RUN__ENVIRONMENT: "demo"
    RUN__MODE: "remote"
    CRONTAB__ENABLE: "off"
    QUEUE__ENABLE: "off"
    SUPERVISOR__ENABLE: "off"
    RUN_SCRIPTS: "1"
    WEBROOT: "/data/web/public"
    PHP_ERRORS_STDERR: "true"
    MAIL__DRIVER: "smtp"
    MAIL__HOST: "smtp.feishu.cn"
    MAIL__PORT: "587"
    MAIL__USERNAME: "<EMAIL>"
    MAIL__PASSWORD: "wjS8tLhvQRt2loXn"
    MAIL__ENCRYPTION: "tls"
    MAIL__FROM__NAME: "油品对接"
    MAIL__FROM__ADDRESS: "<EMAIL>"
resources:
  limits:
    cpu: 100m
    memory: 1000Mi
  requests:
    cpu: 50m
    memory: 200Mi
