<?php

use App\Http\Middleware\Callback\ADAuthenticate;
use App\Http\Middleware\Callback\CNPCAuthenticate;
use App\Http\Middleware\Callback\DHAuthenticate;
use App\Http\Middleware\Callback\DTAuthenticate;
use App\Http\Middleware\Callback\FYAuthenticate;
use App\Http\Middleware\Callback\GAODENGAuthenticate;
use App\Http\Middleware\Callback\GBAuthenticate;
use App\Http\Middleware\Callback\GBTJAuthenticate;
use App\Http\Middleware\Callback\GDQPAuthenticate;
use App\Http\Middleware\Callback\HBKJAuthenticate;
use App\Http\Middleware\Callback\HSYAuthenticate;
use App\Http\Middleware\Callback\JHAuthenticate;
use App\Http\Middleware\Callback\JHCXAuthenticate;
use App\Http\Middleware\Callback\JQAuthenticate;
use App\Http\Middleware\Callback\JTAuthenticate;
use App\Http\Middleware\Callback\JTXAuthenticate;
use App\Http\Middleware\Callback\MTLSYAuthenticate;
use App\Http\Middleware\Callback\QKAuthenticate;
use App\Http\Middleware\Callback\SAICSTATIONAuthenticate;
use App\Http\Middleware\Callback\SAICSTRADEAuthenticate;
use App\Http\Middleware\Callback\SCQPAuthenticate;
use App\Http\Middleware\Callback\SHAuthenticate;
use App\Http\Middleware\Callback\SHSXAuthenticate;
use App\Http\Middleware\Callback\SQZLAuthenticate;
use App\Http\Middleware\Callback\SQZSHAuthenticate;
use App\Http\Middleware\Callback\TBJXAuthenticate;
use App\Http\Middleware\Callback\WJYAuthenticate;
use App\Http\Middleware\Callback\XMSKAuthenticate;
use App\Http\Middleware\Callback\XYNAuthenticate;
use App\Http\Middleware\Callback\YCQPAuthenticate;
use App\Http\Middleware\Callback\YGYAuthenticate;
use App\Http\Middleware\Callback\YUNDATONGAuthenticate;
use App\Http\Middleware\Callback\ZDCAuthenticate;
use App\Http\Middleware\Callback\ZHUOYIQAuthenticate;
use App\Http\Middleware\Callback\ZHUOYIYAuthenticate;
use App\Http\Middleware\Callback\ZHYKAuthenticate;
use App\Http\Middleware\Callback\ZWLAuthenticate;
use Laravel\Lumen\Routing\Router;

/**
 * 自定义路由
 */
//宝兑通订单回调路由
/** @var Router $router */
$router->get('baoduitong', 'Api\CallbackController@bdt');

/**
 * 自定义路由
 */
//GOS回调
$router->get('gos', 'Api\CallbackController@gos');

/**
 * 自定义路由
 */
//车主邦退款接口路由
$router->post('refundForCzb', 'Api\CallbackController@refundForCzb');

/**
 * 广东壳牌路由
 */
$router->group(['prefix' => 'yc_qp', 'middleware' => YCQPAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/query', 'Api\OrderController@query');
    });
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');
    });
});

/**
 * 四川壳牌路由
 */
$router->group(['prefix' => 'sc_qp', 'middleware' => SCQPAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'v1'], function () use ($router) {
        $router->post('/purchase', 'Api\OrderController@query');
    });
    $router->group(['prefix' => 'v1'], function () use ($router) {
        $router->post('/reversal', 'Api\TradeController@refund');
    });
});

/**
 * 健康检查路由
 */
$router->post('healthCheck', 'Api\CallbackController@healthCheck');

//惠运通事件推送
$router->post('hyt', 'Api\CallbackController@hyt');

//万金油
$router->group(['prefix' => 'wjy', 'middleware' => WJYAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/toBePaid', 'Api\OrderController@toBePaid');
    });
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });
    $router->group(['prefix' => 'code'], function () use ($router) {
        $router->post('/parse', 'Api\CodeController@parse');
    });
});

//上汽
$router->group(['prefix' => 'saic'], function () use ($router) {
    $router->group(['prefix' => 'trade', 'middleware' => SAICSTRADEAuthenticate::class], function () use ($router) {
        $router->group(['prefix' => 'pay'], function () use ($router) {
            $router->post('/aj', 'Api\TradeController@pay');
            $router->post('/fl', 'Api\TradeController@pay');
            $router->post('/ajsw', 'Api\TradeController@pay');
            $router->post('/yc', 'Api\TradeController@pay');
        });
    });

    $router->group(['prefix' => 'station', 'middleware' => SAICSTATIONAuthenticate::class], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//固本能源
$router->group(['prefix' => 'gb_energy', 'middleware' => GBAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//河北中石油
$router->group(['prefix' => 'cnpc', 'middleware' => CNPCAuthenticate::class], function () use ($router) {
    $router->post('/voucher_verification_result', 'Api\TradeController@pay');
});

//DT
$router->group(['prefix' => 'dt', 'middleware' => DTAuthenticate::class], function () use ($router) {
    $router->post('/notify', 'Api\CallbackController@dt');

    $router->post('/preAuthToTrade', 'Api\TradeController@pay');
});

//YGY
$router->group(['prefix' => 'ygy', 'middleware' => YGYAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/queryAll', 'Api\StationController@queryAll');
    });
    $router->group(['prefix' => 'company'], function () use ($router) {
        $router->post('/query', 'Api\AccountController@query');

        $router->post('/receive', 'Api\AccountController@companyReceive');
    });
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/toBePaid', 'Api\OrderController@toBePaid');

        $router->post('/pay', 'Api\TradeController@pay');

        $router->post('/queryFlow', 'Api\OrderController@queryFlow');

        $router->post('/query', 'Api\OrderController@query');

        $router->post('/getSecondaryPaymentCertificate', 'Api\OrderController@getSecondaryPaymentCertificate');

        $router->post('/refundCallback', 'Api\OrderController@refundCallback');
    });
});

//聚合出行
$router->group(['prefix' => 'jhcx', 'middleware' => JHCXAuthenticate::class], function () use ($router) {
    $router->post('', 'Api\CallbackController@jhcx');
});

//华商云
$router->group(['prefix' => 'hsy', 'middleware' => HSYAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');

        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//上汽直联
$router->group(['prefix' => 'sqzl', 'middleware' => SQZLAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
        $router->post('/pay', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//HBKJ
$router->group(['prefix' => 'hbkj', 'middleware' => HBKJAuthenticate::class], function () use ($router) {
    $router->post('/coupon_verification_result', 'Api\TradeController@pay');
});

/**
 * 广东壳牌(新)路由
 */
$router->group(['prefix' => 'gd_qp', 'middleware' => GDQPAuthenticate::class], function () use ($router) {
    $router->post('/purchase', 'Api\TradeController@pay');
    $router->post('/reversal', 'Api\TradeController@refund');
});

//固本能源(天津)
$router->group(['prefix' => 'gb_energy_tj', 'middleware' => GBTJAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//智慧油客
$router->group(['prefix' => 'zhyk', 'middleware' => ZHYKAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//掌多车
$router->group(['prefix' => 'zdc', 'middleware' => ZDCAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
        $router->post('/reversal', 'Api\TradeController@refund');
        $router->post('/refundCallback', 'Api\TradeController@refund');
        $router->post('/purchase', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//安得
$router->group(['prefix' => 'ad', 'middleware' => ADAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/queryAll', 'Api\StationController@queryAll');
        $router->post('/query', 'Api\StationController@queryOne');
    });

    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/toBePaid', 'Api\OrderController@toBePaid');
        $router->post('/cancel', 'Api\OrderController@cancel');
        $router->post('/reToBePaidCallback', 'Api\TradeController@reToBePaidCallback');
    });

    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'bill'], function () use ($router) {
        $router->post('/query', 'Api\BillController@query');
    });
});

//中物流 回调的安全访问控制需通过公司WAF做IP白名单限制->曾友
$router->group(['prefix' => 'zwl', 'middleware' => ZWLAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/statusChangeCallback', 'Api\TradeController@pay');
    });
});

//星油(新)
$router->group(['prefix' => 'xyn', 'middleware' => XYNAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//上汽中石化
$router->group(['prefix' => 'sqZsh', 'middleware' => SQZSHAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
        $router->post('/pay', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/statusChange', 'Api\OrderController@statusChange');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//梦驼铃(上游) 回调的安全访问控制需通过公司WAF做IP白名单限制->曾友
$router->group(['prefix' => 'mtlSy', 'middleware' => MTLSYAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//善宏
$router->group(['prefix' => 'sh', 'middleware' => SHAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/receiveWrittenOffResult', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});
//善宏(山西)
$router->group(['prefix' => 'shsx', 'middleware' => SHSXAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/receiveWrittenOffResult', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//江投
$router->group(['prefix' => 'jt', 'middleware' => JTAuthenticate::class], function () use ($router) {
    $router->post('/callback', 'Api\CallbackController@jt');
});

//九和
$router->group(['prefix' => 'jh', 'middleware' => JHAuthenticate::class], function () use ($router) {
    $router->post('/callback', 'Api\CallbackController@jh');
});

//汽开
$router->group(['prefix' => 'qk', 'middleware' => QKAuthenticate::class], function () use ($router) {
    $router->post('/callback', 'Api\CallbackController@jh');
});

//江投(新)
$router->group(['prefix' => 'jtx', 'middleware' => JTXAuthenticate::class], function () use ($router) {
    $router->post('/callback', 'Api\CallbackController@jh');
});

//东华
$router->group(['prefix' => 'dh', 'middleware' => DHAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');
    });
});

//象盟数科
$router->group(['prefix' => 'xmsk', 'middleware' => XMSKAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/statusChange', 'Api\OrderController@statusChange');
    });
    $router->group(['prefix' => 'order'], function () use ($router) {
        $router->post('/receiveWrittenOffResult', 'Api\TradeController@pay');
    });

    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//运达通
$router->group(['prefix' => 'yundatong', 'middleware' => YUNDATONGAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });
});

//高灯
$router->group(['prefix' => 'gaodeng', 'middleware' => GAODENGAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });
});

//通宝吉祥
$router->group(['prefix' => 'tbjx', 'middleware' => TBJXAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
    });
});

//鲸启
$router->group(['prefix' => 'jq', 'middleware' => JQAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/refund', 'Api\TradeController@refund');
        $router->post('/refundCallback', 'Api\TradeController@refund');
    });
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//卓壹(油)
$router->group(['prefix' => 'zhuoyi', 'middleware' => ZHUOYIYAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');
    });
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});
//卓壹(气)
$router->group(['prefix' => 'zhuoyiq', 'middleware' => ZHUOYIQAuthenticate::class], function () use ($router) {
    $router->group(['prefix' => 'trade'], function () use ($router) {
        $router->post('/pay', 'Api\TradeController@pay');
    });
    $router->group(['prefix' => 'station'], function () use ($router) {
        $router->post('/receive', 'Api\StationController@receive');
    });
});

//福佑
$router->group(['prefix' => 'fy', 'middleware' => FYAuthenticate::class], function () use ($router) {
    $router->post('/saveTradeInfo', 'Api\OrderController@toBePaid');
    $router->post('/getTradeInfo', 'Api\OrderController@getSecondaryPaymentCertificate');
    $router->post('/delTrade', 'Api\OrderController@refundApplication');
    $router->post('/getAccountInfo', 'Api\AccountController@query');
    $router->post('/selectAccountRecordList', 'Api\AccountController@changeRecord');
});