<?php

use App\Http\Middleware\AfterMiddleware;
use App\Http\Middleware\BeforeMiddleware;
use Faker\Provider\Uuid;
use Illuminate\Mail\MailServiceProvider;

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/Functions/function.php';

try {
    (new Dotenv\Dotenv(dirname(__DIR__)))->load();
} catch (Dotenv\Exception\InvalidPathException $e) {
    //
}

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| Here we will load the environment and create the application instance
| that serves as the central piece of this framework. We'll use this
| application as an "IoC" container and router for this framework.
|
*/

$app = new Laravel\Lumen\Application(
    dirname(__DIR__)
);

$app->withFacades();

$app->withEloquent();

/*
|--------------------------------------------------------------------------
| Register Container Bindings
|--------------------------------------------------------------------------
|
| Now we will register a few bindings in the service container. We will
| register the exception handler and the console kernel. You may add
| your own bindings here if you like or you can make another file.
|
*/

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->register(Illuminate\Session\SessionServiceProvider::class);
$app->register(Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
$app->register(Wn\Generators\CommandsServiceProvider::class);
$app->register(Illuminate\Redis\RedisServiceProvider::class);
$app->register(MailServiceProvider::class);
$app->register(App\Providers\EventServiceProvider::class);

$app->alias('session', 'Illuminate\Session\SessionManager');

/*
|--------------------------------------------------------------------------
| Register Middleware
|--------------------------------------------------------------------------
|
| Next, we will register the middleware with the application. These can
| be global middleware that run before and after each request into a
| route or middleware that'll be assigned to some specific routes.
|
*/

$app->middleware([
    BeforeMiddleware::class,
    AfterMiddleware::class,
]);

// $app->routeMiddleware([
//     'auth' => App\Http\Middleware\Authenticate::class,
// ]);

/*
|--------------------------------------------------------------------------
| Register Service Providers
|--------------------------------------------------------------------------
|
| Here we will register all of the application's service providers which
| are used to bind services into the container. Service providers are
| totally optional, so you are not required to uncomment this line.
|
*/

// $app->register(App\Providers\AppServiceProvider::class);
// $app->register(App\Providers\AuthServiceProvider::class);
// $app->register(App\Providers\EventServiceProvider::class);

/*
|--------------------------------------------------------------------------
| Load The Application Routes
|--------------------------------------------------------------------------
|
| Next we will include the routes file so that they can all be added to
| the application. This will provide all of the URLs the application
| can respond to, as well as the controllers that may handle them.
|
*/

$app->router->group([
    'namespace' => 'App\Http\Controllers',
], function ($router) {
    require __DIR__ . '/../routes/api.php';
    require __DIR__ . '/../routes/admin.php';
    require __DIR__ . '/../routes/customer.php';
});

/*
|--------------------------------------------------------------------------
| Register Custom Config
|--------------------------------------------------------------------------
|
*/

if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    $app->configure('view');
}

$app->configure("error");
$app->configure("database");
$app->configure("queue");
$app->configure("role");
$app->configure('session');
$app->configure('template');
$app->configure('brand');
$app->configure('mail');
$app->configure('machine');
$app->configure('other');
$app->configure("logging");
$app->configure("station_type");
$app->configure("fixed_payment_certificate");

//根据环境区分设置油品配置
createOilConfig();
$app->configure("oil");

//生成链路ID
global $routeId;
$routeId = $routeId ?? Uuid::uuid();

//定义GOS SDK配置文件
if(!defined('GOS_SDK_CONFIG'))
    define("GOS_SDK_CONFIG", base_path("config/gos.php"));

return $app;
