{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "edc2f782aab1a75030fc3a1a2765c69b", "packages": [{"name": "bacon/bacon-qr-code", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "eaac909da3ccc32b748a65b127acd8918f58d9b0"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/bacon/bacon-qr-code/bacon-bacon-qr-code-eaac909da3ccc32b748a65b127acd8918f58d9b0-zip-89eef4.tar", "reference": "eaac909da3ccc32b748a65b127acd8918f58d9b0", "shasum": "f82805fdc977daabb729b26b4f8952d7c1184688"}, "require": {"dasprid/enum": "^1.0", "ext-iconv": "*", "php": "^7.1"}, "require-dev": {"phly/keep-a-changelog": "^1.4", "phpunit/phpunit": "^6.4", "squizlabs/php_codesniffer": "^3.1"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "time": "2018-04-25T17:53:56+00:00"}, {"name": "barryvdh/laravel-ide-helper", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-ide-helper.git", "reference": "5f677edc14bdcfdcac36633e6eea71b2728a4dbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-ide-helper/zipball/5f677edc14bdcfdcac36633e6eea71b2728a4dbc", "reference": "5f677edc14bdcfdcac36633e6eea71b2728a4dbc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"barryvdh/reflection-docblock": "^2.0.6", "composer/composer": "^1.6", "doctrine/dbal": "~2.3", "illuminate/console": "^5.5|^6|^7", "illuminate/filesystem": "^5.5|^6|^7", "illuminate/support": "^5.5|^6|^7", "php": ">=7.2"}, "require-dev": {"illuminate/config": "^5.5|^6|^7", "illuminate/view": "^5.5|^6|^7", "mockery/mockery": "^1.3", "orchestra/testbench": "^3|^4|^5", "phpro/grumphp": "^0.17.1", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}, "laravel": {"providers": ["Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider"]}}, "autoload": {"psr-4": {"Barryvdh\\LaravelIdeHelper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Laravel IDE Helper, generates correct PHPDocs for all Facade classes, to improve auto-completion.", "keywords": ["autocomplete", "codeintel", "helper", "ide", "laravel", "netbeans", "phpdoc", "phpstorm", "sublime"], "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2020-04-22T09:57:26+00:00"}, {"name": "barryvdh/reflection-docblock", "version": "v2.0.6", "source": {"type": "git", "url": "https://github.com/barryvdh/ReflectionDocBlock.git", "reference": "6b69015d83d3daf9004a71a89f26e27d27ef6a16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/ReflectionDocBlock/zipball/6b69015d83d3daf9004a71a89f26e27d27ef6a16", "reference": "6b69015d83d3daf9004a71a89f26e27d27ef6a16", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0,<4.5"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Barryvdh": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2018-12-13T10:34:14+00:00"}, {"name": "bluem/tree", "version": "3.1", "source": {"type": "git", "url": "https://github.com/BlueM/Tree.git", "reference": "e520150cc713b4318c6ceae05d578462d930bc59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BlueM/Tree/zipball/e520150cc713b4318c6ceae05d578462d930bc59", "reference": "e520150cc713b4318c6ceae05d578462d930bc59", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.0"}, "require-dev": {"doctrine/instantiator": "1.0.*", "phpunit/phpunit": "6.*"}, "type": "library", "autoload": {"psr-4": {"BlueM\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for handling tree structures based on parent IDs", "homepage": "https://github.com/BlueM/Tree", "keywords": ["hierarchical", "tree"], "time": "2019-09-15T06:11:51+00:00"}, {"name": "codeitnowin/barcode", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/codeitnowin/barcode-generator.git", "reference": "68e6f0f9eac16a20e20ed0e8258d4d9765800878"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/codeitnowin/barcode-generator/zipball/68e6f0f9eac16a20e20ed0e8258d4d9765800878", "reference": "68e6f0f9eac16a20e20ed0e8258d4d9765800878", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "php": ">=5.3.2"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"CodeItNow\\": "CodeItNow/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "er.akhtar<PERSON><EMAIL>"}], "description": "Barcode & Qr Code generator library by http://www.codeitnow.in. You can use it with Custom PHP application or any PHP Framework such as Laravel, Cakephp, Yii, Codeigneter etc.", "homepage": "http://www.codeitnow.in", "keywords": ["Symfony2", "barcode", "cakephp", "code", "generator", "laravel", "qr", "qrcode", "symfony"], "support": {"issues": "https://github.com/codeitnowin/barcode-generator/issues", "source": "https://github.com/codeitnowin/barcode-generator/tree/v3.0.6"}, "abandoned": "barcode-bakery/barcode-1d", "time": "2021-08-20T15:46:10+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/composer/ca-bundle/composer-ca-bundle-95c63ab2117a72f48f5a55da9740a3273d45b7fd-zip-9ba602.tar", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd", "shasum": "92ed4c6f70679249875c751b0132c92672928910"}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-04-08T08:27:21+00:00"}, {"name": "composer/composer", "version": "1.10.6", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "be81b9c4735362c26876bdbfd3b5bc7e7f711c88"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/composer/composer/composer-composer-be81b9c4735362c26876bdbfd3b5bc7e7f711c88-zip-f11d9c.tar", "reference": "be81b9c4735362c26876bdbfd3b5bc7e7f711c88", "shasum": "33ae95499484b2b709b594eac4d7c4c07aa227dd"}, "require": {"composer/ca-bundle": "^1.0", "composer/semver": "^1.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^1.1", "justinrainbow/json-schema": "^3.0 || ^4.0 || ^5.0", "php": "^5.3.2 || ^7.0", "psr/log": "^1.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/finder": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/process": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "conflict": {"symfony/console": "2.8.38", "symfony/phpunit-bridge": "3.4.40"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^3.4"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-05-06T08:28:10+00:00"}, {"name": "composer/semver", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/composer/semver/composer-semver-c6bea70230ef4dd483e6bbcab6005f682ed3a8de-zip-e0fe47.tar", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "shasum": "c915b480d7fd26f8ef904704d4ab2be2fa27c474"}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2020-01-13T12:06:48+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "0c3e51e1880ca149682332770e25977c70cf9dae"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/composer/spdx-licenses/composer-spdx-licenses-0c3e51e1880ca149682332770e25977c70cf9dae-zip-4a09cc.tar", "reference": "0c3e51e1880ca149682332770e25977c70cf9dae", "shasum": "95ff190c3c88dbd4484da14b6b6526fc0288e7b3"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2020-02-14T07:44:31+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "1ab9842d69e64fb3a01be6b656501032d1b78cb7"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/composer/xdebug-handler/composer-xdebug-handler-1ab9842d69e64fb3a01be6b656501032d1b78cb7-zip-6f4a82.tar", "reference": "1ab9842d69e64fb3a01be6b656501032d1b78cb7", "shasum": "f33027c32b62d68776ca24c3d33e8ce8299e925b"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "funding": [{"url": "https://packagist.com", "type": "custom"}], "time": "2020-03-01T12:26:26+00:00"}, {"name": "dasprid/enum", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "631ef6e638e9494b0310837fa531bedd908fc22b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/dasprid/enum/dasprid-enum-631ef6e638e9494b0310837fa531bedd908fc22b-zip-f6ef6d.tar", "reference": "631ef6e638e9494b0310837fa531bedd908fc22b", "shasum": "f7b8f7fffb42ea683e0c35dd408e208d7e6764e2"}, "require-dev": {"phpunit/phpunit": "^6.4", "squizlabs/php_codesniffer": "^3.1"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "time": "2017-10-25T22:45:27+00:00"}, {"name": "doctrine/cache", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "35a4a70cd94e09e2259dfae7488afc6b474ecbd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/35a4a70cd94e09e2259dfae7488afc6b474ecbd3", "reference": "35a4a70cd94e09e2259dfae7488afc6b474ecbd3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2020-05-27T16:24:54+00:00"}, {"name": "doctrine/dbal", "version": "2.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "aab745e7b6b2de3b47019da81e7225e14dcfdac8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/aab745e7b6b2de3b47019da81e7225e14dcfdac8", "reference": "aab745e7b6b2de3b47019da81e7225e14dcfdac8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "jetbrains/phpstorm-stubs": "^2019.1", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.4.1", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.11"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev", "dev-develop": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2020-04-20T17:19:26+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "629572819973f13486371cb611386eb17851e85c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/629572819973f13486371cb611386eb17851e85c", "reference": "629572819973f13486371cb611386eb17851e85c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "time": "2019-11-10T09:48:07+00:00"}, {"name": "doctrine/inflector", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "889b42b8155f2aa274596b6e0424371b1e3c51bb"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/doctrine/inflector/doctrine-inflector-889b42b8155f2aa274596b6e0424371b1e3c51bb-zip-b01377.tar", "reference": "889b42b8155f2aa274596b6e0424371b1e3c51bb", "shasum": "a529490383455beb89fee3e228b22c8bc2c59da5"}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector", "Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2020-05-25T20:02:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/doctrine/instantiator/doctrine-instantiator-ae466f726242e637cebdd526a7d991b9433bacf1-zip-10f3b0.tar", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1", "shasum": "69bd49c310b11c86b393d83a2c0f6ac752a766fe"}, "require": {"php": "^7.1"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2019-10-21T16:45:58+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/doctrine/lexer/doctrine-lexer-e864bbf5904cb8f5bb334f99209b48018522f042-zip-dfa769.tar", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": "252465dca3be112478ced5e83cd6d5afb58255bd"}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/72b6fbf76adb3cf5bc0db68559b33d41219aba27", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "time": "2019-03-31T00:38:28+00:00"}, {"name": "egulias/email-validator", "version": "2.1.17", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/egulias/email-validator/egulias-email-validator-ade6887fd9bd74177769645ab5c474824f8a418a-zip-daafaf.tar", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a", "shasum": "b6fa7758a31cd77946ef1fc718ebea998c63e18c"}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-02-13T22:36:52+00:00"}, {"name": "endroid/qr-code", "version": "3.8.1", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "a7e07d26fad46d7032b39a076f6c85e07757028d"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/endroid/qr-code/endroid-qr-code-a7e07d26fad46d7032b39a076f6c85e07757028d-zip-4366b1.tar", "reference": "a7e07d26fad46d7032b39a076f6c85e07757028d", "shasum": "0f0666f0db9d2b4b2997b4f0a8a50b4e7a930cdb"}, "require": {"bacon/bacon-qr-code": "^2.0", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^1.0.2", "myclabs/php-enum": "^1.5", "php": ">=7.2", "symfony/options-resolver": "^3.4||^4.4||^5.0", "symfony/property-access": "^3.4||^4.4||^5.0"}, "require-dev": {"endroid/quality": "dev-master"}, "suggest": {"roave/security-advisories": "Avoids installation of package versions with vulnerabilities", "symfony/security-checker": "Checks your composer.lock for vulnerabilities"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["bundle", "code", "endroid", "php", "qr", "qrcode"], "funding": [{"url": "https://github.com/endroid", "type": "github"}], "time": "2020-06-02T20:10:27+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2019-12-30T22:54:17+00:00"}, {"name": "evit/php-gm-crypto", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/Endy-c/php-gm-crypto.git", "reference": "186e03ce9bb13932ce0d91396e11c67b59bce78a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Endy-c/php-gm-crypto/zipball/186e03ce9bb13932ce0d91396e11c67b59bce78a", "reference": "186e03ce9bb13932ce0d91396e11c67b59bce78a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "autoload": {"psr-4": {"Evit\\PhpGmCrypto\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fully compatible with the sm4-cbc and sm4-ecb state secret SM algorithms of openssl. When openssl >= 1.1.1 supports the state secret algorithm, directly call openssl for SM4 encryption and decryption, otherwise call the custom algorithm.", "support": {"issues": "https://github.com/Endy-c/php-gm-crypto/issues", "source": "https://github.com/Endy-c/php-gm-crypto/tree/1.0.4"}, "time": "2023-06-02T03:13:44+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.1", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/fc10d778e4b84d5bd315dad194661e091d307c6f", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "time": "2019-12-12T13:22:17+00:00"}, {"name": "gregwar/captcha", "version": "v1.1.8", "source": {"type": "git", "url": "https://github.com/Gregwar/Captcha.git", "reference": "6088ad3db59bc226423ad1476a9f0424b19b1866"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/gregwar/captcha/gregwar-captcha-6088ad3db59bc226423ad1476a9f0424b19b1866-zip-3e4502.tar", "reference": "6088ad3db59bc226423ad1476a9f0424b19b1866", "shasum": "45082268e594a97e1bde16b8252555f30ed9e229"}, "require": {"ext-gd": "*", "ext-mbstring": "*", "php": ">=5.3.0", "symfony/finder": "*"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "<PERSON><PERSON>a", "autoload": {"psr-4": {"Gregwar\\": "src/<PERSON><PERSON>"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.gregwar.com/"}, {"name": "<PERSON>", "email": "jeremy.j.living<PERSON>@gmail.com"}], "description": "Captcha generator", "homepage": "https://github.com/Gregwar/Captcha", "keywords": ["bot", "<PERSON><PERSON>a", "spam"], "time": "2020-01-22T14:54:02+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.4", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/guzzlehttp/guzzle/guzzlehttp-guzzle-a4a1b6930528a8f7ee03518e6442ec7a44155d9d-zip-066034.tar", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "shasum": "a4fd9fb0f9e5ebd73c61d208889fe89969191e51"}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-05-25T19:35:05+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "776503d3a8e85d4f9a1148614f95b7a608b046ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/776503d3a8e85d4f9a1148614f95b7a608b046ad", "reference": "776503d3a8e85d4f9a1148614f95b7a608b046ad", "shasum": ""}, "require": {"php": "^5.3|^7.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "1.3.3", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "time": "2016-01-20T08:20:44+00:00"}, {"name": "hyr/gos-sdk", "version": "0.0.1", "source": {"type": "git", "url": "***********************:hyr/GosSDK.git", "reference": "f6387949617acd8349d94486819feeda7f06712c"}, "require": {"guzzlehttp/guzzle": "^6.2"}, "type": "library", "autoload": {"psr-4": {"GosSDK\\": "src"}}, "license": ["private"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "对接Gos系统之SDK", "time": "2019-09-02T10:28:07+00:00"}, {"name": "illuminate/auth", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/auth.git", "reference": "2e5bfa737ed0e7c20075bfcd3fffd79f7304c509"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/auth/zipball/2e5bfa737ed0e7c20075bfcd3fffd79f7304c509", "reference": "2e5bfa737ed0e7c20075bfcd3fffd79f7304c509", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/http": "5.7.*", "illuminate/queue": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "suggest": {"illuminate/console": "Required to use the auth:clear-resets command (5.7.*).", "illuminate/queue": "Required to fire login / logout events (5.7.*).", "illuminate/session": "Required to use the session based guard (5.7.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Auth\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Auth package.", "homepage": "https://laravel.com", "time": "2019-02-26T00:28:01+00:00"}, {"name": "illuminate/broadcasting", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/broadcasting.git", "reference": "c2f2dff2c00942d0469e39797022f2fc6da68f5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/broadcasting/zipball/c2f2dff2c00942d0469e39797022f2fc6da68f5a", "reference": "c2f2dff2c00942d0469e39797022f2fc6da68f5a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/bus": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/queue": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "psr/log": "^1.0"}, "suggest": {"pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Broadcasting\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Broadcasting package.", "homepage": "https://laravel.com", "time": "2019-02-24T15:03:48+00:00"}, {"name": "illuminate/bus", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "5cad4bff635071ff07c317aad200a8c2169f264f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/5cad4bff635071ff07c317aad200a8c2169f264f", "reference": "5cad4bff635071ff07c317aad200a8c2169f264f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/pipeline": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/cache", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/cache.git", "reference": "7998b137cc723dd5e68846ada33f8c0143f5b10f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/cache/zipball/7998b137cc723dd5e68846ada33f8c0143f5b10f", "reference": "7998b137cc723dd5e68846ada33f8c0143f5b10f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "suggest": {"illuminate/database": "Required to use the database cache driver (5.7.*).", "illuminate/filesystem": "Required to use the file cache driver (5.7.*).", "illuminate/redis": "Required to use the redis cache driver (5.7.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Cache package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/config", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/config.git", "reference": "540e11b9ae058c9a94051d9ca6c02e40258c71fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/config/zipball/540e11b9ae058c9a94051d9ca6c02e40258c71fd", "reference": "540e11b9ae058c9a94051d9ca6c02e40258c71fd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Config\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Config package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/console", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/console.git", "reference": "0d97b6ead0cbb09140b1e8317f5a9d9f69ff9ec6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/console/zipball/0d97b6ead0cbb09140b1e8317f5a9d9f69ff9ec6", "reference": "0d97b6ead0cbb09140b1e8317f5a9d9f69ff9ec6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "symfony/console": "^4.1"}, "suggest": {"dragonmantank/cron-expression": "Required to use scheduling component (^2.0).", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (^6.0).", "symfony/process": "Required to use scheduling component (^4.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Console package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/container", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "8c3a75e464d59509ae88db152cab61a3f115b9ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/8c3a75e464d59509ae88db152cab61a3f115b9ec", "reference": "8c3a75e464d59509ae88db152cab61a3f115b9ec", "shasum": ""}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "psr/container": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/contracts", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "b63324d349a8ae2156fbc2697c1ccc85879b3803"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/b63324d349a8ae2156fbc2697c1ccc85879b3803", "reference": "b63324d349a8ae2156fbc2697c1ccc85879b3803", "shasum": ""}, "require": {"php": "^7.1.3", "psr/container": "^1.0", "psr/simple-cache": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2019-02-12T07:46:48+00:00"}, {"name": "illuminate/database", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "c0702cb8c665cab8d080a81de5a44ac672b26d62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/c0702cb8c665cab8d080a81de5a44ac672b26d62", "reference": "c0702cb8c665cab8d080a81de5a44ac672b26d62", "shasum": ""}, "require": {"illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "fzaninotto/faker": "Required to use the eloquent factory builder (^1.4).", "illuminate/console": "Required to use the database commands (5.7.*).", "illuminate/events": "Required to use the observers with Eloquent (5.7.*).", "illuminate/filesystem": "Required to use the migrations (5.7.*).", "illuminate/pagination": "Required to paginate the result set (5.7.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "time": "2019-02-20T03:55:15+00:00"}, {"name": "illuminate/encryption", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/encryption.git", "reference": "57bfd9d6016e3c33895b5c3b3bad0458f9d9c3eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/encryption/zipball/57bfd9d6016e3c33895b5c3b3bad0458f9d9c3eb", "reference": "57bfd9d6016e3c33895b5c3b3bad0458f9d9c3eb", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Encryption\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Encryption package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/events", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "e48888062a9962f30c431524357b9a815b093609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/e48888062a9962f30c431524357b9a815b093609", "reference": "e48888062a9962f30c431524357b9a815b093609", "shasum": ""}, "require": {"illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/filesystem", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "ff853e678a93996b1d0a3ddc6fc56c10bae0de30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/ff853e678a93996b1d0a3ddc6fc56c10bae0de30", "reference": "ff853e678a93996b1d0a3ddc6fc56c10bae0de30", "shasum": ""}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "symfony/finder": "^4.1"}, "suggest": {"league/flysystem": "Required to use the Flysystem local and FTP drivers (^1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/hashing", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/hashing.git", "reference": "c56e2e6cedadeddb677702820bec3c08097b9e44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/hashing/zipball/c56e2e6cedadeddb677702820bec3c08097b9e44", "reference": "c56e2e6cedadeddb677702820bec3c08097b9e44", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Hashing\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Hashing package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/http", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/http.git", "reference": "279984eed495fc19a5649e4e8f57c5076e406f0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/http/zipball/279984eed495fc19a5649e4e8f57c5076e406f0a", "reference": "279984eed495fc19a5649e4e8f57c5076e406f0a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/session": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "symfony/http-foundation": "^4.1", "symfony/http-kernel": "^4.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Http package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/log", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/log.git", "reference": "4db43591e3216a4893e4b17855dc077ffcfbad19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/log/zipball/4db43591e3216a4893e4b17855dc077ffcfbad19", "reference": "4db43591e3216a4893e4b17855dc077ffcfbad19", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "monolog/monolog": "^1.11", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Log\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Log package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/mail", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/mail.git", "reference": "91ac88078b481f4b8bde7403f8bcb406be70769e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/mail/zipball/91ac88078b481f4b8bde7403f8bcb406be70769e", "reference": "91ac88078b481f4b8bde7403f8bcb406be70769e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"erusev/parsedown": "^1.7", "illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "psr/log": "^1.0", "swiftmailer/swiftmailer": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.2.1"}, "suggest": {"aws/aws-sdk-php": "Required to use the SES mail driver (^3.0).", "guzzlehttp/guzzle": "Required to use the Mailgun and Mandrill mail drivers (^6.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Mail\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Mail package.", "homepage": "https://laravel.com", "time": "2019-02-22T15:28:19+00:00"}, {"name": "illuminate/pagination", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/pagination.git", "reference": "3955a9c41095da874d353a205f9079b7211f9238"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pagination/zipball/3955a9c41095da874d353a205f9079b7211f9238", "reference": "3955a9c41095da874d353a205f9079b7211f9238", "shasum": ""}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pagination\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pagination package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/pipeline", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "63a6e66bfab88c9a7dd4bbb077634fac3df4aa2a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/63a6e66bfab88c9a7dd4bbb077634fac3df4aa2a", "reference": "63a6e66bfab88c9a7dd4bbb077634fac3df4aa2a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "time": "2019-02-24T18:58:51+00:00"}, {"name": "illuminate/queue", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/queue.git", "reference": "44babb781fd61c665afc865be981dd7a3b494796"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/queue/zipball/44babb781fd61c665afc865be981dd7a3b494796", "reference": "44babb781fd61c665afc865be981dd7a3b494796", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/console": "5.7.*", "illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/database": "5.7.*", "illuminate/filesystem": "5.7.*", "illuminate/support": "5.7.*", "opis/closure": "^3.1", "php": "^7.1.3", "symfony/debug": "^4.1", "symfony/process": "^4.1"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver (^3.0).", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "illuminate/redis": "Required to use the Redis queue driver (5.7.*).", "pda/pheanstalk": "Required to use the Beanstalk queue driver (^3.0|^4.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Queue\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Queue package.", "homepage": "https://laravel.com", "time": "2019-02-22T10:35:45+00:00"}, {"name": "illuminate/redis", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/redis.git", "reference": "3c20e8395b6e698560977ae9afb5ef408c8f24d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/redis/zipball/3c20e8395b6e698560977ae9afb5ef408c8f24d3", "reference": "3c20e8395b6e698560977ae9afb5ef408c8f24d3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "predis/predis": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Redis\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Redis package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/session", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/session.git", "reference": "0d1233ea455b9ad50112212022ca3bcff874fa86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/session/zipball/0d1233ea455b9ad50112212022ca3bcff874fa86", "reference": "0d1233ea455b9ad50112212022ca3bcff874fa86", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.7.*", "illuminate/filesystem": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "symfony/finder": "^4.1", "symfony/http-foundation": "^4.1"}, "suggest": {"illuminate/console": "Required to use the session:table command (5.7.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Session\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Session package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/support", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "3e2810145f37eb89fa11759781ee88ee1c1a5262"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/3e2810145f37eb89fa11759781ee88ee1c1a5262", "reference": "3e2810145f37eb89fa11759781ee88ee1c1a5262", "shasum": ""}, "require": {"doctrine/inflector": "^1.1", "ext-mbstring": "*", "illuminate/contracts": "5.7.*", "nesbot/carbon": "^1.26.3", "php": "^7.1.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.7.*).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "ramsey/uuid": "Required to use Str::uuid() (^3.7).", "symfony/process": "Required to use the composer class (^4.1).", "symfony/var-dumper": "Required to use the dd function (^4.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "time": "2019-02-12T07:57:07+00:00"}, {"name": "illuminate/translation", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/translation.git", "reference": "4875559c0f32892c4070ca1185127c71fe18b8cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/translation/zipball/4875559c0f32892c4070ca1185127c71fe18b8cb", "reference": "4875559c0f32892c4070ca1185127c71fe18b8cb", "shasum": ""}, "require": {"illuminate/contracts": "5.7.*", "illuminate/filesystem": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Translation package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/validation", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/validation.git", "reference": "ee897c6708685294ebaa1db8407f30a1fe62f7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/validation/zipball/ee897c6708685294ebaa1db8407f30a1fe62f7f3", "reference": "ee897c6708685294ebaa1db8407f30a1fe62f7f3", "shasum": ""}, "require": {"illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/support": "5.7.*", "illuminate/translation": "5.7.*", "php": "^7.1.3", "symfony/http-foundation": "^4.1"}, "suggest": {"illuminate/database": "Required to use the database presence verifier (5.7.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Validation package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "illuminate/view", "version": "v5.7.28", "source": {"type": "git", "url": "https://github.com/illuminate/view.git", "reference": "e19e4e16ad309503d27845383fc533a889581739"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/view/zipball/e19e4e16ad309503d27845383fc533a889581739", "reference": "e19e4e16ad309503d27845383fc533a889581739", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/events": "5.7.*", "illuminate/filesystem": "5.7.*", "illuminate/support": "5.7.*", "php": "^7.1.3", "symfony/debug": "^4.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Illuminate\\View\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate View package.", "homepage": "https://laravel.com", "time": "2019-02-11T13:48:57+00:00"}, {"name": "jukylin/jaeger-php", "version": "v2.1.3", "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/jukylin/jaeger-php/jukylin-jaeger-php-f58a6f4a356902e7d70c0f4bad35862babcb33af-zip.tar", "shasum": "c04ffec0b748b1d895ba69ed78857732378e0bbf"}, "require": {"opentracing/opentracing": "1.0.0-beta5", "packaged/thrift": "0.10.0", "php": ">=5.6.0"}, "require-dev": {"php-coveralls/php-coveralls": "^1.0", "phpunit/phpunit": "^5"}, "type": "library", "autoload": {"files": ["src/Jaeger/Constants.php"], "psr-4": {"Jaeger\\": "src\\Jaeger"}}, "license": ["Apache-2.0"], "authors": [{"name": "juk<PERSON>in", "email": "<EMAIL>"}], "description": "php client for jaeger", "keywords": ["jaeger", "opentracing", "trace"], "time": "2020-05-28T01:17:38+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.10", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/justinrainbow/json-schema/justinrainbow-json-schema-2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b-zip-d13d66.tar", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "shasum": "8a184eb0c625bbeaa343eeb6ce267f33b9c6ed2b"}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2020-05-27T16:41:55+00:00"}, {"name": "khanamiryan/qrcode-detector-decoder", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/khanamiryan/php-qrcode-detector-decoder.git", "reference": "89b57f2d9939dd57394b83f6ccbd3e1a74659e34"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/khanamiryan/qrcode-detector-decoder/khanamiryan-qrcode-detector-decoder-89b57f2d9939dd57394b83f6ccbd3e1a74659e34-zip-7bca8a.tar", "reference": "89b57f2d9939dd57394b83f6ccbd3e1a74659e34", "shasum": "50d3dd7f55adcc9f2a827a8268dac7c2cda04077"}, "require": {"php": "^5.6|^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "type": "library", "autoload": {"psr-4": {"Zxing\\": "lib/"}, "files": ["lib/Common/customFunctions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "homepage": "https://github.com/khanamiryan", "role": "Developer"}], "description": "QR code decoder / reader", "homepage": "https://github.com/khanamiryan/php-qrcode-detector-decoder/", "keywords": ["barcode", "qr", "zxing"], "time": "2020-04-19T16:18:51+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/kylekatarnls/update-helper.git", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/kylekatarnls/update-helper/kylekatarnls-update-helper-429be50660ed8a196e0798e5939760f168ec8ce9-zip-3c2f10.tar", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": "848afe4d858e6f11d9def0d3aa897b9c38c67a43"}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-04-07T20:44:10+00:00"}, {"name": "laravel/lumen-framework", "version": "v5.7.8", "source": {"type": "git", "url": "https://github.com/laravel/lumen-framework.git", "reference": "6d60d23871caa57dd64a8d620f63ecde9a3294f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/lumen-framework/zipball/6d60d23871caa57dd64a8d620f63ecde9a3294f1", "reference": "6d60d23871caa57dd64a8d620f63ecde9a3294f1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"dragonmantank/cron-expression": "^2.0", "illuminate/auth": "5.7.*", "illuminate/broadcasting": "5.7.*", "illuminate/bus": "5.7.*", "illuminate/cache": "5.7.*", "illuminate/config": "5.7.*", "illuminate/container": "5.7.*", "illuminate/contracts": "5.7.*", "illuminate/database": "5.7.*", "illuminate/encryption": "5.7.*", "illuminate/events": "5.7.*", "illuminate/filesystem": "5.7.*", "illuminate/hashing": "5.7.*", "illuminate/http": "5.7.*", "illuminate/log": "5.7.*", "illuminate/pagination": "5.7.*", "illuminate/pipeline": "5.7.*", "illuminate/queue": "5.7.*", "illuminate/support": "5.7.*", "illuminate/translation": "5.7.*", "illuminate/validation": "5.7.*", "illuminate/view": "5.7.*", "nikic/fast-route": "^1.3", "php": "^7.1.3", "symfony/http-foundation": "^4.1", "symfony/http-kernel": "^4.1", "symfony/var-dumper": "^4.1"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0"}, "suggest": {"laravel/tinker": "Required to use the tinker console command (^1.0).", "vlucas/phpdotenv": "Required to use .env files (^2.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"psr-4": {"Laravel\\Lumen\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Lumen Framework.", "homepage": "https://lumen.laravel.com", "keywords": ["framework", "laravel", "lumen"], "time": "2019-03-05T10:00:20+00:00"}, {"name": "league/flysystem", "version": "1.0.69", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "7106f78428a344bc4f643c233a94e48795f10967"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/league/flysystem/league-flysystem-7106f78428a344bc4f643c233a94e48795f10967-zip-e1724b.tar", "reference": "7106f78428a344bc4f643c233a94e48795f10967", "shasum": "7bd5928896d40ac69a2dfc92c7c4678cc80a4f58"}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.26"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2020-05-18T15:13:39+00:00"}, {"name": "library/monitor", "version": "1.36", "source": {"type": "git", "url": "https://yuanzhi:<EMAIL>/hyr/monitorSdk.git", "reference": "70ec5a9893c7c3b41af99350b9801ff1f68f2a9f"}, "dist": {"type": "tar", "url": "https://packagelist.chinawayltd.com/dist/library/monitor/library-monitor-1.36-6faa7d.tar", "reference": "70ec5a9893c7c3b41af99350b9801ff1f68f2a9f", "shasum": "9e4cd0736a6b7e52d9cb9bfca9a2f78a60981b56"}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "6.*", "php": ">=5.5"}, "type": "library", "autoload": {"psr-4": {"Library\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "yuanzhi", "email": "<EMAIL>"}], "description": "monitor for php client,send dingding warning msg", "time": "2020-04-10T02:19:49+00:00"}, {"name": "markbaker/complex", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/markbaker/complex/markbaker-complex-8eaa40cceec7bf0518187530b2e63871be661b72-zip-8bff0c.tar", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72", "shasum": "e723d7c3b8608ee2a4c85ca92e0aa79bb98ccb46"}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-03-11T20:15:49+00:00"}, {"name": "markbaker/matrix", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/markbaker/matrix/markbaker-matrix-5348c5a67e3b75cd209d70103f916a93b1f1ed21-zip-566d51.tar", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21", "shasum": "152434be0116d806f0438f0b2878bc6130355cd3"}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2019-10-06T11:29:25+00:00"}, {"name": "mockery/mockery", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/mockery/mockery/mockery-mockery-f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be-zip-47a5f9.tar", "reference": "f69bbde7d7a75d6b2862d9ca8fab1cd28014b4be", "shasum": "4cf89a7f02560811ab07f70183623cb12d9a7788"}, "require": {"hamcrest/hamcrest-php": "~2.0", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "~5.7.10|~6.5|~7.0|~8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "time": "2019-12-26T09:49:15+00:00"}, {"name": "monolog/monolog", "version": "1.25.4", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "3022efff205e2448b560c833c6fbbf91c3139168"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/monolog/monolog/monolog-monolog-3022efff205e2448b560c833c6fbbf91c3139168-zip-daa82d.tar", "reference": "3022efff205e2448b560c833c6fbbf91c3139168", "shasum": "56899a6d2dcdce6766231441e41804a02398b568"}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2020-05-22T07:31:27+00:00"}, {"name": "myclabs/deep-copy", "version": "1.9.5", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/myclabs/deep-copy/myclabs-deep-copy-b2c28789e80a97badd14145fda39b545d83ca3ef-zip-d9415c.tar", "reference": "b2c28789e80a97badd14145fda39b545d83ca3ef", "shasum": "307778f3b1ce88d8ccbff3a7356889a8956165c4"}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2020-01-17T21:11:47+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.6", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "5f36467c7a87e20fbdc51e524fd8f9d1de80187c"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/myclabs/php-enum/myclabs-php-enum-5f36467c7a87e20fbdc51e524fd8f9d1de80187c-zip-d86d43.tar", "reference": "5f36467c7a87e20fbdc51e524fd8f9d1de80187c", "shasum": "692f68129c1107081bd6990f2d011ea6f9d1542c"}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "time": "2020-02-14T08:15:52+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/nesbot/carbon/nesbot-carbon-4be0c005164249208ce1b5ca633cd57bdd42ff33-zip-2c53ee.tar", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": "323e48d96d80789a7416690ff3ba4a673d284a6f"}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2019-10-14T05:51:36+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"psr-4": {"FastRoute\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "time": "2018-02-13T20:26:39+00:00"}, {"name": "opentracing/opentracing", "version": "1.0.0-beta5", "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/opentracing/opentracing/opentracing-opentracing-19591d4084e32eaea061eebd9448b62e5ee3ec19-zip.tar", "shasum": "be6bbfb08f4b6b4c17771331e38632dbad3bccb7"}, "require": {"php": "^5.6||^7.0"}, "require-dev": {"phpunit/phpunit": "~5.7.19", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"files": ["./src/OpenTracing/Tags.php", "./src/OpenTracing/Formats.php"], "psr-4": {"OpenTracing\\": "./src/OpenTracing/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "time": "2018-04-13T13:38:46+00:00"}, {"name": "opis/closure", "version": "3.5.3", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "cac47092144043d5d676e2e7cf8d0d2f83fc89ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/cac47092144043d5d676e2e7cf8d0d2f83fc89ca", "reference": "cac47092144043d5d676e2e7cf8d0d2f83fc89ca", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2020-05-25T09:32:45+00:00"}, {"name": "packaged/thrift", "version": "0.10.0", "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/packaged/thrift/packaged-thrift-8af3f4c0388319f65a2522844d80d8ded60d211b-zip.tar", "shasum": "181575ca80e83d5b178d06d4291ddf3541f3e661"}, "require": {"php": ">=5.0.0"}, "type": "library", "autoload": {"psr-0": {"Thrift\\": "src/"}}, "license": ["Apache-2.0"], "description": "Apache Thrift", "homepage": "http://thrift.apache.org/", "keywords": ["apache", "thrift"], "time": "2017-06-05T07:52:51+00:00"}, {"name": "pburggraf/crc", "version": "v0.6.1", "source": {"type": "git", "url": "https://github.com/pburggraf/CRC.git", "reference": "62365ada36bf177c658383e31bad636f5fe0bca3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pburggraf/CRC/zipball/62365ada36bf177c658383e31bad636f5fe0bca3", "reference": "62365ada36bf177c658383e31bad636f5fe0bca3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "require-dev": {"phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12.1", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"PBurggraf\\CRC\\": "src/PBurggraf/CRC/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cyclic redundancy checker (crc) class to calculate crc8, crc16, crc24 and crc32 checksum values", "keywords": ["checksum", "checksum generator", "crc", "crc16", "crc24", "crc32", "crc8", "cyclic redundancy checker", "validation"], "time": "2019-12-10T16:16:53+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "php-ds/php-ds", "version": "v1.4.1", "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/php-ds/php-ds/php-ds-php-ds-43d2df301a9e2017f67b8c11d94a5222f9c00fd1-zip.tar", "shasum": "ce297fb55a9060ae14627a1b8ae5fa2bac5d6f99"}, "require": {"ext-json": "*", "php": ">=7.0"}, "provide": {"ext-ds": "1.3.0"}, "require-dev": {"php-ds/tests": "^1.3"}, "suggest": {"ext-ds": "to improve performance and reduce memory usage"}, "type": "library", "autoload": {"psr-4": {"Ds\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["data structures", "ds", "php", "polyfill"], "time": "2022-03-09T20:39:30+00:00"}, {"name": "php-spec/php-tracing", "version": "0.2", "source": {"type": "git", "url": "***********************:php-spec/php-tracing.git", "reference": "92fc303b429e0fd907e4f00b33aaf3f83d2ba608"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/php-spec/php-tracing/php-spec-php-tracing-0.2-5d69eb.tar", "reference": "92fc303b429e0fd907e4f00b33aaf3f83d2ba608", "shasum": "084fa313c491f4492232d034ad4f8713a098075d"}, "require": {"jukylin/jaeger-php": "^2.1.3", "php": ">=7.0", "php-ds/php-ds": "~1.4"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"G7\\Tracing\\": "src"}}, "autoload-dev": {"psr-4": {"G7\\Tracing\\Tests\\": "tests"}}, "license": ["private"], "description": "Tracing Adapter API for PHP", "time": "2023-01-30T10:49:52+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpdocumentor/reflection-common/phpdocumentor-reflection-common-6568f4687e5b41b054365f9ae03fcb1ed5f2069b-zip-2c60a0.tar", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b", "shasum": "0d014b7a17217bb529ae582a5cfcc5d39efe2379"}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-04-27T09:25:28+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpdocumentor/reflection-docblock/phpdocumentor-reflection-docblock-cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e-zip-f79ac8.tar", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "shasum": "bf1933aeb720839987509699d93f60ea323df4f1"}, "require": {"ext-filter": "^7.1", "php": "^7.2", "phpdocumentor/reflection-common": "^2.0", "phpdocumentor/type-resolver": "^1.0", "webmozart/assert": "^1"}, "require-dev": {"doctrine/instantiator": "^1", "mockery/mockery": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2020-02-22T12:28:44+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpdocumentor/type-resolver/phpdocumentor-type-resolver-7462d5f123dfc080dfdf26897032a6513644fc95-zip-2792c9.tar", "reference": "7462d5f123dfc080dfdf26897032a6513644fc95", "shasum": "27c085891230ded767dba18e8b3dc08b46fe957a"}, "require": {"php": "^7.2", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "^7.2", "mockery/mockery": "~1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2020-02-18T18:59:58+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpoffice/phpspreadsheet/phpoffice-phpspreadsheet-f79611d6dc1f6b7e8e30b738fc371b392001dbfd-zip-62ca0b.tar", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": "3ac1a6e5ccfb40ea964fdde67fc1958ec6c92f4d"}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-04-27T08:12:48+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpspec/prophecy/phpspec-prophecy-451c3cd1418cf640de218914901e51b064abb093-zip-e9b346.tar", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": "434c0725edb6115ee570cf7dc28fc50c1068851d"}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpunit/php-token-stream/phpunit-php-token-stream-995192df77f63a59e47f025390d2d1fdf8f425ff-zip-c295d3.tar", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": "bdfd7299dfc2a6326ae4afda4040f836bf81ba92"}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/phpunit/phpunit/phpunit-phpunit-9467db479d1b0487c99733bb1e7944d32deded2c-zip-024fb8.tar", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": "4636eab81d4498fc2563e02496d4fa5314a05f34"}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2020-01-08T08:45:45+00:00"}, {"name": "predis/predis", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/nrk/predis.git", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nrk/predis/zipball/f0210e38881631afeafb56ab43405a92cafd9fd1", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/nrk/predis", "keywords": ["nosql", "predis", "redis"], "time": "2016-06-16T16:22:20+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/psr/log/psr-log-0f73288fd15629204f9d42b7055f72dacbe811fc-zip-eb4a75.tar", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": "08cad087318b1195ced762254d5c7a1efdd9aabf"}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/sebastian/environment/sebastian-environment-464c90d7bdf5ad4e8a6aea15c091fec0603d4368-zip-90efac.tar", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": "d40f509bb14963637940462e2c52987c342338ef"}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/sebastian/exporter/sebastian-exporter-68609e1261d215ea5b21b7987539cbfbe156ec3e-zip-9c68c0.tar", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": "a22c91604bdba31712594538440fd200075d95ce"}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": ""}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/seld/jsonlint/seld-jsonlint-ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1-zip-62283b.tar", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1", "shasum": "56f8ce397f4a8aa800cb54d124d309f3db9f6c90"}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2020-04-30T19:05:18+00:00"}, {"name": "seld/phar-utils", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "8800503d56b9867d43d9c303b9cbcc26016e82f0"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/seld/phar-utils/seld-phar-utils-8800503d56b9867d43d9c303b9cbcc26016e82f0-zip-e17173.tar", "reference": "8800503d56b9867d43d9c303b9cbcc26016e82f0", "shasum": "c33364a882d520ebc61aeb087de116550c83fea7"}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2020-02-14T15:25:33+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.3", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2019-11-12T09:31:26+00:00"}, {"name": "symfony/console", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/console/symfony-console-10bb3ee3c97308869d53b3e3d03f6ac23ff985f7-zip-44d48c.tar", "reference": "10bb3ee3c97308869d53b3e3d03f6ac23ff985f7", "shasum": "1a86f1e2d54b55bf5452d5d299d7b4bb62e15cd3"}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-30T11:41:10+00:00"}, {"name": "symfony/css-selector", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "5f8d5271303dad260692ba73dfa21777d38e124e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/5f8d5271303dad260692ba73dfa21777d38e124e", "reference": "5f8d5271303dad260692ba73dfa21777d38e124e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-27T16:56:45+00:00"}, {"name": "symfony/debug", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "346636d2cae417992ecfd761979b2ab98b339a45"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/debug/symfony-debug-346636d2cae417992ecfd761979b2ab98b339a45-zip-96d16b.tar", "reference": "346636d2cae417992ecfd761979b2ab98b339a45", "shasum": "47be6127d975e800f68769ca6b15507a9196304e"}, "require": {"php": "^7.1.3", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/deprecation-contracts/symfony-deprecation-contracts-dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337-zip-4423d6.tar", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337", "shasum": "4a60d31bafa10e0fed61cdf24e70166a81cfaa78"}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-27T08:34:37+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "7e9828fc98aa1cf27b422fe478a84f5b0abb7358"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/7e9828fc98aa1cf27b422fe478a84f5b0abb7358", "reference": "7e9828fc98aa1cf27b422fe478a84f5b0abb7358", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ErrorHandler Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-30T14:07:33+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/abc8e3618bfdb55e44c8c6a00abd333f831bbfed", "reference": "abc8e3618bfdb55e44c8c6a00abd333f831bbfed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.7", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "reference": "c43ab685673fb6c8d84220c77897b1d6cdbe1d18", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2019-09-17T09:54:03+00:00"}, {"name": "symfony/filesystem", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "7cd0dafc4353a0f62e307df90b48466379c8cc91"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/filesystem/symfony-filesystem-7cd0dafc4353a0f62e307df90b48466379c8cc91-zip-efee08.tar", "reference": "7cd0dafc4353a0f62e307df90b48466379c8cc91", "shasum": "bcdd8fef52083ea489dbeb986eb05748f6a0fcf8"}, "require": {"php": "^7.2.5", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-04-12T14:40:17+00:00"}, {"name": "symfony/finder", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "5729f943f9854c5781984ed4907bbb817735776b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/finder/symfony-finder-5729f943f9854c5781984ed4907bbb817735776b-zip-aa4407.tar", "reference": "5729f943f9854c5781984ed4907bbb817735776b", "shasum": "6548a7e8146c1a6c200918773a9b6e901076957c"}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-03-27T16:54:36+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/http-foundation/symfony-http-foundation-ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2-zip-765d2b.tar", "reference": "ec5bd254c223786f5fa2bb49a1e705c1b8e7cee2", "shasum": "97adcde732de615888d16472504a80d7912ef250"}, "require": {"php": "^7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-04-18T20:40:08+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "1799a6c01f0db5851f399151abdb5d6393fec277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/1799a6c01f0db5851f399151abdb5d6393fec277", "reference": "1799a6c01f0db5851f399151abdb5d6393fec277", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "psr/log": "~1.0", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-04-28T18:47:42+00:00"}, {"name": "symfony/inflector", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "fddb4262dd136b34db993a2a3488713df91e4856"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/inflector/symfony-inflector-fddb4262dd136b34db993a2a3488713df91e4856-zip-e05408.tar", "reference": "fddb4262dd136b34db993a2a3488713df91e4856", "shasum": "e3d29f3c6109262b0cecc2973a209a193469f8c8"}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/string": "^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/mime", "version": "v5.0.8", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/mime/symfony-mime-5d6c81c39225a750f3f43bee15f03093fb9aaa0b-zip-f4c18b.tar", "reference": "5d6c81c39225a750f3f43bee15f03093fb9aaa0b", "shasum": "f428f12d2f77088d2e467e76ebcce4dd888362d2"}, "require": {"php": "^7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2020-04-17T03:29:44+00:00"}, {"name": "symfony/options-resolver", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "663f5dd5e14057d1954fe721f9709d35837f2447"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/options-resolver/symfony-options-resolver-663f5dd5e14057d1954fe721f9709d35837f2447-zip-5ed373.tar", "reference": "663f5dd5e14057d1954fe721f9709d35837f2447", "shasum": "b99eb394eff62584ef45c6de3053bf6f5ab36090"}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-23T13:08:13+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-ctype/symfony-polyfill-ctype-e94c8b1bbe2bc77507a1056cdb06451c75b427f9-zip-67a655.tar", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "shasum": "52acbbd00cd907bda57d34a5d5c9410e01ad655d"}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c4de7601eefbf25f9d47190abe07f79fe0a27424", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-intl-grapheme/symfony-polyfill-intl-grapheme-e094b0770f7833fdf257e6ba4775be4e258230b2-zip-ead258.tar", "reference": "e094b0770f7833fdf257e6ba4775be4e258230b2", "shasum": "2ddb69d6eb2610b36b41c6b8047ce655ccb6a44d"}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-intl-idn/symfony-polyfill-intl-idn-3bff59ea7047e925be6b7f2059d60af31bb46d6a-zip-e0add8.tar", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a", "shasum": "9eeb21f3701c608ba00ccfeea784bda6dc4de253"}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-intl-normalizer/symfony-polyfill-intl-normalizer-1357b1d168eb7f68ad6a134838e46b0b159444a9-zip-c84c45.tar", "reference": "1357b1d168eb7f68ad6a134838e46b0b159444a9", "shasum": "631554942e68dd12ed0a378b5f67f594d0b64a13"}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-mbstring/symfony-polyfill-mbstring-fa79b11539418b02fc5e1897267673ba2c19419c-zip-6896b6.tar", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c", "shasum": "4c23a484bdb4d4bcd2e277890e02e22fb23ee0d1"}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "f048e612a3905f34931127360bdd2def19a5e582"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-php72/symfony-polyfill-php72-f048e612a3905f34931127360bdd2def19a5e582-zip-315def.tar", "reference": "f048e612a3905f34931127360bdd2def19a5e582", "shasum": "f77b56a38ee97f6336ae9a3ffd3fb2b1469facfe"}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-php73/symfony-polyfill-php73-a760d8964ff79ab9bf057613a5808284ec852ccc-zip-3cf1b5.tar", "reference": "a760d8964ff79ab9bf057613a5808284ec852ccc", "shasum": "e9d998dacfb9c962688f008b1670d448c6b292c6"}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "5e30b2799bc1ad68f7feb62b60a73743589438dd"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/polyfill-php80/symfony-polyfill-php80-5e30b2799bc1ad68f7feb62b60a73743589438dd-zip-312018.tar", "reference": "5e30b2799bc1ad68f7feb62b60a73743589438dd", "shasum": "2254dbc962e4b6f7ff28d23da14c1b83848776e8"}, "require": {"php": ">=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/process", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "4b6a9a4013baa65d409153cbb5a895bf093dc7f4"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/process/symfony-process-4b6a9a4013baa65d409153cbb5a895bf093dc7f4-zip-219639.tar", "reference": "4b6a9a4013baa65d409153cbb5a895bf093dc7f4", "shasum": "4750a4d80a5d7afd6bcd2526cf438e0242333537"}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-04-15T15:56:18+00:00"}, {"name": "symfony/property-access", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "776bf85b9ed5a9ba99496d5e5457a50de63cd997"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/property-access/symfony-property-access-776bf85b9ed5a9ba99496d5e5457a50de63cd997-zip-0af8a8.tar", "reference": "776bf85b9ed5a9ba99496d5e5457a50de63cd997", "shasum": "9e4b4d502b3028866f76944a35223ee6727912f3"}, "require": {"php": ">=7.2.5", "symfony/inflector": "^4.4|^5.0", "symfony/polyfill-php80": "^1.15", "symfony/property-info": "^5.1"}, "require-dev": {"symfony/cache": "^4.4|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-30T21:17:32+00:00"}, {"name": "symfony/property-info", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "b644923132bdd92c2664f0c7955f77a9a2b8e919"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/property-info/symfony-property-info-b644923132bdd92c2664f0c7955f77a9a2b8e919-zip-d7473b.tar", "reference": "b644923132bdd92c2664f0c7955f77a9a2b8e919", "shasum": "8487f0e77ce7bb378d49d1b0e4362bc61229ed66"}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15", "symfony/string": "^5.1"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "~1.7", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/service-contracts", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "66a8f0957a3ca54e4f724e49028ab19d75a8918b"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/service-contracts/symfony-service-contracts-66a8f0957a3ca54e4f724e49028ab19d75a8918b-zip-81ebee.tar", "reference": "66a8f0957a3ca54e4f724e49028ab19d75a8918b", "shasum": "aaea32952db76bbda3b049308a0e26c09b719e1a"}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/string", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "90c2a5103f07feb19069379f3abdcdbacc7753a9"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/string/symfony-string-90c2a5103f07feb19069379f3abdcdbacc7753a9-zip-64a51b.tar", "reference": "90c2a5103f07feb19069379f3abdcdbacc7753a9", "shasum": "32634e47e7ac9c83e31bdaf988a4ab4f26ebdcce"}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\String\\": ""}, "files": ["Resources/functions.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony String component", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/translation", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/translation/symfony-translation-8272bbd2b7e220ef812eba2a2b30068a5c64b191-zip-230d73.tar", "reference": "8272bbd2b7e220ef812eba2a2b30068a5c64b191", "shasum": "0d84e4898c056a2ee780c79a337dda912081f39d"}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-04-12T16:45:36+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e5ca07c8f817f865f618aa072c2fe8e0e637340e"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/symfony/translation-contracts/symfony-translation-contracts-e5ca07c8f817f865f618aa072c2fe8e0e637340e-zip-b1e280.tar", "reference": "e5ca07c8f817f865f618aa072c2fe8e0e637340e", "shasum": "a9db98061ff924999bac1a8ae625d0f13914e0ed"}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.8", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "c587e04ce5d1aa62d534a038f574d9a709e814cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/c587e04ce5d1aa62d534a038f574d9a709e814cf", "reference": "c587e04ce5d1aa62d534a038f574d9a709e814cf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-04-12T16:14:02+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-06-13T22:48:21+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.2", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "dda2ee426acd6d801d5b7fd1001cde9b5f790e15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/dda2ee426acd6d801d5b7fd1001cde9b5f790e15", "reference": "dda2ee426acd6d801d5b7fd1001cde9b5f790e15", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "time": "2019-10-24T08:53:34+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "67d472b1794c986381a8950e4958e1adb779d561"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/67d472b1794c986381a8950e4958e1adb779d561", "reference": "67d472b1794c986381a8950e4958e1adb779d561", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.0"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2020-05-02T13:38:00+00:00"}, {"name": "webmozart/assert", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6"}, "dist": {"type": "tar", "url": "http://satis.ews.chinawayltd.com//dist/webmozart/assert/webmozart-assert-ab2cb0b3b559010b75981b1bdce728da3ee90ad6-zip-5a8a4f.tar", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "shasum": "c310982f35216453ac3850d1719a4d5f7879ed18"}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-04-18T12:12:48+00:00"}, {"name": "wn/lumen-generators", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/webNeat/lumen-generators.git", "reference": "dafa7f2f220a62e113f5f28c348b1f42bed74f48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webNeat/lumen-generators/zipball/dafa7f2f220a62e113f5f28c348b1f42bed74f48", "reference": "dafa7f2f220a62e113f5f28c348b1f42bed74f48", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"fzaninotto/faker": "^1.5", "illuminate/console": "^5.1", "illuminate/filesystem": "^5.1", "php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"Wn\\Generators\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> ha<PERSON>", "email": "<EMAIL>"}], "description": "A collection of generators for Lumen and Laravel 5.", "keywords": ["api", "generators", "laravel", "lumen", "rest"], "time": "2017-05-08T00:22:45+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=7.2", "ext-bcmath": "*", "ext-curl": "*", "ext-json": "*", "ext-mcrypt": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-simplexml": "*", "ext-soap": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}